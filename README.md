# GejieSoft PDF Tools - 智能PPT信息提取与生成工具

[![Go Version](https://img.shields.io/badge/Go-1.24+-blue.svg)](https://golang.org/)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)
[![License](https://img.shields.io/badge/License-Proprietary-red.svg)](LICENSE)

一个智能的PPT信息提取与**生成**工具，支持文本提取、图片自动提取、人像识别与抠图、照片矫正优化、**以及根据数据生成定制化PPT幻灯片**等功能。



##  windows 下使用方法


1、构建主程序在根目录执行  ./main/build.bat
2、运行主程序在根目录执行  ./gejiesoft-pdf-tools.exe
3、合并 .\main\build.bat ; .\gejiesoft-pdf-tools.exe       
4、创建配置文件 main\config.json

主程序用于PPT的预置母板：
存放路径：/templates-ppt/ppt-teample1.pptx  ppt-teample2.pptx ppt-teample3.pptx 依次类推


### 当前版本功能状态

| 功能模块 | 状态 | 说明 |
|---------|------|------|
| **智能文本提取** | ✅ 已启用 | 支持PPT文本内容提取 |
| **图片自动提取** | ✅ 已启用 | 自动识别并提取PPT图片 |
| **人像识别与抠图** | ✅ 已启用 | 智能人像检测和背景去除 |
| **照片矫正优化** | ✅ 已启用 | 详见下方详细说明 |
| **Web界面操作** | ✅ 已启用 | 友好的Web操作界面 |
| **PPT生成** | ✅ 已启用 | 基于提取数据生成定制化PPT |

### 照片矫正优化功能详情

#### ✅ 已启用功能
- **人像抠图**：使用腾讯云人体分析API，支持背景去除，生成透明背景的人像图片

#### 🔄 预留功能（待后期集成）
- **歪斜矫正**：检测图片倾斜角度并自动矫正
- **人脸定位**：基于人脸位置重新定位图片，实现人脸居中
- **质量优化**：图片清晰度、亮度、对比度自动优化
- **尺寸调整**：支持标准尺寸和PPT专用尺寸调整

> **说明**：预留功能的代码已完整实现，但暂未在主流程中启用，可在后期开发中根据需要集成。

## ✨ 功能特性

### 🎯 核心功能
- **🔍 智能文本提取**：基于pptx库深度解析PPT文档结构，提取完整文本内容
- **🖼️ 图片自动提取**：智能识别并批量提取PPT中的嵌入图片，支持多种图片格式
- **👤 人像识别与抠图**：集成腾讯云IAI API，实现高精度人像检测和背景分割
- **✨ 照片智能优化**：多层次图片处理管道
  - ✅ **人像抠图**：已启用，生成透明背景PNG
  - ✅ **图像超分**：腾讯云AI图像超分，提升低分辨率图片质量
  - ✅ **智能压缩**：自适应图片大小优化，满足API调用要求
  - 🔄 **歪斜矫正**：代码已实现，支持图片倾斜角度检测和矫正
  - 🔄 **人脸定位**：代码已实现，支持人脸居中和智能裁剪
  - 🔄 **质量增强**：代码已实现，支持亮度、对比度、清晰度优化
- **🤖 AI信息提取**：集成自定义Aibot API，智能解析简历信息并结构化输出
- **📊 PPT智能生成**：基于Python-pptx的模板引擎，支持动态内容填充和布局优化
- **🌐 现代Web界面**：响应式设计，支持拖拽上传、实时进度监控、结果预览

### 🔧 技术特性
- **🏗️ 微服务架构**：Go后端 + Python PPT生成 + 前端分离的模块化设计
- **🐳 容器化部署**：完整的Docker支持，一键部署和扩展
- **⚙️ 灵活配置管理**：环境变量优先，配置文件备用的双重配置策略
- **🔗 外部API集成**：
  - 腾讯云COS：图片存储和CDN加速
  - 腾讯云IAI：人脸检测、人像抠图、图像超分
  - Aibot API：自然语言处理和信息提取
- **📝 实时日志系统**：Server-Sent Events实现的实时日志流，支持多客户端同步
- **🔒 安全设计**：
  - 敏感信息环境变量管理
  - 文件路径安全验证
  - API密钥加密存储
- **📊 处理统计**：详细的处理过程统计和性能监控
- **🔄 错误恢复**：完善的错误处理和重试机制

## 🚀 快速开始

### 环境要求
- **Go 1.24+** (本地运行)
- **Python 3.8+** (PPT生成功能)
- **Docker & Docker Compose** (容器化部署)
- **Windows/Linux/macOS** 支持
> **注意**：本项目主要基于Windows环境进行开发和测试，因此在Windows环境下具有最佳兼容性。

### 方式一：Docker部署（推荐）

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd gejiesoft-pdf-tools
   ```

2. **配置环境变量**
   ```bash
   # 复制环境变量模板
   cp env.example .env
   
   # 编辑.env文件，填入您的API密钥
   ```

3. **启动服务**
   ```bash
   docker-compose up --build -d
   ```

4. **访问Web界面**
   ```
   http://localhost:8088
   ```

### 方式二：Linux 源码部署

#### 2.1 一键自动化部署（推荐）

对于Linux用户，我们提供了自动化部署脚本，可以快速完成环境配置和应用部署：

```bash
# 进入项目主目录
cd gejiesoft-pdf-tools

# 运行自动化部署脚本
chmod +x deploy_linux.sh
./deploy_linux.sh
```

**脚本功能：**
- 自动检测Linux发行版（Ubuntu/Debian/CentOS/RHEL/Fedora）
- 自动安装所需依赖（Go、Python、系统工具）
- 配置Go模块代理以加速依赖下载
- 创建Python虚拟环境并安装依赖
- 构建应用程序
- 配置环境变量
- 可选创建systemd服务以实现开机自启
- 配置防火墙规则
- 启动应用程序

**使用须知：**
- 脚本需要sudo权限来安装系统依赖
- 会自动设置Go代理为国内镜像源
- 支持交互式配置，用户可选择是否创建服务等

#### 2.2 手动部署（高级用户）

如果您希望手动控制部署过程，可以按照以下步骤操作：

##### 2.2.1 环境准备

1. **安装Go语言环境**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install -y golang-go
   
   # CentOS/RHEL/Fedora
   sudo yum install -y golang
   # 或者 (较新版本)
   sudo dnf install -y golang
   
   # 验证安装
   go version
   ```

2. **安装Python环境**
   ```bash
   # Ubuntu/Debian
   sudo apt install -y python3 python3-pip python3-venv
   
   # CentOS/RHEL/Fedora
   sudo yum install -y python3 python3-pip
   # 或者 (较新版本)
   sudo dnf install -y python3 python3-pip
   
   # 验证安装
   python3 --version
   pip3 --version
   ```

3. **安装其他依赖**
   ```bash
   # Ubuntu/Debian
   sudo apt install -y git build-essential
   
   # CentOS/RHEL/Fedora
   sudo yum groupinstall -y "Development Tools"
   sudo yum install -y git
   ```

##### 2.2.2 项目部署

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd gejiesoft-pdf-tools
   ```

2. **配置Go模块代理（可选，提高下载速度）**
   ```bash
   # 设置Go模块代理
   export GOPROXY=https://goproxy.cn,direct
   export GO111MODULE=on
   
   # 或者使用阿里云代理
   export GOPROXY=https://mirrors.aliyun.com/goproxy/,direct
   ```

3. **安装Go依赖**
   ```bash
   go mod tidy
   ```

4. **安装Python依赖**
   ```bash
   # 创建Python虚拟环境
   python3 -m venv venv
   source venv/bin/activate
   
   # 安装Python依赖
   pip3 install -r main/ppt_generator/requirements.txt
   ```

5. **配置环境变量**
   ```bash
   # 复制环境变量模板
   cp env.example .env
   
   # 编辑环境变量文件
   nano .env
   # 或者使用vim
   vim .env
   
   # 设置必要的环境变量
   export AIBOT_API_KEY=your_api_key
   export TENCENT_SECRET_ID=your_secret_id  
   export TENCENT_SECRET_KEY=your_secret_key
   export TENCENT_BUCKET=your_bucket_name
   ```

6. **构建并运行应用**
   ```bash
   # 进入主程序目录
   cd main
   
   # 构建应用
   CGO_ENABLED=0 go build -o ../gejiesoft-pdf-tools
   
   # 返回根目录
   cd ..
   
   # 运行应用
   ./gejiesoft-pdf-tools
   ```

7. **设置为系统服务（可选）**
   ```bash
   # 创建系统服务文件
   sudo nano /etc/systemd/system/gejiesoft-pdf-tools.service
   ```
   
   服务文件内容：
   ```ini
   [Unit]
   Description=GejieSoft PDF Tools
   After=network.target
   
   [Service]
   Type=simple
   User=your_username
   WorkingDirectory=/path/to/gejiesoft-pdf-tools
   ExecStart=/path/to/gejiesoft-pdf-tools/gejiesoft-pdf-tools
   Restart=always
   RestartSec=10
   Environment=AIBOT_API_KEY=your_api_key
   Environment=TENCENT_SECRET_ID=your_secret_id
   Environment=TENCENT_SECRET_KEY=your_secret_key
   Environment=TENCENT_BUCKET=your_bucket_name
   
   [Install]
   WantedBy=multi-user.target
   ```
   
   启用并启动服务：
   ```bash
   sudo systemctl enable gejiesoft-pdf-tools
   sudo systemctl start gejiesoft-pdf-tools
   sudo systemctl status gejiesoft-pdf-tools
   ```

#### 2.3 项目依赖详情

**Go依赖包：**
- `github.com/gin-gonic/gin` - Web框架
- `github.com/gin-contrib/cors` - 跨域请求支持
- `github.com/moipa-cn/pptx` - PPT文件解析
- `github.com/tencentcloud/tencentcloud-sdk-go` - 腾讯云SDK
- `github.com/tencentyun/cos-go-sdk-v5` - 腾讯云COS SDK

**Python依赖包：**
- `python-pptx==0.6.23` - PPT文件生成和编辑

**系统依赖：**
- Linux内核版本 >= 3.10
- 至少1GB可用内存
- 至少500MB可用磁盘空间

### 方式三：Windows 本地运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd gejiesoft-pdf-tools
   ```

2. **配置环境变量**
   ```bash
   # 方式1：使用环境变量
   set AIBOT_API_KEY=your_api_key
   set TENCENT_SECRET_ID=your_secret_id
   set TENCENT_SECRET_KEY=your_secret_key
   
   # 方式2：使用配置文件
   cp env.example .env
   # 编辑.env文件
   ```
   > **注意**：在Windows PowerShell中，不支持`&&`语法，请分别执行每条命令。为了更快的依赖安装，您可以考虑配置Go模块代理，例如使用[清华大学开源软件镜像站](https://mirrors.tuna.tsinghua.edu.cn/help/golang/)。

3. 运行 `go mod tidy` 安装依赖
   > **注意**：在处理此项目时，`go.mod` 文件似乎会意外还原。每次添加新依赖后，必须手动验证 `go.mod`，并再次运行 `go mod tidy` 以确保依赖已正确同步。
4. 使用 `go run main.go` 启动开发服务器
   ```bash
   
cd main
set CGO_ENABLED=0
go build -o ../gejiesoft-pdf-tools.exe

   ```

5. **访问Web界面**
   ```
   http://localhost:8088
   ```

## 📁 项目结构

```
gejiesoft-pdf-tools/
├── main/                    # Go后端核心代码
│   ├── main.go             # 🚀 程序入口，启动Web服务器
│   ├── web_server.go       # 🌐 Web服务器核心，处理HTTP请求、文件上传、实时日志流
│   ├── image_processor.go  # 🎨 图片处理引擎，人像检测、图片分类、智能处理统计
│   ├── photo_enhancer.go   # ✨ 照片增强器，人像抠图、图像超分、质量优化
│   ├── post_aibot_api.go   # 🤖 AI API客户端，调用Aibot进行智能文本解析
│   ├── config.go           # ⚙️ 配置管理器，环境变量和配置文件统一管理
│   ├── types.go            # 📊 数据结构定义，ResumeInfo、ProcessingStats等核心类型
│   ├── template_analyzer.go # 📋 模板分析器，PPTX模板解析和占位符识别
│   ├── ppt_generator/      # 🐍 Python PPT生成模块
│   │   ├── ppt_generator.py # 核心Python脚本，基于模板和数据生成定制PPT
│   │   ├── requirements.txt # Python依赖清单
│   │   └── sources/        # 测试资源文件
│   ├── templates/          # 模板映射配置
│   │   └── template_mapping.json # 模板占位符映射配置
│   ├── build.bat           # Windows构建脚本
│   ├── go.mod              # Go模块依赖定义
│   └── go.sum              # Go依赖校验文件
├── frontend/               # 🎨 前端用户界面
│   ├── index.html          # 主页面，拖拽上传、实时日志、结果展示
│   ├── script.js           # 前端逻辑，文件上传、进度监控、数据展示
│   ├── tailwind.min.css    # Tailwind CSS框架
│   ├── font-awesome.min.css # FontAwesome图标库
│   └── webfonts/           # Web字体资源
├── templates-ppt/          # 📋 PPT模板库
│   ├── ppt-teample1.pptx   # 默认PPT模板
│   ├── ppt-teample2.pptx   # 备用模板2
│   └── ppt-teample3.pptx   # 备用模板3
├── templates-vc/           # 📄 示例简历PPT文件
├── output/                 # 📁 输出目录
│   └── VCOUTPUTDATA*/      # 按时间戳命名的处理结果目录
├── temp/                   # 🗂️ 临时文件目录
├── logs/                   # 📝 日志文件目录
├── docs/                   # 📚 项目文档
│   ├── 0.需求说明.md        # 需求文档
│   ├── 4.项目结构说明v1.1.md # 详细结构说明
│   ├── API.md              # API接口文档
│   └── ...                 # 其他技术文档
├── scripts/                # 🔧 脚本工具
│   └── run_tests.sh        # 测试脚本
├── dockerimages/           # 🐳 Docker镜像文件
├── venv/                   # 🐍 Python虚拟环境
├── deploy_linux.sh         # 🐧 Linux一键部署脚本
├── build_standalone.bat    # 🏗️ Windows独立构建脚本
├── check_encoding.bat      # 🔍 编码检查脚本
├── Dockerfile              # 🐳 Docker构建配置
├── docker-compose.yml      # 🐳 Docker编排配置
├── docker-compose-stable.yml # 🐳 稳定版Docker配置
├── env.example             # 📋 环境变量模板
├── mermaid.md              # 📊 系统架构流程图
├── README.md               # 📖 项目说明文档
├── LICENSE                 # 📄 许可证文件
└── CHANGELOG.md            # 📝 版本更新日志
```

### 🏗️ 核心架构说明

#### 后端架构 (Go)
- **Web服务层**: `web_server.go` 提供RESTful API和静态文件服务
- **业务逻辑层**: 图片处理、AI调用、配置管理等核心模块
- **数据访问层**: 文件系统操作、外部API调用
- **配置层**: 统一的环境变量和配置文件管理

#### 前端架构 (HTML/JS)
- **单页应用**: 基于原生JavaScript的响应式Web界面
- **实时通信**: Server-Sent Events实现实时日志流
- **文件处理**: 支持拖拽上传和进度监控

#### Python集成
- **PPT生成**: 独立的Python模块，通过Go后端调用
- **模板处理**: 基于python-pptx库的模板填充和生成

#### 外部服务集成
- **腾讯云COS**: 图片存储和处理
- **腾讯云IAI**: 人脸检测和人像抠图
- **Aibot API**: 智能文本解析和信息提取

## � 系统架构流程图

本项目提供了详细的系统架构流程图，帮助开发者和用户理解整个系统的工作原理。

### 📋 流程图文件
- **文件位置**: `mermaid.md`
- **包含内容**:
  - 🏗️ **整体系统架构流程图**: 展示所有组件之间的关系和数据流
  - 📝 **详细业务流程图**: 时序图展示完整的处理流程
  - 🔧 **核心模块架构图**: 模块间的依赖关系和交互

### 🎯 主要流程概览
```mermaid
graph LR
    A[📁 PPT上传] --> B[📝 文本提取]
    B --> C[🤖 AI解析]
    A --> D[🖼️ 图片提取]
    D --> E[👤 人像检测]
    E --> F[✨ 图片处理]
    C --> G[📊 数据整合]
    F --> G
    G --> H[📄 JSON输出]
    G --> I[🎯 PPT生成]
```

### 📖 查看完整流程图
请查看项目根目录下的 `mermaid.md` 文件，其中包含了完整的系统架构图表，包括：
- 用户交互层到存储层的完整数据流
- 各个API的调用时序
- 错误处理和重试机制
- 配置管理和日志系统

## �🔄 完整运作流程

本项目实现了从PPT文件上传到最终生成定制化PPT的完整自动化流程，主要分为以下五个阶段：

### 第一阶段：PPT文件上传和解析
1.  **用户上传PPT文件**：通过前端Web界面，用户拖拽或选择PPTX文件进行上传。
2.  **后端接收文件**：Web服务器接收上传文件，并将其保存到临时目录。
3.  **PPT内容解析**：后端对上传的PPTX文件进行解析，提取其中的文本内容和嵌入的图片。
4.  **图片自动提取**：自动识别并提取PPT中的图片，保存到指定的输出目录。

### 第二阶段：人像检测和处理
1.  **人像检测**：利用腾讯云COS API对提取出的每张图片进行人像检测，判断是否含有人像。
2.  **图片分类**：根据检测结果，将图片区分为人像图片和非人像图片。
3.  **人像抠图**：对检测到的人像图片，调用腾讯云COS和IAI API进行背景分割，生成透明背景的人像图片。
4.  **质量优化（预留）**：生成透明背景的PNG文件，为后续的图片质量优化（如歪斜矫正、人脸定位等预留功能）做准备。

### 第三阶段：AI信息提取
1.  **文本内容整理**：对从PPT中提取的原始文本进行清洗、格式化和初步处理。
2.  **AI API调用**：整理后的文本内容被发送到自定义的Aibot API (`https://aibot.gejiesoft.com/v1/chat-messages`) 进行深度解析和信息提取。
3.  **结构化数据**：Aibot API返回JSON格式的结构化简历信息，其中包括`姓名`、`医院`、`科室`、`职称`、`介绍`等字段。其中`介绍`字段会综合`职务职称`、`学术任职`、`专业擅长`、`学术成果`和`其他`等相关信息。
4.  **结果整合**：将AI提取的结构化文本信息与处理后的图片信息（如头像图片路径）进行整合。

### 第四阶段：结果输出
1.  **JSON文件生成**：将整合后的完整简历信息保存为JSON格式文件。
2.  **图片打包**：处理后的人像抠图文件和原始提取图片统一存放。
3.  **下载链接生成**：Web服务器生成JSON文件和处理后图片的下载链接（使用相对路径），方便用户获取。
4.  **前端展示**：前端界面实时显示处理进度和最终提取结果，并提供下载按钮。

### 第五阶段：PPT生成
1.  **数据准备**：利用AI提取的结构化简历信息（JSON格式）作为输入，并从输出目录获取处理后的头像图片路径。
2.  **PPT生成**：Go后端调用Python PPT生成模块（`main/ppt_generator/ppt_generator.py`），根据提供的JSON数据和指定的PPT模板（如`ppt-teample1.pptx`），自动填充文本占位符和插入头像图片。
3.  **文本排版**：PPT生成模块会根据预设的固定字体大小（例如14pt）来填充文本内容（如姓名、职称、医院、科室、介绍、专业擅长、职务职称列表、学术成果列表），以确保文本的显示效果，避免溢出和字体兼容性问题。
4.  **结果保存**：生成的定制化PPT文件保存到`output/`目录，并生成下载链接（使用相对路径），供用户下载使用。

## 📊 核心数据结构

### ResumeInfo（简历信息结构）
```go
// ResumeInfo 简历信息结构，用于存储从PPT中提取和AI解析后的结构化数据
type ResumeInfo struct {
	Name           string `json:"姓名"`             // 姓名
	Hospital       string `json:"医院"`             // 医院
	Department     string `json:"科室"`             // 科室
	Title          string `json:"职称"`             // 职称
	Description    string `json:"介绍"`             // 综合介绍，AI整合的完整描述
	ProfilePicture string `json:"头像,omitempty"`   // 头像图片路径（相对于output目录）

	// 详细字段，用于更精细的信息存储和PPT模板填充
	Position       string `json:"职务职称,omitempty"` // 职务职称（原始文本）
	AcademicTitle  string `json:"学术任职,omitempty"` // 学术任职（原始文本）
	Specialty      string `json:"专业擅长,omitempty"` // 专业擅长
	AcademicOutput string `json:"学术成果,omitempty"` // 学术成果（原始文本）
	Other          string `json:"其他,omitempty"`     // 其他信息

	// 结构化列表数据（用于PPT模板的列表展示）
	Positions       []string `json:"职务职称列表,omitempty"` // 职务职称列表
	AcademicTitles  []string `json:"学术任职列表,omitempty"` // 学术任职列表
	AcademicOutputs []string `json:"学术成果列表,omitempty"` // 学术成果列表
}
```

### ProcessingStats（图片处理统计）
```go
// ProcessingStats 图片处理统计信息，记录完整的处理过程和结果
type ProcessingStats struct {
	TotalImages     int           `json:"total_images"`     // 总图片数量
	PortraitImages  int           `json:"portrait_images"`  // 检测到的人像图片数
	ProcessedImages int           `json:"processed_images"` // 成功处理的图片数
	ProcessingTime  int64         `json:"processing_time"`  // 总处理时间（毫秒）
	BestPortrait    string        `json:"best_portrait,omitempty"` // 最佳头像路径
	Results         []ImageResult `json:"results,omitempty"`       // 详细处理结果
}

// ImageResult 单张图片的处理结果详情
type ImageResult struct {
	OriginalPath      string  `json:"original_path"`      // 原始图片路径
	ProcessedPath     string  `json:"processed_path,omitempty"` // 处理后图片路径
	CutoutPath        string  `json:"cutout_path,omitempty"`    // 抠图结果路径
	StandardPhotoPath string  `json:"standard_photo_path,omitempty"` // 标准化照片路径
	IsPortrait        bool    `json:"is_portrait"`        // 是否为人像
	Confidence        float64 `json:"confidence,omitempty"` // 检测置信度
	ProcessingTime    int64   `json:"processing_time"`    // 单张图片处理时间
	Error             string  `json:"error,omitempty"`    // 错误信息

	// 图片优化相关信息
	CompressionInfo *ImageCompressionInfo `json:"compression_info,omitempty"` // 压缩信息
	QualityWarnings []string              `json:"quality_warnings,omitempty"` // 质量警告
	EnhancedPath    string                `json:"enhanced_path,omitempty"`    // 增强后图片路径
	UseEnhanced     bool                  `json:"use_enhanced,omitempty"`     // 是否使用增强图片
}
```

### Config（配置结构）
```go
// Config 应用配置结构，支持环境变量和配置文件双重配置
type Config struct {
	// AI API配置
	AIAPIKey    string `json:"ai_api_key"`    // Aibot API密钥
	AIModel     string `json:"ai_model"`      // AI模型名称
	AIEndpoint  string `json:"ai_endpoint"`   // AI API端点

	// 腾讯云配置
	TencentSecretID  string `json:"tencent_secret_id"`  // 腾讯云SecretID
	TencentSecretKey string `json:"tencent_secret_key"` // 腾讯云SecretKey
	TencentRegion    string `json:"tencent_region"`     // 腾讯云地域
	TencentBucket    string `json:"tencent_bucket"`     // 腾讯云存储桶

	// 图片处理配置
	PortraitDetectionThreshold float64                `json:"portrait_detection_threshold"` // 人像检测阈值
	EnableImageProcessing      bool                   `json:"enable_image_processing"`      // 启用图片处理
	MaxImageSizeMB             int                    `json:"max_image_size_mb"`            // 最大图片大小
	ImageProcessingTimeout     int                    `json:"image_processing_timeout"`     // 处理超时时间
	EnablePhotoEnhancement     bool                   `json:"enable_photo_enhancement"`     // 启用照片增强
	PhotoEnhancement           PhotoEnhancementConfig `json:"photo_enhancement"`            // 照片增强配置
}
```

## 🔧 开发指南

### 本地开发环境
1. 安装Go 1.24+
2. 配置环境变量或使用配置文件
3. 运行 `go mod tidy` 安装依赖
4. 使用 `go run main.go` 启动开发服务器

### 代码结构
- `main.go`: 程序入口，初始化配置和启动服务器
- `web_server.go`: Web服务器实现，处理HTTP请求
- `image_processor.go`: 图片处理核心逻辑
- `photo_enhancer.go`: 照片增强和矫正功能
- `config.go`: 配置管理，支持环境变量和配置文件

## 🐳 Docker部署

### 构建镜像
```bash
docker build -t gejiesoft-pdf-tools .
```

### 运行容器
```bash
docker run -d \
  -p 8088:8088 \
  -v $(pwd)/output:/app/output \
  --env-file .env \
  gejiesoft-pdf-tools
```

### 使用Docker Compose
```bash
docker-compose up -d
```

## 📚 文档

- [环境变量配置说明](docs/环境变量配置说明.md)
- [测试指南](docs/测试指南.md)
- [项目结构说明](docs/项目结构说明.md)
- [本地环境变量使用示例](docs/本地环境变量使用示例.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用私有商业许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 👨‍💻 作者

*Gordon Wang*
**GejieSoft Team**

- 官方网站: [https://www.gejiesoft.com/]
- 问题反馈: [https://www.gejiesoft.com/]
- 功能建议: [https://www.gejiesoft.com/]

## ⚙️ 配置说明

### 环境变量配置

| 变量名 | 说明 | 默认值 | 必需 |
|--------|------|--------|------|
| `AIBOT_API_KEY` | Aibot API密钥 | - | ✅ |
| `AIBOT_MODEL` | Aibot模型名称 | 简历信息提取 | ❌ |
| `AIBOT_ENDPOINT` | Aibot API端点 | https://aibot.gejiesoft.com/v1 | ❌ |
| `TENCENT_SECRET_ID` | 腾讯云SecretId | - | ✅ |
| `TENCENT_SECRET_KEY` | 腾讯云SecretKey | - | ✅ |
| `TENCENT_REGION` | 腾讯云地域 | ap-shanghai | ❌ |
| `TENCENT_BUCKET` | 腾讯云存储桶 | - | ✅ |
| `PORT` | Web服务器端口 | 8088 | ❌ |

### 详细配置说明
请参考 [环境变量配置说明](docs/环境变量配置说明.md)

## 🧪 测试指南

### 快速测试
```bash
# 测试本地环境
test_local.bat

# 测试Docker环境
test_docker.bat

# 测试环境变量配置
test_env_local.bat
```

### 详细测试说明
请参考 [测试指南](docs/测试指南.md)

## 🛠️ 脚本工具说明

项目根目录提供了多个便捷的脚本工具，用于简化开发、测试和部署流程：

### 脚本分类总结

| 脚本名称 | 类型 | 主要用途 | 使用频率 | 支持平台 |
|---------|------|----------|----------|----------|
| `deploy_linux.sh` | 部署 | Linux自动化部署 | 部署时 | Linux |
| `build_standalone.bat` | 构建 | 创建独立运行包 | 发布时 | Windows |
| `test_local.bat` | 测试 | 本地可执行文件测试 | 部署时 | Windows |
| `test_docker.bat` | 测试 | Docker环境测试 | 部署时 | Windows |
| `test_env_local.bat` | 测试 | 本地开发环境测试 | 开发时 | Windows |
| `test_env_complete.bat` | 测试 | 完整配置测试 | 开发时 | Windows |
| `verify_security.bat` | 验证 | 安全性检查 | 部署前 | Windows |
| `check_encoding.bat` | 验证 | 编码统一性检查 | 开发/发布时 | Windows |

### 脚本功能详解

#### 🚀 部署脚本
- **`deploy_linux.sh`**: Linux系统一键自动化部署
  - 自动检测Linux发行版并安装相应依赖
  - 检查并升级Go版本至项目要求
  - 配置Go模块代理以加速依赖下载
  - 创建Python虚拟环境并安装依赖
  - 自动构建应用程序
  - 交互式配置环境变量
  - 可选创建systemd服务实现开机自启
  - 配置防火墙规则
  - 启动应用程序

#### 🔨 构建脚本
- **`build_standalone.bat`**: 构建可独立运行的发布包
  - 创建dist目录结构
  - 复制可执行文件和前端文件
  - 生成UTF-8编码的说明文件
  - 创建完整的独立运行包

#### 🧪 测试脚本
- **`test_local.bat`**: 测试本地编译的可执行文件
  - 检查程序文件存在性
  - 启动程序并验证端口监听
  - 提供完整的本地测试流程

- **`test_docker.bat`**: 测试Docker容器化部署
  - 检查Docker环境可用性
  - 自动创建配置文件
  - 构建并启动容器
  - 验证部署状态

- **`test_env_local.bat`**: 测试本地Go开发环境
  - 设置完整的环境变量
  - 使用go run启动程序
  - 验证开发环境配置

- **`test_env_complete.bat`**: 全面测试环境变量配置
  - 检查配置文件完整性
  - 测试配置优先级
  - 验证环境变量读取

#### 🔒 验证脚本
- **`verify_security.bat`**: 验证项目安全性配置
  - 检查敏感文件排除设置
  - 验证Docker安全配置
  - 确保敏感信息不被泄露

- **`check_encoding.bat`**: 检查项目文件编码统一性
  - 验证所有文件的UTF-8编码
  - 检查字符集设置
  - 确保编码一致性

### 快速使用指南

#### Linux 用户（推荐）
```bash
# 1. 克隆项目
git clone <repository-url>
cd gejiesoft-pdf-tools

# 2. 一键部署
chmod +x deploy_linux.sh
./deploy_linux.sh

# 3. 访问应用
# 应用会自动启动，访问 http://localhost:8088
```

#### Windows 新手用户
```bash
# 1. 首次使用 - 测试本地环境
test_env_local.bat

# 2. 配置验证 - 确保设置正确
test_env_complete.bat

# 3. 启动程序 - 本地运行
test_local.bat
```

#### 开发人员
```bash
# Windows 开发环境
# 1. 开发测试
test_env_local.bat

# 2. 编码检查
check_encoding.bat

# 3. 安全性验证
verify_security.bat

# Linux 开发环境
# 1. 自动化部署
./deploy_linux.sh

# 2. 手动构建（开发时）
cd main && go build -o ../gejiesoft-pdf-tools && cd ..
```

#### 部署人员
```bash
# 1. Docker部署（推荐）
test_docker.bat

# 2. Linux生产环境
./deploy_linux.sh

# 3. Windows本地部署
test_local.bat

# 4. 构建独立包
build_standalone.bat
```

## ☁️ API 调用情况

本项目集成了以下外部API服务，实现智能化的PPT信息提取和处理：

### 🔗 腾讯云服务集成

#### 1. 腾讯云COS (Cloud Object Storage)
- **用途**: 图片存储、CDN加速和数据持久化
- **实现**: `main/image_processor.go` 中的COS客户端
- **配置**: 通过环境变量 `TENCENT_SECRET_ID`、`TENCENT_SECRET_KEY`、`TENCENT_BUCKET`、`TENCENT_REGION`
- **相关文档**: [COS存储桶命名规范](https://cloud.tencent.com/document/product/436/13312)

#### 2. 腾讯云IAI (人脸识别与人体分析)
- **人脸检测API**:
  - **接口**: `DetectFace`
  - **用途**: 检测图片中的人脸位置和数量，判断是否为人像图片
  - **实现**: `main/photo_enhancer.go` 中的 `detectFacesWithTencentAPI` 函数
  - **文档**: [Go SDK 人脸识别](https://cloud.tencent.com/document/product/460/78834)

- **人像抠图API**:
  - **接口**: `AIPortraitMatting`
  - **用途**: 智能分割图像背景，生成透明背景的人像图片
  - **实现**: `main/photo_enhancer.go` 中的 `removeBackgroundWithTencentAPI` 函数
  - **输出**: PNG格式的透明背景图片
  - **文档**: [Go SDK 人像抠图](https://cloud.tencent.com/document/product/460/110934)

- **图像超分API**:
  - **接口**: `ImageSuperResolution`
  - **用途**: 提升低分辨率图片的清晰度和质量
  - **实现**: `main/photo_enhancer.go` 中的 `enhanceImageWithTencentAI` 函数
  - **触发条件**: 图片DPI < 100 或分辨率 < 400x500

### 🤖 Aibot API集成

#### 自定义智能文本解析服务
- **端点**: `https://aibot.gejiesoft.com/v1/chat-messages`
- **实现**: `main/post_aibot_api.go` 中的 `callAibotAPI` 函数
- **用途**: 将PPT提取的原始文本转换为结构化的简历信息
- **请求格式**:
  ```json
  {
    "inputs": {},
    "query": "提取的PPT文本内容",
    "response_mode": "blocking",
    "conversation_id": "",
    "user": "GejieSoft-PDF-Tools",
    "files": []
  }
  ```
- **响应处理**: 解析返回的JSON中的 `answer` 字段，提取结构化简历数据
- **错误处理**: 支持最多3次重试，每次重试间隔3秒
- **超时设置**: 60秒请求超时

### 📊 API调用流程

```mermaid
sequenceDiagram
    participant App as 应用程序
    participant COS as 腾讯云COS
    participant IAI as 腾讯云IAI
    participant Aibot as Aibot API

    App->>Aibot: 发送PPT文本内容
    Aibot-->>App: 返回结构化简历信息

    App->>IAI: 人脸检测请求
    IAI-->>App: 返回人脸位置信息

    alt 检测到人像
        App->>IAI: 人像抠图请求
        IAI-->>App: 返回透明背景图片

        opt 图片质量不足
            App->>IAI: 图像超分请求
            IAI-->>App: 返回高清图片
        end
    end

    App->>COS: 上传处理后的图片
    COS-->>App: 返回存储URL
```

### 🔒 安全配置

- **API密钥管理**: 所有敏感信息通过环境变量管理，不在代码中硬编码
- **请求加密**: 使用HTTPS协议确保数据传输安全
- **错误处理**: 完善的错误处理和重试机制，避免API调用失败影响整体流程
- **超时控制**: 合理的超时设置，防止长时间等待

## ⚠️ 常见部署问题与解决方案

### Linux 部署常见问题

#### 1. Go 版本不兼容

**问题描述**：在某些Linux发行版中，通过包管理器安装的Go版本可能过低（< 1.24）。

**解决方案**：
```bash
# 手动安装最新版本的Go
wget https://dl.google.com/go/go1.24.4.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.24.4.linux-amd64.tar.gz

# 设置环境变量
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
source ~/.bashrc

# 验证安装
go version
```

#### 2. Python 虚拟环境问题

**问题描述**：Python 依赖安装失败或虚拟环境无法激活。

**解决方案**：
```bash
# 确保安装了python3-venv
sudo apt install python3-venv python3-dev

# 如果仍有问题，使用完整路径
/usr/bin/python3 -m venv venv
source venv/bin/activate

# 升级pip
pip install --upgrade pip
```

#### 3. 权限问题

**问题描述**：在某些目录下运行时遇到权限错误。

**解决方案**：
```bash
# 确保用户对项目目录有完整权限
sudo chown -R $USER:$USER /path/to/gejiesoft-pdf-tools
chmod -R 755 /path/to/gejiesoft-pdf-tools

# 或者使用专门的用户运行
sudo useradd -r -s /bin/false gejiesoft
sudo chown -R gejiesoft:gejiesoft /path/to/gejiesoft-pdf-tools
```

#### 4. 端口被占用

**问题描述**：8088端口被其他服务占用。

**解决方案**：
```bash
# 查看端口占用情况
sudo netstat -tlnp | grep :8088
# 或者
sudo lsof -i :8088

# 修改端口配置
export PORT=8089
# 或在.env文件中设置
echo "PORT=8089" >> .env
```

#### 5. 防火墙配置

**问题描述**：外部无法访问Web界面。

**解决方案**：
```bash
# Ubuntu/Debian (UFW)
sudo ufw allow 8088/tcp
sudo ufw reload

# CentOS/RHEL/Fedora (firewalld)
sudo firewall-cmd --permanent --add-port=8088/tcp
sudo firewall-cmd --reload

# 或者临时关闭防火墙进行测试
sudo systemctl stop ufw
sudo systemctl stop firewalld
```

### Linux 性能优化建议

#### 1. 系统资源监控

```bash
# 安装监控工具
sudo apt install htop iostat # Ubuntu/Debian
sudo yum install htop sysstat # CentOS/RHEL

# 监控系统资源
htop
iostat -x 1
```

#### 2. 调整系统参数

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 调整内核参数
echo "net.core.somaxconn = 1024" >> /etc/sysctl.conf
echo "net.core.netdev_max_backlog = 5000" >> /etc/sysctl.conf
sysctl -p
```

#### 3. 使用反向代理

使用Nginx作为反向代理，提高性能和安全性：

```nginx
# /etc/nginx/sites-available/gejiesoft-pdf-tools
server {
    listen 80;
    server_name your_domain.com;
    
    client_max_body_size 100M;
    
    location / {
        proxy_pass http://localhost:8088;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        proxy_pass http://localhost:8088;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

启用配置：
```bash
sudo ln -s /etc/nginx/sites-available/gejiesoft-pdf-tools /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### Apache 反向代理下PPT下载404

**问题描述**：当将本应用部署在Apache反向代理之后，通过Web界面下载生成的PPT文件时，可能会遇到"404 Not Found"错误，即使Go后端服务正常运行。

**问题分析**：这通常是由于Apache在处理包含编码斜杠（`%2F`）的URL时，默认会将其视为不安全的字符并拒绝转发。而本应用在提供PPT下载路径时，为了提高安全性和可移植性，返回的是相对于`output/`目录的相对路径，这些路径中可能包含斜杠，当这些斜杠被URL编码后，Apache无法正确识别导致404。

**解决方案**：在Apache的虚拟主机配置（`VirtualHost`）或目录配置中，添加以下指令来允许编码斜杠通过：

```apache
AllowEncodedSlashes On
```

**示例配置**：

```apache
<VirtualHost *:80>
    ServerName your_domain.com

    ProxyRequests Off
    ProxyPreserveHost On

    <Proxy *>
        Order deny,allow
        Allow from all
    </Proxy>

    ProxyPass /api/download-ppt/ http://localhost:8088/api/download-ppt/ nocanon
    ProxyPassReverse /api/download-ppt/ http://localhost:8088/api/download-ppt/

    ProxyPass / http://localhost:8088/
    ProxyPassReverse / http://localhost:8088/

    # 允许编码斜杠通过，解决PPT下载404问题
    AllowEncodedSlashes On

    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined
</VirtualHost>
```

**操作步骤**：
1.  打开您的Apache配置文件（通常在`/etc/apache2/sites-available/` 或 `/etc/httpd/conf.d/` 目录下）。
2.  找到对应的`VirtualHost`或`Directory`块。
3.  在其中添加`AllowEncodedSlashes On`指令。
4.  保存文件并重启Apache服务，例如：`sudo systemctl restart apache2` 或 `sudo service httpd restart`。