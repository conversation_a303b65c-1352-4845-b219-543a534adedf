# 变更日志

本项目的所有重要变更都会记录在此文件中。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/spec/v2.0.0.html)。

## [未发布]

### 新增
- Linux 自动化部署脚本 (`deploy_linux.sh`)
- 详细的 Linux 部署指南
- 贡献指南 (`CONTRIBUTING.md`)
- 项目变更日志 (`CHANGELOG.md`)

### 改进
- 完善 README.md 文档结构
- 优化多平台部署支持
- 增强错误处理和日志记录

### 修复
- 修复 Windows 环境下路径兼容性问题
- 解决 Apache 反向代理下 PPT 下载 404 问题

## [1.0.0] - 2024-01-15

### 新增
- 智能 PPT 文本提取功能
- 图片自动提取和分类
- 人像识别与背景抠图
- AI 驱动的简历信息结构化
- PPT 生成功能（基于模板和数据）
- Web 界面操作支持
- Docker 容器化部署
- 腾讯云 API 集成（COS、IAI）
- 多环境配置支持

### 功能特性
- 支持 PPTX 文件解析
- 人像检测和背景去除
- 结构化数据提取
- 自定义 PPT 模板支持
- 实时处理进度显示
- 批量图片处理
- 相对路径下载支持

### 技术栈
- 后端：Go 1.24+
- 前端：HTML5 + JavaScript + TailwindCSS
- Python：PPT 生成模块
- 存储：腾讯云 COS
- AI：自定义 Aibot API

### 支持平台
- Windows 10/11
- Linux (Ubuntu, CentOS, Debian)
- macOS (通过 Docker)

---

## 版本说明

### 版本格式
- 主版本号：当你做了不兼容的 API 修改
- 次版本号：当你做了向下兼容的功能性新增
- 修订号：当你做了向下兼容的问题修正

### 变更类型
- `新增`：新功能
- `改进`：对现有功能的改进
- `修复`：错误修复
- `移除`：移除的功能
- `安全`：安全相关的修复
- `弃用`：将来会被移除的功能

### 发布计划
- **v1.1.0**：计划添加更多图片处理功能
- **v1.2.0**：计划支持更多 PPT 模板格式
- **v2.0.0**：计划重构核心架构，提升性能 