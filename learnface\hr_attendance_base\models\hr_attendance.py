# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class HrAttendance(models.Model):
    _inherit = "hr.attendance"

    employee_id_image = fields.Image(string="Image employee", related='employee_id.image_1920')
    ismobile_check_in = fields.<PERSON><PERSON><PERSON>(string="移动设备签到？")
    ismobile_check_out = fields.Boolean(string="移动设备签出？")
#    employee_id_department_id = fields.Many2one('hr.department', string='Department', related='employee_id.department_id')
#    employee_id_identification_id = fields.Char(string='Identification No', groups="hr.group_hr_user", related='employee_id.identification_id')
