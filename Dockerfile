# 使用官方Go镜像作为构建环境
FROM golang:1.24-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apk add --no-cache git

# 复制整个项目结构（模拟本地环境）
COPY . .

# 配置Go代理（国内加速）
ENV GOPROXY=https://goproxy.cn,direct

# 下载依赖（在根目录，就像本地一样）
RUN go mod download

# 构建应用程序（完全模拟build.bat的过程）
WORKDIR /app/main
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o ../main .

# 使用轻量级的alpine镜像作为运行环境
FROM alpine:latest

# 配置清华大学Alpine镜像源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apk/repositories

# 安装系统依赖（包括Python环境）
RUN apk update && apk add --no-cache \
    ca-certificates \
    python3 \
    py3-pip \
    python3-dev

# 创建非root用户
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/main .

# 复制所有必要的项目文件（保持目录结构）
COPY --from=builder /app/templates-ppt/ ./templates-ppt/
COPY --from=builder /app/main/templates/ ./templates/
COPY --from=builder /app/main/ppt_generator/ ./ppt_generator/
COPY --from=builder /app/frontend/ ./frontend/

# 创建一个最小化配置文件（主要依赖环境变量）
RUN echo '{\
  "ai_model": "简历信息提取",\
  "tencent_region": "ap-shanghai",\
  "portrait_detection_threshold": 0.7\
}' > ./config.json

# 创建必要的目录
RUN mkdir -p output temp

# 安装Python依赖（使用清华源，Docker环境安全地绕过系统包管理限制）
RUN if [ -f ./ppt_generator/requirements.txt ]; then \
        pip3 install --break-system-packages --no-cache-dir -r ./ppt_generator/requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/; \
    fi

# 设置权限
RUN chown -R appuser:appgroup /app

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8088

# 启动命令
CMD ["./main"] 