<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>头像抠图系统 - 功能演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 20px 0;
            font-size: 3em;
            font-weight: 300;
        }
        
        .demo-section {
            padding: 40px;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .demo-card {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .demo-card h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .demo-steps {
            list-style: none;
            padding: 0;
        }
        
        .demo-steps li {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .demo-steps li strong {
            color: #667eea;
        }
        
        .feature-showcase {
            background: #e8f4fd;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .feature-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
        }
        
        .feature-item h4 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .feature-item p {
            color: #666;
            font-size: 0.9em;
            line-height: 1.5;
        }
        
        .tech-specs {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
        }
        
        .specs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .spec-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .spec-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .spec-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }
        
        .action-center {
            text-align: center;
            padding: 40px;
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
        }
        
        .workflow-diagram {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }
        
        .workflow-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
            margin: 30px 0;
        }
        
        .workflow-step {
            flex: 1;
            min-width: 150px;
            text-align: center;
        }
        
        .workflow-step .step-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px auto;
            color: white;
            font-size: 2em;
        }
        
        .workflow-step h4 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .workflow-step p {
            color: #666;
            font-size: 0.9em;
        }
        
        .workflow-arrow {
            font-size: 2em;
            color: #667eea;
            margin: 0 10px;
        }
        
        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }
            
            .workflow-steps {
                flex-direction: column;
            }
            
            .workflow-arrow {
                transform: rotate(90deg);
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>🎭 头像抠图系统演示</h1>
            <p>基于Human.js的智能人脸识别与头像生成技术</p>
        </div>
        
        <!-- 演示内容 -->
        <div class="demo-section">
            <!-- 功能演示 -->
            <div class="demo-grid">
                <div class="demo-card">
                    <h3>📷 单张图片处理演示</h3>
                    <ul class="demo-steps">
                        <li><strong>步骤1:</strong> 上传或拖拽照片到处理区域</li>
                        <li><strong>步骤2:</strong> 系统自动检测人脸并分析468个关键点</li>
                        <li><strong>步骤3:</strong> 智能裁切生成1:1比例标准头像</li>
                        <li><strong>步骤4:</strong> 应用背景移除生成透明PNG</li>
                        <li><strong>步骤5:</strong> 下载最终头像文件</li>
                    </ul>
                </div>
                
                <div class="demo-card">
                    <h3>📁 批量处理演示</h3>
                    <ul class="demo-steps">
                        <li><strong>步骤1:</strong> 选择包含照片的文件夹</li>
                        <li><strong>步骤2:</strong> 系统扫描并识别所有图像文件</li>
                        <li><strong>步骤3:</strong> 逐个进行人脸检测和头像生成</li>
                        <li><strong>步骤4:</strong> 实时显示处理进度和统计信息</li>
                        <li><strong>步骤5:</strong> 批量下载所有生成的头像</li>
                    </ul>
                </div>
            </div>
            
            <!-- 技术特色 -->
            <div class="feature-showcase">
                <h3>🚀 核心技术特色</h3>
                <div class="feature-grid">
                    <div class="feature-item">
                        <div class="feature-icon">🤖</div>
                        <h4>AI智能检测</h4>
                        <p>基于Google MediaPipe技术，468个3D关键点精确定位人脸特征</p>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">🔄</div>
                        <h4>自动旋转矫正</h4>
                        <p>智能检测人脸角度，自动矫正到标准位置，支持±45度角度范围</p>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">✂️</div>
                        <h4>智能裁切</h4>
                        <p>基于面部特征计算最佳裁切区域，保留完整面部和适当边距</p>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">🎨</div>
                        <h4>背景移除</h4>
                        <p>智能背景移除算法，生成透明背景的标准头像</p>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">⚡</div>
                        <h4>GPU加速</h4>
                        <p>使用WebGL硬件加速，大幅提升处理速度和性能</p>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">📊</div>
                        <h4>质量保证</h4>
                        <p>多重置信度检查，确保检测结果的准确性和可靠性</p>
                    </div>
                </div>
            </div>
            
            <!-- 处理流程 -->
            <div class="workflow-diagram">
                <h3>🔄 处理流程图</h3>
                <div class="workflow-steps">
                    <div class="workflow-step">
                        <div class="step-icon">📤</div>
                        <h4>图像输入</h4>
                        <p>上传JPG/PNG格式照片</p>
                    </div>
                    
                    <div class="workflow-arrow">→</div>
                    
                    <div class="workflow-step">
                        <div class="step-icon">🔍</div>
                        <h4>人脸检测</h4>
                        <p>BlazeFace快速检测</p>
                    </div>
                    
                    <div class="workflow-arrow">→</div>
                    
                    <div class="workflow-step">
                        <div class="step-icon">📐</div>
                        <h4>特征分析</h4>
                        <p>468点FaceMesh分析</p>
                    </div>
                    
                    <div class="workflow-arrow">→</div>
                    
                    <div class="workflow-step">
                        <div class="step-icon">✂️</div>
                        <h4>智能裁切</h4>
                        <p>1:1比例标准裁切</p>
                    </div>
                    
                    <div class="workflow-arrow">→</div>
                    
                    <div class="workflow-step">
                        <div class="step-icon">🎨</div>
                        <h4>背景移除</h4>
                        <p>生成透明背景</p>
                    </div>
                    
                    <div class="workflow-arrow">→</div>
                    
                    <div class="workflow-step">
                        <div class="step-icon">💾</div>
                        <h4>输出结果</h4>
                        <p>下载PNG头像</p>
                    </div>
                </div>
            </div>
            
            <!-- 技术规格 -->
            <div class="tech-specs">
                <h3>📊 技术规格</h3>
                <div class="specs-grid">
                    <div class="spec-item">
                        <div class="spec-value">468</div>
                        <div class="spec-label">3D关键点</div>
                    </div>
                    
                    <div class="spec-item">
                        <div class="spec-value">95%</div>
                        <div class="spec-label">检测准确率</div>
                    </div>
                    
                    <div class="spec-item">
                        <div class="spec-value">500ms</div>
                        <div class="spec-label">平均处理时间</div>
                    </div>
                    
                    <div class="spec-item">
                        <div class="spec-value">512px</div>
                        <div class="spec-label">输出尺寸</div>
                    </div>
                    
                    <div class="spec-item">
                        <div class="spec-value">±45°</div>
                        <div class="spec-label">支持角度</div>
                    </div>
                    
                    <div class="spec-item">
                        <div class="spec-value">100+</div>
                        <div class="spec-label">测试样本</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 操作中心 -->
        <div class="action-center">
            <h3>🎯 开始体验</h3>
            <p>选择下方功能开始使用头像抠图处理系统</p>
            
            <a href="avatar_processor.html" class="btn">
                📷 单张图片处理
            </a>
            
            <a href="batch_processor.html" class="btn btn-secondary">
                📁 批量处理
            </a>
            
            <a href="test_processor.html" class="btn btn-info">
                🧪 系统测试
            </a>
            
            <div style="margin-top: 30px;">
                <p style="color: #666; font-size: 0.9em;">
                    💡 建议首次使用时先运行"系统测试"确保环境兼容性
                </p>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 渐入动画
            const elements = document.querySelectorAll('.demo-card, .feature-item, .workflow-step');
            elements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(30px)';
                
                setTimeout(() => {
                    element.style.transition = 'all 0.6s ease';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
