# -*- coding: utf-8 -*-

{
    'name': 'HR考勤人脸识别-图片编辑器',
    'summary': "开箱即用的图像、图片、照片编辑器 \
(裁剪、翻转、旋转、绘图、形状、图标、文本、遮罩过滤器、原始图像过滤器、下载、上传、撤消、重做、重置、删除对象（形状、线条、遮罩图像…）\
和滤色器（灰度、反转、深褐色、模糊锐化、浮雕、去除白色、亮度、噪波、像素化、滤色器、着色、相乘、混合）\
编辑图像、管理图像、管理文本、图像文本绘图、图像绘图、图像绘图、过滤器、图像过滤器\
遮罩图像、遮罩图标、图像图标裁剪、图像裁剪旋转、图像旋转翻转、图像翻转编辑、图片编辑、照片编辑过滤器\
照片过滤器、裁剪照片、裁剪文本照片、文本绘图、照片绘图、照片绘制遮罩、照片遮罩管理、照片管理\
翻转照片、翻转旋转照片、旋转图片、旋转裁剪图片、裁剪图片、绘制图片、绘图图片、编辑器图片\
过滤器、图片过滤器、图像编辑器、照片编辑器、调整大小 \
",
    'author': "GejieSoft Inc",
    'website': "https://www.gejiesoft.com",


    #类别可用于筛选模块列表中的模块
    'category': "扩展工具",
    'version': '********',
    # 此模块正常工作所需的前置模块
    'depends': ['web', 'field_image_preview'],
    "license": "LGPL-3",
    'images': [
        'static/description/icon.png',
    ],
    # 'data': [
    #     'views/assets.xml',
    # ],
    # 'qweb': [ 'static/src/xml/image.xml', ],
    'installable': True,
    'application': False,
    # 如果为True，则所有依赖项都将自动安装模块
    'auto_install': False,
    "cloc_exclude": [
        "static/src/lib/**/*",  # 排除单个文件夹
    ],
    'assets': {
        
        'web.assets_backend': [
            'field_image_editor/static/src/lib/tui/fabric.min.js',
            'field_image_editor/static/src/lib/tui/tui-code-snippet.min.js',
            'field_image_editor/static/src/lib/tui/tui-color-picker.css',
            'field_image_editor/static/src/lib/tui/tui-color-picker.js',
            'field_image_editor/static/src/lib/tui/tui.image-editor/dist/tui-image-editor.css',
            'field_image_editor/static/src/lib/tui/tui.image-editor/dist/tui-image-editor.js',
            'field_image_editor/static/src/lib/tui/tui.image-editor/examples/js/theme/black-theme.js',

            'field_image_editor/static/src/component/image_field.xml',
            'field_image_editor/static/src/component/image_field.js',
        ],
    },
}
