{"format": "graph-model", "generatedBy": "https://github.com/google/mediapipe", "convertedBy": "https://github.com/vladmandic", "signature": {"inputs": {"input_1": {"name": "input_1:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "224"}, {"size": "224"}, {"size": "3"}]}}}, "outputs": {"Identity_1:0": {"name": "Identity_1:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}]}}, "Identity:0": {"name": "Identity:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "63"}]}}, "Identity_2:0": {"name": "Identity_2:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}]}}, "Identity_3:0": {"name": "Identity_3:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "63"}]}}}}, "modelTopology": {"node": [{"name": "Identity_dense/kernel", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "672"}, {"size": "63"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Identity_dense/bias", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "63"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/conv_handflag/MatMul_model_1/model/conv_handflag/BiasAdd_dense/kernel", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "672"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/conv_handflag/MatMul_model_1/model/conv_handflag/BiasAdd_dense/bias", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "model_1/model/conv_handedness/MatMul_model_1/model/conv_handedness/BiasAdd_dense/kernel", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "672"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/conv_handedness/MatMul_model_1/model/conv_handedness/BiasAdd_dense/bias", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "model_1/model/batch_normalization_44/FusedBatchNormV3_model_1/model/conv2d_29/Conv2D1/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "112"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_28/filter", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "112"}, {"size": "672"}]}}}}}, {"name": "Add_28/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "672"}]}}}}}, {"name": "depthwise_14/filter_in", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "672"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Add_29/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "672"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_29/filter", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "672"}, {"size": "112"}]}}}}}, {"name": "model_1/model/batch_normalization_41/FusedBatchNormV3_model_1/model/conv2d_29/Conv2D_model_1/model/conv2d_27/Conv2D1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "112"}]}}}}}, {"name": "Conv2D_26/filter", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "112"}, {"size": "672"}]}}}}}, {"name": "Add_26/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "672"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "depthwise_13/filter_in", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "672"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Add_27/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "672"}]}}}}}, {"name": "Conv2D_27/filter", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "672"}, {"size": "112"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/batch_normalization_38/FusedBatchNormV3_model_1/model/conv2d_29/Conv2D_model_1/model/conv2d_25/Conv2D1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "112"}]}}}}}, {"name": "Conv2D_24/filter", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "112"}, {"size": "672"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Add_24/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "672"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "depthwise_12/filter_in", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "672"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Add_25/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "672"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_25/filter", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "672"}, {"size": "112"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/batch_normalization_32/FusedBatchNormV3_model_1/model/conv2d_21/Conv2D1/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_20/filter", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "384"}]}}}}}, {"name": "Add_20/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "depthwise_10/filter_in", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "384"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Add_21/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_21/filter", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/batch_normalization_29/FusedBatchNormV3_model_1/model/conv2d_21/Conv2D_model_1/model/conv2d_19/Conv2D1/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_18/filter", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "384"}]}}}}}, {"name": "Add_18/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "depthwise_9/filter_in", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "384"}, {"size": "1"}]}}}}}, {"name": "Add_19/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}}}, {"name": "Conv2D_19/filter", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/batch_normalization_23/FusedBatchNormV3_model_1/model/conv2d_15/Conv2D1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}]}}}}}, {"name": "Conv2D_14/filter", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "48"}, {"size": "288"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Add_14/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "288"}]}}}}}, {"name": "depthwise_7/filter_in", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "288"}, {"size": "1"}]}}}}}, {"name": "Add_15/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "288"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_15/filter", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "288"}, {"size": "48"}]}}}}}, {"name": "model_1/model/batch_normalization_20/FusedBatchNormV3_model_1/model/conv2d_15/Conv2D_model_1/model/conv2d_13/Conv2D1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}]}}}}}, {"name": "Conv2D_12/filter", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "48"}, {"size": "288"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Add_12/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "288"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "depthwise_6/filter_in", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "288"}, {"size": "1"}]}}}}}, {"name": "Add_13/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "288"}]}}}}}, {"name": "Conv2D_13/filter", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "288"}, {"size": "48"}]}}}}}, {"name": "model_1/model/batch_normalization_14/FusedBatchNormV3_model_1/model/conv2d_9/Conv2D1/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_8/filter", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "144"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Add_8/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "144"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "depthwise_4/filter_in", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "144"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Add_9/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "144"}]}}}}}, {"name": "Conv2D_9/filter", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "144"}, {"size": "24"}]}}}}}, {"name": "model_1/model/batch_normalization_8/FusedBatchNormV3_model_1/model/conv2d_5/Conv2D1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "Conv2D_4/filter", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Add_4/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "depthwise_2/filter_in", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "96"}, {"size": "1"}]}}}}}, {"name": "Add_5/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_5/filter", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "16"}]}}}}}, {"name": "Conv2D_2/filter", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "64"}]}}}}}, {"name": "Add_2/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "depthwise_1/filter_in", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Add_3/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_3/filter", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "16"}]}}}}}, {"name": "model_1/model/batch_normalization_5/FusedBatchNormV3_model_1/model/conv2d_5/Conv2D_model_1/model/conv2d_3/Conv2D1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "Conv2D/filter", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "3"}, {"size": "24"}]}}}}}, {"name": "Add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "depthwise/filter_in", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}}}, {"name": "Add_1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "Conv2D_1/filter", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/batch_normalization_2/FusedBatchNormV3_model_1/model/conv2d_5/Conv2D_model_1/model/conv2d_1/Conv2D1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "Conv2D_6/filter", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "96"}]}}}}}, {"name": "Add_6/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "depthwise_3/filter_in", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "96"}, {"size": "1"}]}}}}}, {"name": "Add_7/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_7/filter", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/batch_normalization_11/FusedBatchNormV3_model_1/model/conv2d_9/Conv2D_model_1/model/conv2d_7/Conv2D1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "Conv2D_10/filter", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "144"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Add_10/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "144"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "depthwise_5/filter_in", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "144"}, {"size": "1"}]}}}}}, {"name": "Add_11/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "144"}]}}}}}, {"name": "Conv2D_11/filter", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "144"}, {"size": "48"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/batch_normalization_17/FusedBatchNormV3_model_1/model/conv2d_15/Conv2D_model_1/model/conv2d_11/Conv2D1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}]}}}}}, {"name": "Conv2D_16/filter", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "48"}, {"size": "288"}]}}}}}, {"name": "Add_16/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "288"}]}}}}}, {"name": "depthwise_8/filter_in", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "288"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Add_17/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "288"}]}}}}}, {"name": "Conv2D_17/filter", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "288"}, {"size": "64"}]}}}}}, {"name": "model_1/model/batch_normalization_26/FusedBatchNormV3_model_1/model/conv2d_21/Conv2D_model_1/model/conv2d_17/Conv2D1/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_22/filter", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "384"}]}}}}}, {"name": "Add_22/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}}}, {"name": "depthwise_11/filter_in", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "384"}, {"size": "1"}]}}}}}, {"name": "Add_23/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "384"}]}}}}}, {"name": "Conv2D_23/filter", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "384"}, {"size": "112"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/batch_normalization_35/FusedBatchNormV3_model_1/model/conv2d_29/Conv2D_model_1/model/conv2d_23/Conv2D1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "112"}]}}}}}, {"name": "Conv2D_30/filter", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "112"}, {"size": "672"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "Add_30/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "672"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "depthwise_15/filter_in", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "672"}, {"size": "1"}]}}}}}, {"name": "Add_31/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "672"}]}}}}}, {"name": "model_1/model/global_average_pooling2d/Mean/reduction_indices", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "Identity_3_dense/kernel", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "672"}, {"size": "63"}]}}}}}, {"name": "Identity_3_dense/bias", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "63"}]}}}}}, {"name": "input_1", "op": "Placeholder", "attr": {"dtype": {"type": "DT_FLOAT"}, "shape": {"shape": {"dim": [{"size": "1"}, {"size": "224"}, {"size": "224"}, {"size": "3"}]}}}}, {"name": "Conv2D", "op": "Conv2D", "input": ["input_1", "Conv2D/filter"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "Add", "op": "AddV2", "input": ["Conv2D", "Add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu/Relu6_model_1/model/batch_normalization/FusedBatchNormV3_model_1/model/batch_normalization_1/FusedBatchNormV3_model_1/model/depthwise_conv2d/depthwise_model_1/model/conv2d_9/Conv2D_model_1/model/conv2d/Conv2D", "op": "Relu6", "input": ["Add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "depthwise", "op": "DepthwiseConv2dNative", "input": ["model_1/model/re_lu/Relu6_model_1/model/batch_normalization/FusedBatchNormV3_model_1/model/batch_normalization_1/FusedBatchNormV3_model_1/model/depthwise_conv2d/depthwise_model_1/model/conv2d_9/Conv2D_model_1/model/conv2d/Conv2D", "depthwise/filter_in"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "Add_1", "op": "AddV2", "input": ["depthwise", "Add_1/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_1/Relu6_model_1/model/batch_normalization_1/FusedBatchNormV3_model_1/model/depthwise_conv2d/depthwise_model_1/model/conv2d_9/Conv2D", "op": "Relu6", "input": ["Add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_1", "op": "Conv2D", "input": ["model_1/model/re_lu_1/Relu6_model_1/model/batch_normalization_1/FusedBatchNormV3_model_1/model/depthwise_conv2d/depthwise_model_1/model/conv2d_9/Conv2D", "Conv2D_1/filter"], "device": "/device:CPU:0", "attr": {"padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "model_1/model/batch_normalization_2/FusedBatchNormV3_model_1/model/conv2d_5/Conv2D_model_1/model/conv2d_1/Conv2D1", "op": "AddV2", "input": ["Conv2D_1", "model_1/model/batch_normalization_2/FusedBatchNormV3_model_1/model/conv2d_5/Conv2D_model_1/model/conv2d_1/Conv2D1/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_2", "op": "Conv2D", "input": ["model_1/model/batch_normalization_2/FusedBatchNormV3_model_1/model/conv2d_5/Conv2D_model_1/model/conv2d_1/Conv2D1", "Conv2D_2/filter"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/max_pooling2d/MaxPool", "op": "MaxPool", "input": ["model_1/model/batch_normalization_2/FusedBatchNormV3_model_1/model/conv2d_5/Conv2D_model_1/model/conv2d_1/Conv2D1"], "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "T": {"type": "DT_FLOAT"}, "ksize": {"list": {"i": ["1", "2", "2", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}}}, {"name": "Add_2", "op": "AddV2", "input": ["Conv2D_2", "Add_2/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_2/Relu6_model_1/model/batch_normalization_3/FusedBatchNormV3_model_1/model/batch_normalization_4/FusedBatchNormV3_model_1/model/depthwise_conv2d_1/depthwise_model_1/model/conv2d_21/Conv2D_model_1/model/conv2d_2/Conv2D", "op": "Relu6", "input": ["Add_2"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "depthwise_1", "op": "DepthwiseConv2dNative", "input": ["model_1/model/re_lu_2/Relu6_model_1/model/batch_normalization_3/FusedBatchNormV3_model_1/model/batch_normalization_4/FusedBatchNormV3_model_1/model/depthwise_conv2d_1/depthwise_model_1/model/conv2d_21/Conv2D_model_1/model/conv2d_2/Conv2D", "depthwise_1/filter_in"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "Add_3", "op": "AddV2", "input": ["depthwise_1", "Add_3/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_3/Relu6_model_1/model/batch_normalization_4/FusedBatchNormV3_model_1/model/depthwise_conv2d_1/depthwise_model_1/model/conv2d_21/Conv2D", "op": "Relu6", "input": ["Add_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_3", "op": "Conv2D", "input": ["model_1/model/re_lu_3/Relu6_model_1/model/batch_normalization_4/FusedBatchNormV3_model_1/model/depthwise_conv2d_1/depthwise_model_1/model/conv2d_21/Conv2D", "Conv2D_3/filter"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "model_1/model/batch_normalization_5/FusedBatchNormV3_model_1/model/conv2d_5/Conv2D_model_1/model/conv2d_3/Conv2D1", "op": "AddV2", "input": ["Conv2D_3", "model_1/model/batch_normalization_5/FusedBatchNormV3_model_1/model/conv2d_5/Conv2D_model_1/model/conv2d_3/Conv2D1/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/add/add", "op": "AddV2", "input": ["model_1/model/batch_normalization_5/FusedBatchNormV3_model_1/model/conv2d_5/Conv2D_model_1/model/conv2d_3/Conv2D1", "model_1/model/max_pooling2d/MaxPool"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_4", "op": "Conv2D", "input": ["model_1/model/add/add", "Conv2D_4/filter"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "Add_4", "op": "AddV2", "input": ["Conv2D_4", "Add_4/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_4/Relu6_model_1/model/batch_normalization_6/FusedBatchNormV3_model_1/model/batch_normalization_10/FusedBatchNormV3_model_1/model/depthwise_conv2d_3/depthwise_model_1/model/conv2d_4/Conv2D", "op": "Relu6", "input": ["Add_4"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "depthwise_2", "op": "DepthwiseConv2dNative", "input": ["model_1/model/re_lu_4/Relu6_model_1/model/batch_normalization_6/FusedBatchNormV3_model_1/model/batch_normalization_10/FusedBatchNormV3_model_1/model/depthwise_conv2d_3/depthwise_model_1/model/conv2d_4/Conv2D", "depthwise_2/filter_in"], "attr": {"padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "Add_5", "op": "AddV2", "input": ["depthwise_2", "Add_5/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_5/Relu6_model_1/model/batch_normalization_7/FusedBatchNormV3_model_1/model/batch_normalization_10/FusedBatchNormV3_model_1/model/depthwise_conv2d_3/depthwise_model_1/model/depthwise_conv2d_2/depthwise", "op": "Relu6", "input": ["Add_5"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_5", "op": "Conv2D", "input": ["model_1/model/re_lu_5/Relu6_model_1/model/batch_normalization_7/FusedBatchNormV3_model_1/model/batch_normalization_10/FusedBatchNormV3_model_1/model/depthwise_conv2d_3/depthwise_model_1/model/depthwise_conv2d_2/depthwise", "Conv2D_5/filter"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}}}, {"name": "model_1/model/add_1/ArithmeticOptimizer/AddOpsRewrite_Leaf_1_add", "op": "AddN", "input": ["Conv2D_5", "model_1/model/add/add"], "attr": {"N": {"i": "2"}, "_grappler_ArithmeticOptimizer_AddOpsRewriteStage": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/add_1/ArithmeticOptimizer/AddOpsRewrite_add", "op": "AddV2", "input": ["model_1/model/batch_normalization_8/FusedBatchNormV3_model_1/model/conv2d_5/Conv2D1/y", "model_1/model/add_1/ArithmeticOptimizer/AddOpsRewrite_Leaf_1_add"], "attr": {"_grappler_ArithmeticOptimizer_AddOpsRewriteStage": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_6", "op": "Conv2D", "input": ["model_1/model/add_1/ArithmeticOptimizer/AddOpsRewrite_add", "Conv2D_6/filter"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "Add_6", "op": "AddV2", "input": ["Conv2D_6", "Add_6/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_6/Relu6_model_1/model/batch_normalization_9/FusedBatchNormV3_model_1/model/batch_normalization_10/FusedBatchNormV3_model_1/model/depthwise_conv2d_3/depthwise_model_1/model/conv2d_6/Conv2D", "op": "Relu6", "input": ["Add_6"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "depthwise_3", "op": "DepthwiseConv2dNative", "input": ["model_1/model/re_lu_6/Relu6_model_1/model/batch_normalization_9/FusedBatchNormV3_model_1/model/batch_normalization_10/FusedBatchNormV3_model_1/model/depthwise_conv2d_3/depthwise_model_1/model/conv2d_6/Conv2D", "depthwise_3/filter_in"], "attr": {"T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}}}, {"name": "Add_7", "op": "AddV2", "input": ["depthwise_3", "Add_7/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_7/Relu6_model_1/model/batch_normalization_10/FusedBatchNormV3_model_1/model/depthwise_conv2d_3/depthwise", "op": "Relu6", "input": ["Add_7"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_7", "op": "Conv2D", "input": ["model_1/model/re_lu_7/Relu6_model_1/model/batch_normalization_10/FusedBatchNormV3_model_1/model/depthwise_conv2d_3/depthwise", "Conv2D_7/filter"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "model_1/model/batch_normalization_11/FusedBatchNormV3_model_1/model/conv2d_9/Conv2D_model_1/model/conv2d_7/Conv2D1", "op": "AddV2", "input": ["Conv2D_7", "model_1/model/batch_normalization_11/FusedBatchNormV3_model_1/model/conv2d_9/Conv2D_model_1/model/conv2d_7/Conv2D1/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_8", "op": "Conv2D", "input": ["model_1/model/batch_normalization_11/FusedBatchNormV3_model_1/model/conv2d_9/Conv2D_model_1/model/conv2d_7/Conv2D1", "Conv2D_8/filter"], "device": "/device:CPU:0", "attr": {"padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}}}, {"name": "Add_8", "op": "AddV2", "input": ["Conv2D_8", "Add_8/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_8/Relu6_model_1/model/batch_normalization_12/FusedBatchNormV3_model_1/model/batch_normalization_16/FusedBatchNormV3_model_1/model/depthwise_conv2d_5/depthwise_model_1/model/conv2d_8/Conv2D", "op": "Relu6", "input": ["Add_8"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "depthwise_4", "op": "DepthwiseConv2dNative", "input": ["model_1/model/re_lu_8/Relu6_model_1/model/batch_normalization_12/FusedBatchNormV3_model_1/model/batch_normalization_16/FusedBatchNormV3_model_1/model/depthwise_conv2d_5/depthwise_model_1/model/conv2d_8/Conv2D", "depthwise_4/filter_in"], "attr": {"T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "Add_9", "op": "AddV2", "input": ["depthwise_4", "Add_9/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_9/Relu6_model_1/model/batch_normalization_13/FusedBatchNormV3_model_1/model/batch_normalization_16/FusedBatchNormV3_model_1/model/depthwise_conv2d_5/depthwise_model_1/model/depthwise_conv2d_4/depthwise", "op": "Relu6", "input": ["Add_9"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_9", "op": "Conv2D", "input": ["model_1/model/re_lu_9/Relu6_model_1/model/batch_normalization_13/FusedBatchNormV3_model_1/model/batch_normalization_16/FusedBatchNormV3_model_1/model/depthwise_conv2d_5/depthwise_model_1/model/depthwise_conv2d_4/depthwise", "Conv2D_9/filter"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "model_1/model/add_2/ArithmeticOptimizer/AddOpsRewrite_Leaf_1_add", "op": "AddN", "input": ["Conv2D_9", "model_1/model/batch_normalization_11/FusedBatchNormV3_model_1/model/conv2d_9/Conv2D_model_1/model/conv2d_7/Conv2D1"], "attr": {"_grappler_ArithmeticOptimizer_AddOpsRewriteStage": {"b": true}, "T": {"type": "DT_FLOAT"}, "N": {"i": "2"}}}, {"name": "model_1/model/add_2/ArithmeticOptimizer/AddOpsRewrite_add", "op": "AddV2", "input": ["model_1/model/batch_normalization_14/FusedBatchNormV3_model_1/model/conv2d_9/Conv2D1/y", "model_1/model/add_2/ArithmeticOptimizer/AddOpsRewrite_Leaf_1_add"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_AddOpsRewriteStage": {"b": true}}}, {"name": "Conv2D_10", "op": "Conv2D", "input": ["model_1/model/add_2/ArithmeticOptimizer/AddOpsRewrite_add", "Conv2D_10/filter"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "Add_10", "op": "AddV2", "input": ["Conv2D_10", "Add_10/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_10/Relu6_model_1/model/batch_normalization_15/FusedBatchNormV3_model_1/model/batch_normalization_16/FusedBatchNormV3_model_1/model/depthwise_conv2d_5/depthwise_model_1/model/conv2d_10/Conv2D", "op": "Relu6", "input": ["Add_10"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "depthwise_5", "op": "DepthwiseConv2dNative", "input": ["model_1/model/re_lu_10/Relu6_model_1/model/batch_normalization_15/FusedBatchNormV3_model_1/model/batch_normalization_16/FusedBatchNormV3_model_1/model/depthwise_conv2d_5/depthwise_model_1/model/conv2d_10/Conv2D", "depthwise_5/filter_in"], "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "Add_11", "op": "AddV2", "input": ["depthwise_5", "Add_11/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_11/Relu6_model_1/model/batch_normalization_16/FusedBatchNormV3_model_1/model/depthwise_conv2d_5/depthwise", "op": "Relu6", "input": ["Add_11"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_11", "op": "Conv2D", "input": ["model_1/model/re_lu_11/Relu6_model_1/model/batch_normalization_16/FusedBatchNormV3_model_1/model/depthwise_conv2d_5/depthwise", "Conv2D_11/filter"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "model_1/model/batch_normalization_17/FusedBatchNormV3_model_1/model/conv2d_15/Conv2D_model_1/model/conv2d_11/Conv2D1", "op": "AddV2", "input": ["Conv2D_11", "model_1/model/batch_normalization_17/FusedBatchNormV3_model_1/model/conv2d_15/Conv2D_model_1/model/conv2d_11/Conv2D1/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_12", "op": "Conv2D", "input": ["model_1/model/batch_normalization_17/FusedBatchNormV3_model_1/model/conv2d_15/Conv2D_model_1/model/conv2d_11/Conv2D1", "Conv2D_12/filter"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "Add_12", "op": "AddV2", "input": ["Conv2D_12", "Add_12/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_12/Relu6_model_1/model/batch_normalization_18/FusedBatchNormV3_model_1/model/batch_normalization_25/FusedBatchNormV3_model_1/model/depthwise_conv2d_8/depthwise_model_1/model/conv2d_12/Conv2D", "op": "Relu6", "input": ["Add_12"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "depthwise_6", "op": "DepthwiseConv2dNative", "input": ["model_1/model/re_lu_12/Relu6_model_1/model/batch_normalization_18/FusedBatchNormV3_model_1/model/batch_normalization_25/FusedBatchNormV3_model_1/model/depthwise_conv2d_8/depthwise_model_1/model/conv2d_12/Conv2D", "depthwise_6/filter_in"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "Add_13", "op": "AddV2", "input": ["depthwise_6", "Add_13/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_13/Relu6_model_1/model/batch_normalization_19/FusedBatchNormV3_model_1/model/batch_normalization_25/FusedBatchNormV3_model_1/model/depthwise_conv2d_8/depthwise_model_1/model/depthwise_conv2d_6/depthwise", "op": "Relu6", "input": ["Add_13"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_13", "op": "Conv2D", "input": ["model_1/model/re_lu_13/Relu6_model_1/model/batch_normalization_19/FusedBatchNormV3_model_1/model/batch_normalization_25/FusedBatchNormV3_model_1/model/depthwise_conv2d_8/depthwise_model_1/model/depthwise_conv2d_6/depthwise", "Conv2D_13/filter"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "model_1/model/add_3/ArithmeticOptimizer/AddOpsRewrite_Leaf_1_add", "op": "AddN", "input": ["Conv2D_13", "model_1/model/batch_normalization_17/FusedBatchNormV3_model_1/model/conv2d_15/Conv2D_model_1/model/conv2d_11/Conv2D1"], "attr": {"N": {"i": "2"}, "_grappler_ArithmeticOptimizer_AddOpsRewriteStage": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/add_3/ArithmeticOptimizer/AddOpsRewrite_add", "op": "AddV2", "input": ["model_1/model/batch_normalization_20/FusedBatchNormV3_model_1/model/conv2d_15/Conv2D_model_1/model/conv2d_13/Conv2D1/y", "model_1/model/add_3/ArithmeticOptimizer/AddOpsRewrite_Leaf_1_add"], "attr": {"_grappler_ArithmeticOptimizer_AddOpsRewriteStage": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_14", "op": "Conv2D", "input": ["model_1/model/add_3/ArithmeticOptimizer/AddOpsRewrite_add", "Conv2D_14/filter"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "Add_14", "op": "AddV2", "input": ["Conv2D_14", "Add_14/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_14/Relu6_model_1/model/batch_normalization_21/FusedBatchNormV3_model_1/model/batch_normalization_25/FusedBatchNormV3_model_1/model/depthwise_conv2d_8/depthwise_model_1/model/conv2d_14/Conv2D", "op": "Relu6", "input": ["Add_14"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "depthwise_7", "op": "DepthwiseConv2dNative", "input": ["model_1/model/re_lu_14/Relu6_model_1/model/batch_normalization_21/FusedBatchNormV3_model_1/model/batch_normalization_25/FusedBatchNormV3_model_1/model/depthwise_conv2d_8/depthwise_model_1/model/conv2d_14/Conv2D", "depthwise_7/filter_in"], "attr": {"T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "Add_15", "op": "AddV2", "input": ["depthwise_7", "Add_15/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_15/Relu6_model_1/model/batch_normalization_22/FusedBatchNormV3_model_1/model/batch_normalization_25/FusedBatchNormV3_model_1/model/depthwise_conv2d_8/depthwise_model_1/model/depthwise_conv2d_7/depthwise", "op": "Relu6", "input": ["Add_15"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_15", "op": "Conv2D", "input": ["model_1/model/re_lu_15/Relu6_model_1/model/batch_normalization_22/FusedBatchNormV3_model_1/model/batch_normalization_25/FusedBatchNormV3_model_1/model/depthwise_conv2d_8/depthwise_model_1/model/depthwise_conv2d_7/depthwise", "Conv2D_15/filter"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "model_1/model/add_4/ArithmeticOptimizer/AddOpsRewrite_Leaf_1_add", "op": "AddN", "input": ["Conv2D_15", "model_1/model/add_3/ArithmeticOptimizer/AddOpsRewrite_add"], "attr": {"T": {"type": "DT_FLOAT"}, "N": {"i": "2"}, "_grappler_ArithmeticOptimizer_AddOpsRewriteStage": {"b": true}}}, {"name": "model_1/model/add_4/ArithmeticOptimizer/AddOpsRewrite_add", "op": "AddV2", "input": ["model_1/model/batch_normalization_23/FusedBatchNormV3_model_1/model/conv2d_15/Conv2D1/y", "model_1/model/add_4/ArithmeticOptimizer/AddOpsRewrite_Leaf_1_add"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_AddOpsRewriteStage": {"b": true}}}, {"name": "Conv2D_16", "op": "Conv2D", "input": ["model_1/model/add_4/ArithmeticOptimizer/AddOpsRewrite_add", "Conv2D_16/filter"], "device": "/device:CPU:0", "attr": {"padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}}}, {"name": "Add_16", "op": "AddV2", "input": ["Conv2D_16", "Add_16/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_16/Relu6_model_1/model/batch_normalization_24/FusedBatchNormV3_model_1/model/batch_normalization_25/FusedBatchNormV3_model_1/model/depthwise_conv2d_8/depthwise_model_1/model/conv2d_16/Conv2D", "op": "Relu6", "input": ["Add_16"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "depthwise_8", "op": "DepthwiseConv2dNative", "input": ["model_1/model/re_lu_16/Relu6_model_1/model/batch_normalization_24/FusedBatchNormV3_model_1/model/batch_normalization_25/FusedBatchNormV3_model_1/model/depthwise_conv2d_8/depthwise_model_1/model/conv2d_16/Conv2D", "depthwise_8/filter_in"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "Add_17", "op": "AddV2", "input": ["depthwise_8", "Add_17/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_17/Relu6_model_1/model/batch_normalization_25/FusedBatchNormV3_model_1/model/depthwise_conv2d_8/depthwise", "op": "Relu6", "input": ["Add_17"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_17", "op": "Conv2D", "input": ["model_1/model/re_lu_17/Relu6_model_1/model/batch_normalization_25/FusedBatchNormV3_model_1/model/depthwise_conv2d_8/depthwise", "Conv2D_17/filter"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "model_1/model/batch_normalization_26/FusedBatchNormV3_model_1/model/conv2d_21/Conv2D_model_1/model/conv2d_17/Conv2D1", "op": "AddV2", "input": ["Conv2D_17", "model_1/model/batch_normalization_26/FusedBatchNormV3_model_1/model/conv2d_21/Conv2D_model_1/model/conv2d_17/Conv2D1/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_18", "op": "Conv2D", "input": ["model_1/model/batch_normalization_26/FusedBatchNormV3_model_1/model/conv2d_21/Conv2D_model_1/model/conv2d_17/Conv2D1", "Conv2D_18/filter"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "Add_18", "op": "AddV2", "input": ["Conv2D_18", "Add_18/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_18/Relu6_model_1/model/batch_normalization_27/FusedBatchNormV3_model_1/model/batch_normalization_34/FusedBatchNormV3_model_1/model/depthwise_conv2d_11/depthwise_model_1/model/conv2d_18/Conv2D", "op": "Relu6", "input": ["Add_18"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "depthwise_9", "op": "DepthwiseConv2dNative", "input": ["model_1/model/re_lu_18/Relu6_model_1/model/batch_normalization_27/FusedBatchNormV3_model_1/model/batch_normalization_34/FusedBatchNormV3_model_1/model/depthwise_conv2d_11/depthwise_model_1/model/conv2d_18/Conv2D", "depthwise_9/filter_in"], "attr": {"explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "Add_19", "op": "AddV2", "input": ["depthwise_9", "Add_19/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_19/Relu6_model_1/model/batch_normalization_28/FusedBatchNormV3_model_1/model/batch_normalization_34/FusedBatchNormV3_model_1/model/depthwise_conv2d_11/depthwise_model_1/model/depthwise_conv2d_9/depthwise", "op": "Relu6", "input": ["Add_19"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_19", "op": "Conv2D", "input": ["model_1/model/re_lu_19/Relu6_model_1/model/batch_normalization_28/FusedBatchNormV3_model_1/model/batch_normalization_34/FusedBatchNormV3_model_1/model/depthwise_conv2d_11/depthwise_model_1/model/depthwise_conv2d_9/depthwise", "Conv2D_19/filter"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/add_5/ArithmeticOptimizer/AddOpsRewrite_Leaf_1_add", "op": "AddN", "input": ["Conv2D_19", "model_1/model/batch_normalization_26/FusedBatchNormV3_model_1/model/conv2d_21/Conv2D_model_1/model/conv2d_17/Conv2D1"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_AddOpsRewriteStage": {"b": true}, "N": {"i": "2"}}}, {"name": "model_1/model/add_5/ArithmeticOptimizer/AddOpsRewrite_add", "op": "AddV2", "input": ["model_1/model/batch_normalization_29/FusedBatchNormV3_model_1/model/conv2d_21/Conv2D_model_1/model/conv2d_19/Conv2D1/y", "model_1/model/add_5/ArithmeticOptimizer/AddOpsRewrite_Leaf_1_add"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_AddOpsRewriteStage": {"b": true}}}, {"name": "Conv2D_20", "op": "Conv2D", "input": ["model_1/model/add_5/ArithmeticOptimizer/AddOpsRewrite_add", "Conv2D_20/filter"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "Add_20", "op": "AddV2", "input": ["Conv2D_20", "Add_20/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_20/Relu6_model_1/model/batch_normalization_30/FusedBatchNormV3_model_1/model/batch_normalization_34/FusedBatchNormV3_model_1/model/depthwise_conv2d_11/depthwise_model_1/model/conv2d_20/Conv2D", "op": "Relu6", "input": ["Add_20"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "depthwise_10", "op": "DepthwiseConv2dNative", "input": ["model_1/model/re_lu_20/Relu6_model_1/model/batch_normalization_30/FusedBatchNormV3_model_1/model/batch_normalization_34/FusedBatchNormV3_model_1/model/depthwise_conv2d_11/depthwise_model_1/model/conv2d_20/Conv2D", "depthwise_10/filter_in"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}}}, {"name": "Add_21", "op": "AddV2", "input": ["depthwise_10", "Add_21/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_21/Relu6_model_1/model/batch_normalization_31/FusedBatchNormV3_model_1/model/batch_normalization_34/FusedBatchNormV3_model_1/model/depthwise_conv2d_11/depthwise_model_1/model/depthwise_conv2d_10/depthwise", "op": "Relu6", "input": ["Add_21"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_21", "op": "Conv2D", "input": ["model_1/model/re_lu_21/Relu6_model_1/model/batch_normalization_31/FusedBatchNormV3_model_1/model/batch_normalization_34/FusedBatchNormV3_model_1/model/depthwise_conv2d_11/depthwise_model_1/model/depthwise_conv2d_10/depthwise", "Conv2D_21/filter"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/add_6/ArithmeticOptimizer/AddOpsRewrite_Leaf_1_add", "op": "AddN", "input": ["Conv2D_21", "model_1/model/add_5/ArithmeticOptimizer/AddOpsRewrite_add"], "attr": {"_grappler_ArithmeticOptimizer_AddOpsRewriteStage": {"b": true}, "T": {"type": "DT_FLOAT"}, "N": {"i": "2"}}}, {"name": "model_1/model/add_6/ArithmeticOptimizer/AddOpsRewrite_add", "op": "AddV2", "input": ["model_1/model/batch_normalization_32/FusedBatchNormV3_model_1/model/conv2d_21/Conv2D1/y", "model_1/model/add_6/ArithmeticOptimizer/AddOpsRewrite_Leaf_1_add"], "attr": {"_grappler_ArithmeticOptimizer_AddOpsRewriteStage": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_22", "op": "Conv2D", "input": ["model_1/model/add_6/ArithmeticOptimizer/AddOpsRewrite_add", "Conv2D_22/filter"], "device": "/device:CPU:0", "attr": {"padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "Add_22", "op": "AddV2", "input": ["Conv2D_22", "Add_22/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_22/Relu6_model_1/model/batch_normalization_33/FusedBatchNormV3_model_1/model/batch_normalization_34/FusedBatchNormV3_model_1/model/depthwise_conv2d_11/depthwise_model_1/model/conv2d_22/Conv2D", "op": "Relu6", "input": ["Add_22"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "depthwise_11", "op": "DepthwiseConv2dNative", "input": ["model_1/model/re_lu_22/Relu6_model_1/model/batch_normalization_33/FusedBatchNormV3_model_1/model/batch_normalization_34/FusedBatchNormV3_model_1/model/depthwise_conv2d_11/depthwise_model_1/model/conv2d_22/Conv2D", "depthwise_11/filter_in"], "attr": {"explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}}}, {"name": "Add_23", "op": "AddV2", "input": ["depthwise_11", "Add_23/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_23/Relu6_model_1/model/batch_normalization_34/FusedBatchNormV3_model_1/model/depthwise_conv2d_11/depthwise", "op": "Relu6", "input": ["Add_23"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_23", "op": "Conv2D", "input": ["model_1/model/re_lu_23/Relu6_model_1/model/batch_normalization_34/FusedBatchNormV3_model_1/model/depthwise_conv2d_11/depthwise", "Conv2D_23/filter"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "model_1/model/batch_normalization_35/FusedBatchNormV3_model_1/model/conv2d_29/Conv2D_model_1/model/conv2d_23/Conv2D1", "op": "AddV2", "input": ["Conv2D_23", "model_1/model/batch_normalization_35/FusedBatchNormV3_model_1/model/conv2d_29/Conv2D_model_1/model/conv2d_23/Conv2D1/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_24", "op": "Conv2D", "input": ["model_1/model/batch_normalization_35/FusedBatchNormV3_model_1/model/conv2d_29/Conv2D_model_1/model/conv2d_23/Conv2D1", "Conv2D_24/filter"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}}}, {"name": "Add_24", "op": "AddV2", "input": ["Conv2D_24", "Add_24/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_24/Relu6_model_1/model/batch_normalization_36/FusedBatchNormV3_model_1/model/batch_normalization_46/FusedBatchNormV3_model_1/model/depthwise_conv2d_15/depthwise_model_1/model/conv2d_24/Conv2D", "op": "Relu6", "input": ["Add_24"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "depthwise_12", "op": "DepthwiseConv2dNative", "input": ["model_1/model/re_lu_24/Relu6_model_1/model/batch_normalization_36/FusedBatchNormV3_model_1/model/batch_normalization_46/FusedBatchNormV3_model_1/model/depthwise_conv2d_15/depthwise_model_1/model/conv2d_24/Conv2D", "depthwise_12/filter_in"], "attr": {"padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "Add_25", "op": "AddV2", "input": ["depthwise_12", "Add_25/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_25/Relu6_model_1/model/batch_normalization_37/FusedBatchNormV3_model_1/model/depthwise_conv2d_12/depthwise_model_1/model/depthwise_conv2d_15/depthwise", "op": "Relu6", "input": ["Add_25"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_25", "op": "Conv2D", "input": ["model_1/model/re_lu_25/Relu6_model_1/model/batch_normalization_37/FusedBatchNormV3_model_1/model/depthwise_conv2d_12/depthwise_model_1/model/depthwise_conv2d_15/depthwise", "Conv2D_25/filter"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "model_1/model/add_7/ArithmeticOptimizer/AddOpsRewrite_Leaf_1_add", "op": "AddN", "input": ["Conv2D_25", "model_1/model/batch_normalization_35/FusedBatchNormV3_model_1/model/conv2d_29/Conv2D_model_1/model/conv2d_23/Conv2D1"], "attr": {"_grappler_ArithmeticOptimizer_AddOpsRewriteStage": {"b": true}, "N": {"i": "2"}, "T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/add_7/ArithmeticOptimizer/AddOpsRewrite_add", "op": "AddV2", "input": ["model_1/model/batch_normalization_38/FusedBatchNormV3_model_1/model/conv2d_29/Conv2D_model_1/model/conv2d_25/Conv2D1/y", "model_1/model/add_7/ArithmeticOptimizer/AddOpsRewrite_Leaf_1_add"], "attr": {"_grappler_ArithmeticOptimizer_AddOpsRewriteStage": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_26", "op": "Conv2D", "input": ["model_1/model/add_7/ArithmeticOptimizer/AddOpsRewrite_add", "Conv2D_26/filter"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}}}, {"name": "Add_26", "op": "AddV2", "input": ["Conv2D_26", "Add_26/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_26/Relu6_model_1/model/batch_normalization_39/FusedBatchNormV3_model_1/model/batch_normalization_46/FusedBatchNormV3_model_1/model/depthwise_conv2d_15/depthwise_model_1/model/conv2d_26/Conv2D", "op": "Relu6", "input": ["Add_26"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "depthwise_13", "op": "DepthwiseConv2dNative", "input": ["model_1/model/re_lu_26/Relu6_model_1/model/batch_normalization_39/FusedBatchNormV3_model_1/model/batch_normalization_46/FusedBatchNormV3_model_1/model/depthwise_conv2d_15/depthwise_model_1/model/conv2d_26/Conv2D", "depthwise_13/filter_in"], "attr": {"data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}}}, {"name": "Add_27", "op": "AddV2", "input": ["depthwise_13", "Add_27/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_27/Relu6_model_1/model/batch_normalization_40/FusedBatchNormV3_model_1/model/depthwise_conv2d_13/depthwise_model_1/model/depthwise_conv2d_15/depthwise", "op": "Relu6", "input": ["Add_27"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_27", "op": "Conv2D", "input": ["model_1/model/re_lu_27/Relu6_model_1/model/batch_normalization_40/FusedBatchNormV3_model_1/model/depthwise_conv2d_13/depthwise_model_1/model/depthwise_conv2d_15/depthwise", "Conv2D_27/filter"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "model_1/model/add_8/ArithmeticOptimizer/AddOpsRewrite_Leaf_1_add", "op": "AddN", "input": ["Conv2D_27", "model_1/model/add_7/ArithmeticOptimizer/AddOpsRewrite_add"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_AddOpsRewriteStage": {"b": true}, "N": {"i": "2"}}}, {"name": "model_1/model/add_8/ArithmeticOptimizer/AddOpsRewrite_add", "op": "AddV2", "input": ["model_1/model/batch_normalization_41/FusedBatchNormV3_model_1/model/conv2d_29/Conv2D_model_1/model/conv2d_27/Conv2D1/y", "model_1/model/add_8/ArithmeticOptimizer/AddOpsRewrite_Leaf_1_add"], "attr": {"_grappler_ArithmeticOptimizer_AddOpsRewriteStage": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_28", "op": "Conv2D", "input": ["model_1/model/add_8/ArithmeticOptimizer/AddOpsRewrite_add", "Conv2D_28/filter"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "Add_28", "op": "AddV2", "input": ["Conv2D_28", "Add_28/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_28/Relu6_model_1/model/batch_normalization_42/FusedBatchNormV3_model_1/model/batch_normalization_46/FusedBatchNormV3_model_1/model/depthwise_conv2d_15/depthwise_model_1/model/conv2d_28/Conv2D", "op": "Relu6", "input": ["Add_28"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "depthwise_14", "op": "DepthwiseConv2dNative", "input": ["model_1/model/re_lu_28/Relu6_model_1/model/batch_normalization_42/FusedBatchNormV3_model_1/model/batch_normalization_46/FusedBatchNormV3_model_1/model/depthwise_conv2d_15/depthwise_model_1/model/conv2d_28/Conv2D", "depthwise_14/filter_in"], "attr": {"padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "Add_29", "op": "AddV2", "input": ["depthwise_14", "Add_29/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_29/Relu6_model_1/model/batch_normalization_43/FusedBatchNormV3_model_1/model/depthwise_conv2d_14/depthwise_model_1/model/depthwise_conv2d_15/depthwise", "op": "Relu6", "input": ["Add_29"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Conv2D_29", "op": "Conv2D", "input": ["model_1/model/re_lu_29/Relu6_model_1/model/batch_normalization_43/FusedBatchNormV3_model_1/model/depthwise_conv2d_14/depthwise_model_1/model/depthwise_conv2d_15/depthwise", "Conv2D_29/filter"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "model_1/model/add_9/ArithmeticOptimizer/AddOpsRewrite_Leaf_1_add", "op": "AddN", "input": ["Conv2D_29", "model_1/model/add_8/ArithmeticOptimizer/AddOpsRewrite_add"], "attr": {"N": {"i": "2"}, "_grappler_ArithmeticOptimizer_AddOpsRewriteStage": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/add_9/ArithmeticOptimizer/AddOpsRewrite_add", "op": "AddV2", "input": ["model_1/model/batch_normalization_44/FusedBatchNormV3_model_1/model/conv2d_29/Conv2D1/y", "model_1/model/add_9/ArithmeticOptimizer/AddOpsRewrite_Leaf_1_add"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_AddOpsRewriteStage": {"b": true}}}, {"name": "Conv2D_30", "op": "Conv2D", "input": ["model_1/model/add_9/ArithmeticOptimizer/AddOpsRewrite_add", "Conv2D_30/filter"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "Add_30", "op": "AddV2", "input": ["Conv2D_30", "Add_30/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_30/Relu6_model_1/model/batch_normalization_45/FusedBatchNormV3_model_1/model/batch_normalization_46/FusedBatchNormV3_model_1/model/depthwise_conv2d_15/depthwise_model_1/model/conv2d_30/Conv2D", "op": "Relu6", "input": ["Add_30"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "depthwise_15", "op": "DepthwiseConv2dNative", "input": ["model_1/model/re_lu_30/Relu6_model_1/model/batch_normalization_45/FusedBatchNormV3_model_1/model/batch_normalization_46/FusedBatchNormV3_model_1/model/depthwise_conv2d_15/depthwise_model_1/model/conv2d_30/Conv2D", "depthwise_15/filter_in"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}}}, {"name": "Add_31", "op": "AddV2", "input": ["depthwise_15", "Add_31/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/re_lu_31/Relu6_model_1/model/batch_normalization_46/FusedBatchNormV3_model_1/model/depthwise_conv2d_15/depthwise", "op": "Relu6", "input": ["Add_31"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "model_1/model/global_average_pooling2d/Mean", "op": "Mean", "input": ["model_1/model/re_lu_31/Relu6_model_1/model/batch_normalization_46/FusedBatchNormV3_model_1/model/depthwise_conv2d_15/depthwise", "model_1/model/global_average_pooling2d/Mean/reduction_indices"], "attr": {"T": {"type": "DT_FLOAT"}, "keep_dims": {"b": false}, "Tidx": {"type": "DT_INT32"}}}, {"name": "Identity_dense/BiasAdd", "op": "_FusedMatMul", "input": ["model_1/model/global_average_pooling2d/Mean", "Identity_dense/kernel", "Identity_dense/bias"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "transpose_b": {"b": false}, "epsilon": {"f": 0}, "transpose_a": {"b": false}}}, {"name": "model_1/model/conv_handflag/MatMul_model_1/model/conv_handflag/BiasAdd_dense/BiasAdd", "op": "_FusedMatMul", "input": ["model_1/model/global_average_pooling2d/Mean", "model_1/model/conv_handflag/MatMul_model_1/model/conv_handflag/BiasAdd_dense/kernel", "model_1/model/conv_handflag/MatMul_model_1/model/conv_handflag/BiasAdd_dense/bias"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "transpose_b": {"b": false}, "num_args": {"i": "1"}, "transpose_a": {"b": false}}}, {"name": "Identity_3_dense/BiasAdd", "op": "_FusedMatMul", "input": ["model_1/model/global_average_pooling2d/Mean", "Identity_3_dense/kernel", "Identity_3_dense/bias"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "T": {"type": "DT_FLOAT"}, "transpose_b": {"b": false}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "transpose_a": {"b": false}}}, {"name": "model_1/model/conv_handedness/MatMul_model_1/model/conv_handedness/BiasAdd_dense/BiasAdd", "op": "_FusedMatMul", "input": ["model_1/model/global_average_pooling2d/Mean", "model_1/model/conv_handedness/MatMul_model_1/model/conv_handedness/BiasAdd_dense/kernel", "model_1/model/conv_handedness/MatMul_model_1/model/conv_handedness/BiasAdd_dense/bias"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "transpose_a": {"b": false}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}}}, {"name": "Identity", "op": "Identity", "input": ["Identity_dense/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity_1", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["model_1/model/conv_handflag/MatMul_model_1/model/conv_handflag/BiasAdd_dense/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity_3", "op": "Identity", "input": ["Identity_3_dense/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity_2", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["model_1/model/conv_handedness/MatMul_model_1/model/conv_handedness/BiasAdd_dense/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}], "library": {}, "versions": {"producer": 808}}, "weightsManifest": [{"paths": ["handlandmark-lite.bin"], "weights": [{"name": "Identity_dense/kernel", "shape": [672, 63], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Identity_dense/bias", "shape": [63], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "model_1/model/conv_handflag/MatMul_model_1/model/conv_handflag/BiasAdd_dense/kernel", "shape": [672, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "model_1/model/conv_handflag/MatMul_model_1/model/conv_handflag/BiasAdd_dense/bias", "shape": [1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "model_1/model/conv_handedness/MatMul_model_1/model/conv_handedness/BiasAdd_dense/kernel", "shape": [672, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "model_1/model/conv_handedness/MatMul_model_1/model/conv_handedness/BiasAdd_dense/bias", "shape": [1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "model_1/model/batch_normalization_44/FusedBatchNormV3_model_1/model/conv2d_29/Conv2D1/y", "shape": [112], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_28/filter", "shape": [1, 1, 112, 672], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_28/y", "shape": [672], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "depthwise_14/filter_in", "shape": [5, 5, 672, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_29/y", "shape": [672], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_29/filter", "shape": [1, 1, 672, 112], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "model_1/model/batch_normalization_41/FusedBatchNormV3_model_1/model/conv2d_29/Conv2D_model_1/model/conv2d_27/Conv2D1/y", "shape": [112], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_26/filter", "shape": [1, 1, 112, 672], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_26/y", "shape": [672], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "depthwise_13/filter_in", "shape": [5, 5, 672, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_27/y", "shape": [672], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_27/filter", "shape": [1, 1, 672, 112], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "model_1/model/batch_normalization_38/FusedBatchNormV3_model_1/model/conv2d_29/Conv2D_model_1/model/conv2d_25/Conv2D1/y", "shape": [112], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_24/filter", "shape": [1, 1, 112, 672], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_24/y", "shape": [672], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "depthwise_12/filter_in", "shape": [5, 5, 672, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_25/y", "shape": [672], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_25/filter", "shape": [1, 1, 672, 112], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "model_1/model/batch_normalization_32/FusedBatchNormV3_model_1/model/conv2d_21/Conv2D1/y", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_20/filter", "shape": [1, 1, 64, 384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_20/y", "shape": [384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "depthwise_10/filter_in", "shape": [5, 5, 384, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_21/y", "shape": [384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_21/filter", "shape": [1, 1, 384, 64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "model_1/model/batch_normalization_29/FusedBatchNormV3_model_1/model/conv2d_21/Conv2D_model_1/model/conv2d_19/Conv2D1/y", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_18/filter", "shape": [1, 1, 64, 384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_18/y", "shape": [384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "depthwise_9/filter_in", "shape": [5, 5, 384, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_19/y", "shape": [384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_19/filter", "shape": [1, 1, 384, 64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "model_1/model/batch_normalization_23/FusedBatchNormV3_model_1/model/conv2d_15/Conv2D1/y", "shape": [48], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_14/filter", "shape": [1, 1, 48, 288], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_14/y", "shape": [288], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "depthwise_7/filter_in", "shape": [3, 3, 288, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_15/y", "shape": [288], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_15/filter", "shape": [1, 1, 288, 48], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "model_1/model/batch_normalization_20/FusedBatchNormV3_model_1/model/conv2d_15/Conv2D_model_1/model/conv2d_13/Conv2D1/y", "shape": [48], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_12/filter", "shape": [1, 1, 48, 288], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_12/y", "shape": [288], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "depthwise_6/filter_in", "shape": [3, 3, 288, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_13/y", "shape": [288], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_13/filter", "shape": [1, 1, 288, 48], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "model_1/model/batch_normalization_14/FusedBatchNormV3_model_1/model/conv2d_9/Conv2D1/y", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_8/filter", "shape": [1, 1, 24, 144], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_8/y", "shape": [144], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "depthwise_4/filter_in", "shape": [5, 5, 144, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_9/y", "shape": [144], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_9/filter", "shape": [1, 1, 144, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "model_1/model/batch_normalization_8/FusedBatchNormV3_model_1/model/conv2d_5/Conv2D1/y", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_4/filter", "shape": [1, 1, 16, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_4/y", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "depthwise_2/filter_in", "shape": [3, 3, 96, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_5/y", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_5/filter", "shape": [1, 1, 96, 16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_2/filter", "shape": [1, 1, 16, 64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_2/y", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "depthwise_1/filter_in", "shape": [3, 3, 64, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_3/y", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_3/filter", "shape": [1, 1, 64, 16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "model_1/model/batch_normalization_5/FusedBatchNormV3_model_1/model/conv2d_5/Conv2D_model_1/model/conv2d_3/Conv2D1/y", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D/filter", "shape": [3, 3, 3, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add/y", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "depthwise/filter_in", "shape": [3, 3, 24, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_1/y", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_1/filter", "shape": [1, 1, 24, 16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "model_1/model/batch_normalization_2/FusedBatchNormV3_model_1/model/conv2d_5/Conv2D_model_1/model/conv2d_1/Conv2D1/y", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_6/filter", "shape": [1, 1, 16, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_6/y", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "depthwise_3/filter_in", "shape": [5, 5, 96, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_7/y", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_7/filter", "shape": [1, 1, 96, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "model_1/model/batch_normalization_11/FusedBatchNormV3_model_1/model/conv2d_9/Conv2D_model_1/model/conv2d_7/Conv2D1/y", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_10/filter", "shape": [1, 1, 24, 144], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_10/y", "shape": [144], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "depthwise_5/filter_in", "shape": [3, 3, 144, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_11/y", "shape": [144], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_11/filter", "shape": [1, 1, 144, 48], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "model_1/model/batch_normalization_17/FusedBatchNormV3_model_1/model/conv2d_15/Conv2D_model_1/model/conv2d_11/Conv2D1/y", "shape": [48], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_16/filter", "shape": [1, 1, 48, 288], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_16/y", "shape": [288], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "depthwise_8/filter_in", "shape": [5, 5, 288, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_17/y", "shape": [288], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_17/filter", "shape": [1, 1, 288, 64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "model_1/model/batch_normalization_26/FusedBatchNormV3_model_1/model/conv2d_21/Conv2D_model_1/model/conv2d_17/Conv2D1/y", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_22/filter", "shape": [1, 1, 64, 384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_22/y", "shape": [384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "depthwise_11/filter_in", "shape": [5, 5, 384, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_23/y", "shape": [384], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_23/filter", "shape": [1, 1, 384, 112], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "model_1/model/batch_normalization_35/FusedBatchNormV3_model_1/model/conv2d_29/Conv2D_model_1/model/conv2d_23/Conv2D1/y", "shape": [112], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Conv2D_30/filter", "shape": [1, 1, 112, 672], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_30/y", "shape": [672], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "depthwise_15/filter_in", "shape": [3, 3, 672, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Add_31/y", "shape": [672], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "model_1/model/global_average_pooling2d/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "Identity_3_dense/kernel", "shape": [672, 63], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "Identity_3_dense/bias", "shape": [63], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}]}]}