/**
 * 头像抠图处理系统配置文件
 * Avatar Processing System Configuration
 */

// Human.js 配置
const HUMAN_CONFIG = {
    // 基础配置
    debug: false,                    // 调试模式
    async: true,                     // 异步处理
    backend: 'webgl',               // 后端类型: 'webgl', 'cpu', 'wasm'
    
    // 模型路径
    modelBasePath: 'learnface/hr_attendance_face_recognition_pro/static/src/js/models/',
    
    // 人脸检测配置
    face: {
        enabled: true,
        detector: {
            rotation: true,          // 启用旋转检测
            maxDetected: 1,         // 最大检测人脸数
            minConfidence: 0.5,     // 最小置信度 (0.0-1.0)
            skipFrames: 0,          // 跳过帧数
            skipTime: 0,            // 跳过时间(ms)
            iouThreshold: 0.4,      // IoU阈值
            scoreThreshold: 0.5     // 分数阈值
        },
        mesh: {
            enabled: true,          // 启用468点网格
            type: 'full'           // 网格类型: 'full', 'attention'
        },
        antispoof: {
            enabled: true,          // 启用防伪检测
            confidence: 0.5         // 防伪置信度
        },
        description: {
            enabled: true,          // 启用特征描述
            minConfidence: 0.5     // 特征描述最小置信度
        },
        iris: {
            enabled: false          // 虹膜检测 (可选)
        },
        emotion: {
            enabled: false          // 情感检测 (可选)
        },
        age: {
            enabled: false          // 年龄检测 (可选)
        },
        gender: {
            enabled: false          // 性别检测 (可选)
        }
    },
    
    // 禁用其他功能以提高性能
    body: { enabled: false },
    hand: { enabled: false },
    object: { enabled: false },
    gesture: { enabled: false },
    segmentation: { enabled: false },
    filter: { enabled: false }
};

// 头像生成配置
const AVATAR_CONFIG = {
    // 输出尺寸
    outputSize: 512,                // 输出头像尺寸 (像素)
    aspectRatio: 1.0,              // 宽高比 (1.0 = 正方形)
    
    // 裁切参数
    cropMargin: 1.8,               // 裁切边距倍数 (相对于人脸大小)
    minCropSize: 200,              // 最小裁切尺寸
    maxCropSize: 2000,             // 最大裁切尺寸
    
    // 背景移除
    backgroundRemoval: {
        enabled: true,              // 启用背景移除
        method: 'circular',         // 移除方法: 'circular', 'smart'
        fadeRadius: 0.4,           // 渐变半径 (相对于图像大小)
        fadeStrength: 0.3          // 渐变强度
    },
    
    // 输出格式
    outputFormat: 'png',           // 输出格式: 'png', 'jpeg'
    quality: 0.95,                 // JPEG质量 (0.0-1.0)
    
    // 旋转矫正
    rotationCorrection: {
        enabled: true,              // 启用旋转矫正
        maxAngle: 45,              // 最大矫正角度 (度)
        threshold: 5               // 矫正阈值 (度)
    }
};

// 批量处理配置
const BATCH_CONFIG = {
    // 处理限制
    maxFiles: 100,                 // 最大文件数
    maxFileSize: 10 * 1024 * 1024, // 最大文件大小 (10MB)
    
    // 支持的文件格式
    supportedFormats: [
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'image/webp'
    ],
    
    // 处理间隔
    processingDelay: 100,          // 处理间隔 (毫秒)
    
    // 并发控制
    maxConcurrent: 1,              // 最大并发数
    
    // 错误处理
    maxRetries: 2,                 // 最大重试次数
    retryDelay: 1000,             // 重试延迟 (毫秒)
    
    // 内存管理
    memoryCleanupInterval: 10,     // 内存清理间隔 (处理文件数)
    forceGC: true                  // 强制垃圾回收
};

// 性能配置
const PERFORMANCE_CONFIG = {
    // GPU设置
    gpu: {
        enabled: true,              // 启用GPU加速
        powerPreference: 'high-performance', // GPU性能偏好
        antialias: false,          // 抗锯齿
        alpha: false,              // Alpha通道
        premultipliedAlpha: false, // 预乘Alpha
        preserveDrawingBuffer: false, // 保留绘图缓冲区
        failIfMajorPerformanceCaveat: false // 性能警告时失败
    },
    
    // 内存设置
    memory: {
        maxTextureSize: 2048,      // 最大纹理尺寸
        maxCanvasSize: 4096,       // 最大画布尺寸
        cacheSize: 100,            // 缓存大小 (MB)
        autoCleanup: true          // 自动清理
    },
    
    // 优化设置
    optimization: {
        useWorkers: false,         // 使用Web Workers
        preloadModels: true,       // 预加载模型
        modelCaching: true,        // 模型缓存
        imageCaching: false        // 图像缓存
    }
};

// UI配置
const UI_CONFIG = {
    // 主题设置
    theme: {
        primaryColor: '#667eea',
        secondaryColor: '#764ba2',
        successColor: '#28a745',
        errorColor: '#dc3545',
        warningColor: '#ffc107'
    },
    
    // 动画设置
    animations: {
        enabled: true,
        duration: 300,             // 动画持续时间 (毫秒)
        easing: 'ease-in-out'     // 缓动函数
    },
    
    // 进度显示
    progress: {
        updateInterval: 100,       // 更新间隔 (毫秒)
        smoothing: true,          // 平滑进度
        showPercentage: true,     // 显示百分比
        showETA: true             // 显示预计完成时间
    },
    
    // 消息设置
    messages: {
        autoHide: true,           // 自动隐藏
        hideDelay: 5000,          // 隐藏延迟 (毫秒)
        maxMessages: 5            // 最大消息数
    }
};

// 调试配置
const DEBUG_CONFIG = {
    // 日志设置
    logging: {
        enabled: false,            // 启用日志
        level: 'info',            // 日志级别: 'debug', 'info', 'warn', 'error'
        console: true,            // 控制台输出
        storage: false            // 本地存储
    },
    
    // 性能监控
    performance: {
        enabled: false,           // 启用性能监控
        memoryTracking: false,    // 内存跟踪
        timingTracking: true,     // 时间跟踪
        fpsTracking: false        // FPS跟踪
    },
    
    // 错误报告
    errorReporting: {
        enabled: true,            // 启用错误报告
        detailed: false,          // 详细错误信息
        stackTrace: true          // 堆栈跟踪
    }
};

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        HUMAN_CONFIG,
        AVATAR_CONFIG,
        BATCH_CONFIG,
        PERFORMANCE_CONFIG,
        UI_CONFIG,
        DEBUG_CONFIG
    };
} else {
    // 浏览器环境
    window.CONFIG = {
        HUMAN_CONFIG,
        AVATAR_CONFIG,
        BATCH_CONFIG,
        PERFORMANCE_CONFIG,
        UI_CONFIG,
        DEBUG_CONFIG
    };
}

/**
 * 获取配置的辅助函数
 */
function getConfig(section, key = null) {
    const configs = {
        human: HUMAN_CONFIG,
        avatar: AVATAR_CONFIG,
        batch: BATCH_CONFIG,
        performance: PERFORMANCE_CONFIG,
        ui: UI_CONFIG,
        debug: DEBUG_CONFIG
    };
    
    const config = configs[section];
    if (!config) {
        console.warn(`配置节 "${section}" 不存在`);
        return null;
    }
    
    if (key === null) {
        return config;
    }
    
    return config[key] || null;
}

/**
 * 更新配置的辅助函数
 */
function updateConfig(section, key, value) {
    const configs = {
        human: HUMAN_CONFIG,
        avatar: AVATAR_CONFIG,
        batch: BATCH_CONFIG,
        performance: PERFORMANCE_CONFIG,
        ui: UI_CONFIG,
        debug: DEBUG_CONFIG
    };
    
    const config = configs[section];
    if (!config) {
        console.warn(`配置节 "${section}" 不存在`);
        return false;
    }
    
    if (typeof key === 'object') {
        // 批量更新
        Object.assign(config, key);
    } else {
        // 单个更新
        config[key] = value;
    }
    
    return true;
}

// 浏览器环境下添加到全局
if (typeof window !== 'undefined') {
    window.getConfig = getConfig;
    window.updateConfig = updateConfig;
}
