<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>背景移除功能调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background: #f0f8ff;
        }
        .results {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .result-item {
            text-align: center;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
        }
        .result-item img, .result-item canvas {
            max-width: 100%;
            max-height: 300px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎭 背景移除功能调试工具</h1>
        <p>这个页面专门用于测试和调试背景移除功能</p>

        <div class="upload-area" id="uploadArea">
            <p>📸 点击或拖拽上传图片</p>
            <p style="font-size: 12px; color: #666;">支持 JPG、PNG、JPEG 格式</p>
            <input type="file" id="fileInput" accept="image/*" style="display: none;">
        </div>

        <div id="status"></div>

        <div class="results" id="results" style="display: none;">
            <div class="result-item">
                <h3>📷 原始图片</h3>
                <img id="originalImage" alt="原始图片">
                <div id="originalInfo"></div>
            </div>
            <div class="result-item">
                <h3>✨ 去除背景后</h3>
                <img id="processedImage" alt="处理后图片">
                <div id="processedInfo"></div>
            </div>
        </div>

        <div class="log" id="logArea">
            <strong>📋 调试日志:</strong><br>
            等待上传图片...
        </div>

        <div style="text-align: center; margin-top: 20px;">
            <button class="btn" id="testBtn" onclick="testAPI()">🧪 测试API连接</button>
            <button class="btn" id="clearBtn" onclick="clearLog()">🗑️ 清空日志</button>
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const logArea = document.getElementById('logArea');
        const status = document.getElementById('status');
        const results = document.getElementById('results');

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logArea.innerHTML += '<br>' + logEntry;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(logEntry);
        }

        function showStatus(message, type = 'info') {
            status.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearLog() {
            logArea.innerHTML = '<strong>📋 调试日志:</strong><br>日志已清空...';
        }

        // 测试API连接
        async function testAPI() {
            log('🧪 测试API连接...');
            try {
                const response = await fetch('/api/remove-background', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ test: true })
                });
                
                const result = await response.json();
                log(`📡 API响应状态: ${response.status}`);
                log(`📦 API响应内容: ${JSON.stringify(result)}`);
                
                if (response.status === 400) {
                    showStatus('✅ API连接正常 (返回400是预期的，因为缺少图片数据)', 'success');
                } else {
                    showStatus('⚠️ API响应异常', 'error');
                }
            } catch (error) {
                log(`❌ API连接失败: ${error.message}`);
                showStatus('❌ API连接失败', 'error');
            }
        }

        // 文件上传处理
        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        // 处理上传的文件
        async function handleFile(file) {
            log(`📁 选择文件: ${file.name} (${Math.round(file.size/1024)}KB)`);
            showStatus('📤 正在处理图片...', 'info');

            try {
                // 读取文件为base64
                const base64Data = await fileToBase64(file);
                log(`📊 Base64数据长度: ${base64Data.length} 字符`);
                
                // 显示原始图片
                const originalImage = document.getElementById('originalImage');
                originalImage.src = base64Data;
                document.getElementById('originalInfo').innerHTML = `
                    <small>文件名: ${file.name}<br>
                    大小: ${Math.round(file.size/1024)}KB<br>
                    类型: ${file.type}</small>
                `;

                // 调用背景移除API
                log('🎭 调用背景移除API...');
                const startTime = Date.now();
                
                const response = await fetch('/api/remove-background', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        image_data: base64Data,
                        model: 'u2net_human_seg'
                    })
                });

                const processingTime = Date.now() - startTime;
                log(`⏱️ API调用耗时: ${processingTime}ms`);
                log(`📡 响应状态: ${response.status} ${response.statusText}`);

                const result = await response.json();
                log(`📦 响应数据: ${JSON.stringify(result, null, 2)}`);

                if (result.success) {
                    log('✅ 背景移除成功!');
                    
                    // 显示处理后的图片
                    const processedImage = document.getElementById('processedImage');
                    processedImage.src = result.data.image_data;
                    document.getElementById('processedInfo').innerHTML = `
                        <small>模型: ${result.data.model}<br>
                        处理时间: ${processingTime}ms<br>
                        输出格式: PNG (透明背景)</small>
                    `;
                    
                    results.style.display = 'grid';
                    showStatus('🎉 背景移除完成!', 'success');
                } else {
                    log(`❌ 背景移除失败: ${result.message}`);
                    if (result.debug) {
                        log(`🐛 调试信息: ${result.debug}`);
                    }
                    showStatus(`❌ 背景移除失败: ${result.message}`, 'error');
                }

            } catch (error) {
                log(`💥 处理过程中发生错误: ${error.message}`);
                showStatus(`💥 处理失败: ${error.message}`, 'error');
            }
        }

        // 文件转base64
        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }

        // 页面加载完成后测试API
        window.addEventListener('load', () => {
            log('🚀 页面加载完成');
            testAPI();
        });
    </script>
</body>
</html>
