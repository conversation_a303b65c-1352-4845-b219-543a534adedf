{"format": "graph-model", "generatedBy": "https://github.com/google/mediapipe", "convertedBy": "https://github.com/vladmandic", "userDefinedMetadata": {"signature": {"inputs": {"input_1:0": {"name": "input_1:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "64"}, {"size": "64"}, {"size": "3"}]}}}, "outputs": {"Identity:0": {"name": "Identity:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "1"}, {"size": "1"}, {"size": "228"}]}}}}}, "modelTopology": {"node": [{"name": "StatefulPartitionedCall/model/p_re_lu_36/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_35/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_34/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_33/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_32/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_31/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_30/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_29/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_28/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_27/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_26/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_25/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_24/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_23/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_22/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_21/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_149", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}}}, {"name": "unknown_175", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}}}, {"name": "unknown_201", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}}}, {"name": "unknown_227", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_253", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_279", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}}}, {"name": "unknown_305", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}}}, {"name": "unknown_331", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_344", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "2"}, {"size": "2"}, {"size": "128"}, {"size": "213"}]}}}}}, {"name": "unknown_345", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "213"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_52/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_51/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_50/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_49/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_48/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_47/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_46/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_45/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_44/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_43/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_42/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_41/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_40/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_39/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_38/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_37/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_20/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_19/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_18/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_17/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_16/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_15/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_14/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_13/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_12/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_11/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_10/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/channel_padding/Pad/paddings", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_9/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_8/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_7/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_6/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_5/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_4/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_3/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_2/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_1/Neg", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu/Neg", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}]}}}}}, {"name": "unknown_12", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "1"}]}}}}}, {"name": "unknown_25", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "1"}]}}}}}, {"name": "unknown_38", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_51", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "1"}]}}}}}, {"name": "unknown_64", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}}}, {"name": "unknown_77", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}}}, {"name": "unknown_90", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}}}, {"name": "unknown_103", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}}}, {"name": "unknown_116", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}}}, {"name": "unknown_129", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}}}, {"name": "unknown_148", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_174", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}}}, {"name": "unknown_200", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}}}, {"name": "unknown_226", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_252", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_278", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}}}, {"name": "unknown_304", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_330", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_346", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "2"}, {"size": "2"}, {"size": "128"}, {"size": "15"}]}}}}}, {"name": "unknown_347", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "15"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/concatenate_eyes_contours_and_brows_and_iris/concat/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "input_1", "op": "Placeholder", "attr": {"shape": {"shape": {"dim": [{"size": "-1"}, {"size": "64"}, {"size": "64"}, {"size": "3"}]}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "3"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_1/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_50/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_1/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_2/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_2/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_3/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_3/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_4/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_4/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_5/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_5/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_6/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_6/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_7/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_7/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_8/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_8/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_9/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "2"}, {"size": "2"}, {"size": "64"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_35/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_9/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_10/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_10/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_11/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_35/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_11/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_12/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_12/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_13/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_13/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_14/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_14/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_15/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_15/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_16/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_16/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_17/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_17/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_18/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_18/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_19/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "2"}, {"size": "2"}, {"size": "128"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_51/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_19/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_20/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_20/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_21/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_51/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_21/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_37/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_37/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_22/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_22/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_38/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_38/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_23/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_23/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_39/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_39/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_24/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_24/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_40/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_40/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_25/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "2"}, {"size": "2"}, {"size": "128"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_36/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_25/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_41/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "2"}, {"size": "2"}, {"size": "128"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_41/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_26/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_36/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_26/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_42/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_42/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_27/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_27/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_43/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_43/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_28/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_28/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_44/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_44/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_29/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_29/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_45/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_45/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_30/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_30/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_46/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_46/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_31/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "2"}, {"size": "2"}, {"size": "128"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_52/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_31/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_47/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "2"}, {"size": "2"}, {"size": "128"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_47/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_32/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_52/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_32/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_48/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_48/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_33/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_33/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_49/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_49/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_34/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_34/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_50/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/batch_normalization/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["input_1", "StatefulPartitionedCall/model/conv2d/Conv2D_weights", "StatefulPartitionedCall/model/conv2d/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu/Neg"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "2"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "epsilon": {"f": 0}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_1/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/batch_normalization/FusedBatchNormV3", "StatefulPartitionedCall/model/conv2d_1/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_1/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_1/Neg"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "explicit_paddings": {"list": {}}, "num_args": {"i": "2"}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_1/FusedBatchNormV3", "unknown_12"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_2/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d/depthwise", "StatefulPartitionedCall/model/conv2d_2/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_2/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/add/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/batch_normalization/FusedBatchNormV3", "StatefulPartitionedCall/model/batch_normalization_2/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_2/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add/add", "StatefulPartitionedCall/model/p_re_lu_2/Neg"]}, {"name": "StatefulPartitionedCall/model/batch_normalization_3/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_2/Relu", "StatefulPartitionedCall/model/conv2d_3/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_3/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_3/Neg"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "2"}, "padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_1/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_3/FusedBatchNormV3", "unknown_25"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_4/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_1/depthwise", "StatefulPartitionedCall/model/conv2d_4/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_4/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "use_cudnn_on_gpu": {"b": true}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/add_1/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/p_re_lu_2/Relu", "StatefulPartitionedCall/model/batch_normalization_4/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_4/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_1/add", "StatefulPartitionedCall/model/p_re_lu_4/Neg"]}, {"name": "StatefulPartitionedCall/model/batch_normalization_5/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_4/Relu", "StatefulPartitionedCall/model/conv2d_5/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_5/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_5/Neg"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "2"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_2/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_5/FusedBatchNormV3", "unknown_38"], "attr": {"explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_6/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_2/depthwise", "StatefulPartitionedCall/model/conv2d_6/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_6/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"padding": {"s": "VkFMSUQ="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "use_cudnn_on_gpu": {"b": true}, "epsilon": {"f": 0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/add_2/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/p_re_lu_4/Relu", "StatefulPartitionedCall/model/batch_normalization_6/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_6/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_2/add", "StatefulPartitionedCall/model/p_re_lu_6/Neg"]}, {"name": "StatefulPartitionedCall/model/batch_normalization_7/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_6/Relu", "StatefulPartitionedCall/model/conv2d_7/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_7/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_7/Neg"], "device": "/device:CPU:0", "attr": {"padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0}, "num_args": {"i": "2"}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_3/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_7/FusedBatchNormV3", "unknown_51"], "attr": {"padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_8/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_3/depthwise", "StatefulPartitionedCall/model/conv2d_8/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_8/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/add_3/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/p_re_lu_6/Relu", "StatefulPartitionedCall/model/batch_normalization_8/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_8/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_3/add", "StatefulPartitionedCall/model/p_re_lu_8/Neg"]}, {"name": "StatefulPartitionedCall/model/max_pooling2d/MaxPool", "op": "MaxPool", "input": ["StatefulPartitionedCall/model/p_re_lu_8/Relu"], "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "padding": {"s": "VkFMSUQ="}, "explicit_paddings": {"list": {}}, "ksize": {"list": {"i": ["1", "2", "2", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_9/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_8/Relu", "StatefulPartitionedCall/model/conv2d_9/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_9/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_9/Neg"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "num_args": {"i": "2"}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}}}, {"name": "StatefulPartitionedCall/model/channel_padding/Pad", "op": "Pad", "input": ["StatefulPartitionedCall/model/max_pooling2d/MaxPool", "StatefulPartitionedCall/model/channel_padding/Pad/paddings"], "attr": {"Tpaddings": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_4/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_9/FusedBatchNormV3", "unknown_64"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_10/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_4/depthwise", "StatefulPartitionedCall/model/conv2d_10/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_10/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "padding": {"s": "VkFMSUQ="}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/add_4/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/channel_padding/Pad", "StatefulPartitionedCall/model/batch_normalization_10/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_10/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_4/add", "StatefulPartitionedCall/model/p_re_lu_10/Neg"]}, {"name": "StatefulPartitionedCall/model/batch_normalization_11/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_10/Relu", "StatefulPartitionedCall/model/conv2d_11/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_11/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_11/Neg"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "2"}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_5/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_11/FusedBatchNormV3", "unknown_77"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_12/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_5/depthwise", "StatefulPartitionedCall/model/conv2d_12/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_12/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/add_5/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/p_re_lu_10/Relu", "StatefulPartitionedCall/model/batch_normalization_12/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_12/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_5/add", "StatefulPartitionedCall/model/p_re_lu_12/Neg"]}, {"name": "StatefulPartitionedCall/model/batch_normalization_13/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_12/Relu", "StatefulPartitionedCall/model/conv2d_13/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_13/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_13/Neg"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "VkFMSUQ="}, "explicit_paddings": {"list": {}}, "num_args": {"i": "2"}, "epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_6/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_13/FusedBatchNormV3", "unknown_90"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_14/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_6/depthwise", "StatefulPartitionedCall/model/conv2d_14/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_14/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"padding": {"s": "VkFMSUQ="}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/model/add_6/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/p_re_lu_12/Relu", "StatefulPartitionedCall/model/batch_normalization_14/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_14/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_6/add", "StatefulPartitionedCall/model/p_re_lu_14/Neg"]}, {"name": "StatefulPartitionedCall/model/batch_normalization_15/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_14/Relu", "StatefulPartitionedCall/model/conv2d_15/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_15/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_15/Neg"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "num_args": {"i": "2"}, "padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_7/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_15/FusedBatchNormV3", "unknown_103"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_16/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_7/depthwise", "StatefulPartitionedCall/model/conv2d_16/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_16/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0}, "num_args": {"i": "1"}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/add_7/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/p_re_lu_14/Relu", "StatefulPartitionedCall/model/batch_normalization_16/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_16/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_7/add", "StatefulPartitionedCall/model/p_re_lu_16/Neg"]}, {"name": "StatefulPartitionedCall/model/batch_normalization_17/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_16/Relu", "StatefulPartitionedCall/model/conv2d_17/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_17/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_17/Neg"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "2"}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "padding": {"s": "VkFMSUQ="}, "epsilon": {"f": 0}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_8/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_17/FusedBatchNormV3", "unknown_116"], "attr": {"T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_18/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_8/depthwise", "StatefulPartitionedCall/model/conv2d_18/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_18/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}, "num_args": {"i": "1"}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "StatefulPartitionedCall/model/add_8/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/p_re_lu_16/Relu", "StatefulPartitionedCall/model/batch_normalization_18/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_18/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_8/add", "StatefulPartitionedCall/model/p_re_lu_18/Neg"]}, {"name": "StatefulPartitionedCall/model/max_pooling2d_1/MaxPool", "op": "MaxPool", "input": ["StatefulPartitionedCall/model/p_re_lu_18/Relu"], "attr": {"padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "ksize": {"list": {"i": ["1", "2", "2", "1"]}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_19/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_18/Relu", "StatefulPartitionedCall/model/conv2d_19/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_19/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_19/Neg"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "2"}, "epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_9/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_19/FusedBatchNormV3", "unknown_129"], "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_20/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_9/depthwise", "StatefulPartitionedCall/model/conv2d_20/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_20/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/model/add_9/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/max_pooling2d_1/MaxPool", "StatefulPartitionedCall/model/batch_normalization_20/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_20/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_9/add", "StatefulPartitionedCall/model/p_re_lu_20/Neg"]}, {"name": "StatefulPartitionedCall/model/batch_normalization_21/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_20/Relu", "StatefulPartitionedCall/model/conv2d_21/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_21/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_21/Neg"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "2"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_37/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_20/Relu", "StatefulPartitionedCall/model/conv2d_37/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_37/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_37/Neg"], "device": "/device:CPU:0", "attr": {"padding": {"s": "VkFMSUQ="}, "epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "num_args": {"i": "2"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_10/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_21/FusedBatchNormV3", "unknown_149"], "attr": {"padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_18/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_37/FusedBatchNormV3", "unknown_148"], "attr": {"data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_22/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_10/depthwise", "StatefulPartitionedCall/model/conv2d_22/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_22/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_38/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_18/depthwise", "StatefulPartitionedCall/model/conv2d_38/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_38/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "StatefulPartitionedCall/model/add_10/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/p_re_lu_20/Relu", "StatefulPartitionedCall/model/batch_normalization_22/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/add_18/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/p_re_lu_20/Relu", "StatefulPartitionedCall/model/batch_normalization_38/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_22/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_10/add", "StatefulPartitionedCall/model/p_re_lu_22/Neg"]}, {"name": "StatefulPartitionedCall/model/p_re_lu_38/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_18/add", "StatefulPartitionedCall/model/p_re_lu_38/Neg"]}, {"name": "StatefulPartitionedCall/model/batch_normalization_23/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_22/Relu", "StatefulPartitionedCall/model/conv2d_23/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_23/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_23/Neg"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "num_args": {"i": "2"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "epsilon": {"f": 0}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_39/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_38/Relu", "StatefulPartitionedCall/model/conv2d_39/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_39/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_39/Neg"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "num_args": {"i": "2"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_11/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_23/FusedBatchNormV3", "unknown_175"], "attr": {"explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_19/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_39/FusedBatchNormV3", "unknown_174"], "attr": {"padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_24/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_11/depthwise", "StatefulPartitionedCall/model/conv2d_24/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_24/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_40/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_19/depthwise", "StatefulPartitionedCall/model/conv2d_40/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_40/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "padding": {"s": "VkFMSUQ="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/add_11/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/p_re_lu_22/Relu", "StatefulPartitionedCall/model/batch_normalization_24/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/add_19/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/p_re_lu_38/Relu", "StatefulPartitionedCall/model/batch_normalization_40/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_24/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_11/add", "StatefulPartitionedCall/model/p_re_lu_24/Neg"]}, {"name": "StatefulPartitionedCall/model/p_re_lu_40/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_19/add", "StatefulPartitionedCall/model/p_re_lu_40/Neg"]}, {"name": "StatefulPartitionedCall/model/max_pooling2d_2/MaxPool", "op": "MaxPool", "input": ["StatefulPartitionedCall/model/p_re_lu_24/Relu"], "attr": {"T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "ksize": {"list": {"i": ["1", "2", "2", "1"]}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_25/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_24/Relu", "StatefulPartitionedCall/model/conv2d_25/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_25/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_25/Neg"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "2"}, "padding": {"s": "VkFMSUQ="}}}, {"name": "StatefulPartitionedCall/model/max_pooling2d_4/MaxPool", "op": "MaxPool", "input": ["StatefulPartitionedCall/model/p_re_lu_40/Relu"], "attr": {"ksize": {"list": {"i": ["1", "2", "2", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_41/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_40/Relu", "StatefulPartitionedCall/model/conv2d_41/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_41/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_41/Neg"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "2", "2", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "2"}, "padding": {"s": "VkFMSUQ="}, "epsilon": {"f": 0}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_12/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_25/FusedBatchNormV3", "unknown_201"], "attr": {"T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_20/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_41/FusedBatchNormV3", "unknown_200"], "attr": {"padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_26/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_12/depthwise", "StatefulPartitionedCall/model/conv2d_26/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_26/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_42/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_20/depthwise", "StatefulPartitionedCall/model/conv2d_42/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_42/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/add_12/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/max_pooling2d_2/MaxPool", "StatefulPartitionedCall/model/batch_normalization_26/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/add_20/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/max_pooling2d_4/MaxPool", "StatefulPartitionedCall/model/batch_normalization_42/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_26/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_12/add", "StatefulPartitionedCall/model/p_re_lu_26/Neg"]}, {"name": "StatefulPartitionedCall/model/p_re_lu_42/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_20/add", "StatefulPartitionedCall/model/p_re_lu_42/Neg"]}, {"name": "StatefulPartitionedCall/model/batch_normalization_27/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_26/Relu", "StatefulPartitionedCall/model/conv2d_27/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_27/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_27/Neg"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "2"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_43/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_42/Relu", "StatefulPartitionedCall/model/conv2d_43/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_43/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_43/Neg"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "2"}, "padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_13/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_27/FusedBatchNormV3", "unknown_227"], "attr": {"explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_21/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_43/FusedBatchNormV3", "unknown_226"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_28/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_13/depthwise", "StatefulPartitionedCall/model/conv2d_28/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_28/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_44/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_21/depthwise", "StatefulPartitionedCall/model/conv2d_44/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_44/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}}}, {"name": "StatefulPartitionedCall/model/add_13/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/p_re_lu_26/Relu", "StatefulPartitionedCall/model/batch_normalization_28/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/add_21/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/p_re_lu_42/Relu", "StatefulPartitionedCall/model/batch_normalization_44/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_28/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_13/add", "StatefulPartitionedCall/model/p_re_lu_28/Neg"]}, {"name": "StatefulPartitionedCall/model/p_re_lu_44/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_21/add", "StatefulPartitionedCall/model/p_re_lu_44/Neg"]}, {"name": "StatefulPartitionedCall/model/batch_normalization_29/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_28/Relu", "StatefulPartitionedCall/model/conv2d_29/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_29/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_29/Neg"], "device": "/device:CPU:0", "attr": {"padding": {"s": "VkFMSUQ="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "2"}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_45/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_44/Relu", "StatefulPartitionedCall/model/conv2d_45/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_45/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_45/Neg"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "num_args": {"i": "2"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_14/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_29/FusedBatchNormV3", "unknown_253"], "attr": {"data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_22/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_45/FusedBatchNormV3", "unknown_252"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_30/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_14/depthwise", "StatefulPartitionedCall/model/conv2d_30/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_30/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_46/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_22/depthwise", "StatefulPartitionedCall/model/conv2d_46/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_46/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}, "epsilon": {"f": 0}}}, {"name": "StatefulPartitionedCall/model/add_14/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/p_re_lu_28/Relu", "StatefulPartitionedCall/model/batch_normalization_30/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/add_22/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/p_re_lu_44/Relu", "StatefulPartitionedCall/model/batch_normalization_46/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_30/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_14/add", "StatefulPartitionedCall/model/p_re_lu_30/Neg"]}, {"name": "StatefulPartitionedCall/model/p_re_lu_46/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_22/add", "StatefulPartitionedCall/model/p_re_lu_46/Neg"]}, {"name": "StatefulPartitionedCall/model/max_pooling2d_3/MaxPool", "op": "MaxPool", "input": ["StatefulPartitionedCall/model/p_re_lu_30/Relu"], "attr": {"T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}, "ksize": {"list": {"i": ["1", "2", "2", "1"]}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_31/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_30/Relu", "StatefulPartitionedCall/model/conv2d_31/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_31/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_31/Neg"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "2"}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "StatefulPartitionedCall/model/max_pooling2d_5/MaxPool", "op": "MaxPool", "input": ["StatefulPartitionedCall/model/p_re_lu_46/Relu"], "attr": {"explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "ksize": {"list": {"i": ["1", "2", "2", "1"]}}, "padding": {"s": "VkFMSUQ="}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_47/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_46/Relu", "StatefulPartitionedCall/model/conv2d_47/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_47/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_47/Neg"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "padding": {"s": "VkFMSUQ="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "num_args": {"i": "2"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_15/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_31/FusedBatchNormV3", "unknown_279"], "attr": {"data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_23/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_47/FusedBatchNormV3", "unknown_278"], "attr": {"explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_32/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_15/depthwise", "StatefulPartitionedCall/model/conv2d_32/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_32/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_48/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_23/depthwise", "StatefulPartitionedCall/model/conv2d_48/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_48/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/add_15/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/max_pooling2d_3/MaxPool", "StatefulPartitionedCall/model/batch_normalization_32/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/add_23/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/max_pooling2d_5/MaxPool", "StatefulPartitionedCall/model/batch_normalization_48/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_32/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_15/add", "StatefulPartitionedCall/model/p_re_lu_32/Neg"]}, {"name": "StatefulPartitionedCall/model/p_re_lu_48/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_23/add", "StatefulPartitionedCall/model/p_re_lu_48/Neg"]}, {"name": "StatefulPartitionedCall/model/batch_normalization_33/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_32/Relu", "StatefulPartitionedCall/model/conv2d_33/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_33/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_33/Neg"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "2"}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_49/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_48/Relu", "StatefulPartitionedCall/model/conv2d_49/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_49/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_49/Neg"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "num_args": {"i": "2"}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "VkFMSUQ="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_16/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_33/FusedBatchNormV3", "unknown_305"], "attr": {"T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_24/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_49/FusedBatchNormV3", "unknown_304"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_34/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_16/depthwise", "StatefulPartitionedCall/model/conv2d_34/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_34/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "epsilon": {"f": 0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_50/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_24/depthwise", "StatefulPartitionedCall/model/conv2d_50/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_50/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}}}, {"name": "StatefulPartitionedCall/model/add_16/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/p_re_lu_32/Relu", "StatefulPartitionedCall/model/batch_normalization_34/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/add_24/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/p_re_lu_48/Relu", "StatefulPartitionedCall/model/batch_normalization_50/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_34/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_16/add", "StatefulPartitionedCall/model/p_re_lu_34/Neg"]}, {"name": "StatefulPartitionedCall/model/p_re_lu_50/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_24/add", "StatefulPartitionedCall/model/p_re_lu_50/Neg"]}, {"name": "StatefulPartitionedCall/model/batch_normalization_35/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_34/Relu", "StatefulPartitionedCall/model/conv2d_35/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_35/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_35/Neg"], "device": "/device:CPU:0", "attr": {"padding": {"s": "VkFMSUQ="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "2"}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_51/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_50/Relu", "StatefulPartitionedCall/model/conv2d_51/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_51/Conv2D_bn_offset", "StatefulPartitionedCall/model/p_re_lu_51/Neg"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "2"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UHJlbHU="]}}, "padding": {"s": "VkFMSUQ="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_17/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_35/FusedBatchNormV3", "unknown_331"], "attr": {"explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_25/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/batch_normalization_51/FusedBatchNormV3", "unknown_330"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_36/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_17/depthwise", "StatefulPartitionedCall/model/conv2d_36/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_36/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_52/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_25/depthwise", "StatefulPartitionedCall/model/conv2d_52/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_52/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/model/add_17/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/p_re_lu_34/Relu", "StatefulPartitionedCall/model/batch_normalization_36/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/add_25/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/p_re_lu_50/Relu", "StatefulPartitionedCall/model/batch_normalization_52/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/p_re_lu_36/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_17/add", "StatefulPartitionedCall/model/p_re_lu_36/Neg"]}, {"name": "StatefulPartitionedCall/model/p_re_lu_52/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_25/add", "StatefulPartitionedCall/model/p_re_lu_52/Neg"]}, {"name": "StatefulPartitionedCall/model/conv_eyes_contours_and_brows/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_36/Relu", "unknown_344", "unknown_345"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/conv_iris/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/p_re_lu_52/Relu", "unknown_346", "unknown_347"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0}, "num_args": {"i": "1"}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "VkFMSUQ="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/concatenate_eyes_contours_and_brows_and_iris/concat", "op": "ConcatV2", "input": ["StatefulPartitionedCall/model/conv_eyes_contours_and_brows/BiasAdd", "StatefulPartitionedCall/model/conv_iris/BiasAdd", "StatefulPartitionedCall/model/concatenate_eyes_contours_and_brows_and_iris/concat/axis"], "attr": {"N": {"i": "2"}, "T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "Identity", "op": "Identity", "input": ["StatefulPartitionedCall/model/concatenate_eyes_contours_and_brows_and_iris/concat"], "attr": {"T": {"type": "DT_FLOAT"}}}], "library": {}, "versions": {}}, "weightsManifest": [{"paths": ["iris.bin"], "weights": [{"name": "StatefulPartitionedCall/model/p_re_lu_36/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_35/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_34/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_33/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_32/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_31/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_30/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_29/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_28/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_27/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_26/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_25/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_24/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_23/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_22/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_21/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "unknown_149", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_175", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_201", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_227", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_253", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_279", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_305", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_331", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_344", "shape": [2, 2, 128, 213], "dtype": "float32"}, {"name": "unknown_345", "shape": [213], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_52/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_51/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_50/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_49/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_48/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_47/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_46/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_45/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_44/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_43/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_42/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_41/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_40/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_39/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_38/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_37/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_20/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_19/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_18/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_17/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_16/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_15/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_14/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_13/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_12/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_11/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_10/Neg", "shape": [1, 1, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/channel_padding/Pad/paddings", "shape": [4, 2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_9/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_8/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_7/Neg", "shape": [1, 1, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_6/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_5/Neg", "shape": [1, 1, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_4/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_3/Neg", "shape": [1, 1, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_2/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu_1/Neg", "shape": [1, 1, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/p_re_lu/Neg", "shape": [1, 1, 64], "dtype": "float32"}, {"name": "unknown_12", "shape": [3, 3, 32, 1], "dtype": "float32"}, {"name": "unknown_25", "shape": [3, 3, 32, 1], "dtype": "float32"}, {"name": "unknown_38", "shape": [3, 3, 32, 1], "dtype": "float32"}, {"name": "unknown_51", "shape": [3, 3, 32, 1], "dtype": "float32"}, {"name": "unknown_64", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_77", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_90", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_103", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_116", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_129", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_148", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_174", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_200", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_226", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_252", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_278", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_304", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_330", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_346", "shape": [2, 2, 128, 15], "dtype": "float32"}, {"name": "unknown_347", "shape": [15], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/concatenate_eyes_contours_and_brows_and_iris/concat/axis", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/conv2d/Conv2D_weights", "shape": [3, 3, 3, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_1/Conv2D_weights", "shape": [1, 1, 64, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_50/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_1/Conv2D_bn_offset", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_2/Conv2D_weights", "shape": [1, 1, 32, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_2/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_3/Conv2D_weights", "shape": [1, 1, 64, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_3/Conv2D_bn_offset", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_4/Conv2D_weights", "shape": [1, 1, 32, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_4/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_5/Conv2D_weights", "shape": [1, 1, 64, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_5/Conv2D_bn_offset", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_6/Conv2D_weights", "shape": [1, 1, 32, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_6/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_7/Conv2D_weights", "shape": [1, 1, 64, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_7/Conv2D_bn_offset", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_8/Conv2D_weights", "shape": [1, 1, 32, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_8/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_9/Conv2D_weights", "shape": [2, 2, 64, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_35/Conv2D_weights", "shape": [1, 1, 128, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_9/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_10/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_10/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_11/Conv2D_weights", "shape": [1, 1, 128, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_35/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_11/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_12/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_12/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_13/Conv2D_weights", "shape": [1, 1, 128, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_13/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_14/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_14/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_15/Conv2D_weights", "shape": [1, 1, 128, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_15/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_16/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_16/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_17/Conv2D_weights", "shape": [1, 1, 128, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_17/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_18/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_18/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_19/Conv2D_weights", "shape": [2, 2, 128, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_51/Conv2D_weights", "shape": [1, 1, 128, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_19/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_20/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_20/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_21/Conv2D_weights", "shape": [1, 1, 128, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_51/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_21/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_37/Conv2D_weights", "shape": [1, 1, 128, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_37/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_22/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_22/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_38/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_38/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_23/Conv2D_weights", "shape": [1, 1, 128, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_23/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_39/Conv2D_weights", "shape": [1, 1, 128, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_39/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_24/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_24/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_40/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_40/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_25/Conv2D_weights", "shape": [2, 2, 128, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_36/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_25/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_41/Conv2D_weights", "shape": [2, 2, 128, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_41/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_26/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_36/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_26/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_42/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_42/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_27/Conv2D_weights", "shape": [1, 1, 128, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_27/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_43/Conv2D_weights", "shape": [1, 1, 128, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_43/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_28/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_28/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_44/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_44/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_29/Conv2D_weights", "shape": [1, 1, 128, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_29/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_45/Conv2D_weights", "shape": [1, 1, 128, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_45/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_30/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_30/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_46/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_46/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_31/Conv2D_weights", "shape": [2, 2, 128, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_52/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_31/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_47/Conv2D_weights", "shape": [2, 2, 128, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_47/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_32/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_52/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_32/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_48/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_48/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_33/Conv2D_weights", "shape": [1, 1, 128, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_33/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_49/Conv2D_weights", "shape": [1, 1, 128, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_49/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_34/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_34/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model/conv2d_50/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}]}]}