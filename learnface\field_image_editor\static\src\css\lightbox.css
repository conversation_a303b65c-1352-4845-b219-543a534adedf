		.in  {

        }
 /* Выделение миниатюры при наведении */
		.in:hover {
          cursor: pointer;
        }
/* Скрытый контейнер с большим изображением */
        #outer {
               display: none;
               position: absolute;
               margin-right: -50%;
               /*transform: translate(-50%, -50%);*/
               z-index: 999;
               width: 860px;
               height: auto;
               top: 0px;
               left: -13%;
               background: rgba(120, 120, 120, 0.3);
        }
/* Бордюр изображения */
        #inner {
               display: table;
               margin: 0 auto;
               max-width: 860px;
               max-height: 100%;
               cursor: pointer;
        }
        .open-block-close{
            position: absolute; 
            top: -32px; 
            right: -32px;
            width: 24px;
            height: 24px;
            text-decoration: none;
            text-align: center;
            border: 2px solid #fff;
            background: #555;
            color: #fff;
            font: 700 20px/16px verdana, sans-serif;
            border-radius: 50%;
            box-shadow: 0 2px 2px rgba(0,0,0,0.5);
        }
       .open-block-close:hover{
           background: #7c7bad;
       }
	