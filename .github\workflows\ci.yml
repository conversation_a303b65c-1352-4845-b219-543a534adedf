name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

env:
  GO_VERSION: "1.24"
  PYTHON_VERSION: "3.8"

jobs:
  test:
    name: 测试
    runs-on: ubuntu-latest
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest]
        go-version: [1.24.x]
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Go环境
      uses: actions/setup-go@v4
      with:
        go-version: ${{ matrix.go-version }}
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: 缓存Go模块
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-
    
    - name: 安装Python依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r main/ppt_generator/requirements.txt
        pip install pytest
    
    - name: 安装Go依赖
      working-directory: ./main
      run: go mod download
    
    - name: 运行代码检查
      working-directory: ./main
      run: |
        go fmt ./...
        go vet ./...
        go mod tidy
        git diff --exit-code
    
    - name: 运行单元测试
      working-directory: ./main
      run: go test -v -race -coverprofile=coverage.out ./...
    
    - name: 上传覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: ./main/coverage.out
        flags: unittests
        name: codecov-umbrella
    
    - name: 运行Python测试
      working-directory: ./main/ppt_generator
      run: |
        python -m py_compile ppt_generator.py
        # 如果有Python测试文件，运行它们
        if [ -f "test_ppt_generator.py" ]; then
          pytest test_ppt_generator.py -v
        fi
    
    - name: 构建应用
      working-directory: ./main
      run: |
        CGO_ENABLED=0 go build -ldflags="-w -s" -o ../gejiesoft-pdf-tools
        ls -la ../gejiesoft-pdf-tools

  security:
    name: 安全检查
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Go环境
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
    
    - name: 运行Gosec安全扫描
      uses: securecodewarrior/github-action-gosec@master
      with:
        args: './main/...'
    
    - name: 运行依赖漏洞扫描
      working-directory: ./main
      run: |
        go install github.com/sonatypeoss/nancy@latest
        go list -json -m all | nancy sleuth

  docker:
    name: Docker构建
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: 登录Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: 构建并推送Docker镜像
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: |
          gejiesoft/pdf-tools:latest
          gejiesoft/pdf-tools:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  release:
    name: 发布
    runs-on: ubuntu-latest
    needs: [test, security]
    if: github.event_name == 'release'
    
    strategy:
      matrix:
        goos: [linux, windows, darwin]
        goarch: [amd64, arm64]
        exclude:
          - goos: windows
            goarch: arm64
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Go环境
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: 安装Python依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r main/ppt_generator/requirements.txt
    
    - name: 构建发布包
      working-directory: ./main
      env:
        GOOS: ${{ matrix.goos }}
        GOARCH: ${{ matrix.goarch }}
      run: |
        # 设置输出文件名
        if [ "$GOOS" = "windows" ]; then
          OUTPUT_NAME="gejiesoft-pdf-tools-${GOOS}-${GOARCH}.exe"
        else
          OUTPUT_NAME="gejiesoft-pdf-tools-${GOOS}-${GOARCH}"
        fi
        
        # 构建二进制文件
        CGO_ENABLED=0 go build -ldflags="-w -s -X main.version=${{ github.ref_name }}" -o "../${OUTPUT_NAME}"
        
        # 创建发布包
        cd ..
        mkdir -p "release-${GOOS}-${GOARCH}"
        cp "${OUTPUT_NAME}" "release-${GOOS}-${GOARCH}/"
        cp -r frontend "release-${GOOS}-${GOARCH}/"
        cp -r templates-ppt "release-${GOOS}-${GOARCH}/"
        cp -r main/ppt_generator "release-${GOOS}-${GOARCH}/"
        cp README.md "release-${GOOS}-${GOARCH}/"
        cp LICENSE "release-${GOOS}-${GOARCH}/"
        cp env.example "release-${GOOS}-${GOARCH}/.env.example"
        
        # 创建压缩包
        if [ "$GOOS" = "windows" ]; then
          zip -r "gejiesoft-pdf-tools-${GOOS}-${GOARCH}.zip" "release-${GOOS}-${GOARCH}"
        else
          tar -czf "gejiesoft-pdf-tools-${GOOS}-${GOARCH}.tar.gz" "release-${GOOS}-${GOARCH}"
        fi
    
    - name: 上传发布资产
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ github.event.release.upload_url }}
        asset_path: ./gejiesoft-pdf-tools-${{ matrix.goos }}-${{ matrix.goarch }}.${{ matrix.goos == 'windows' && 'zip' || 'tar.gz' }}
        asset_name: gejiesoft-pdf-tools-${{ matrix.goos }}-${{ matrix.goarch }}.${{ matrix.goos == 'windows' && 'zip' || 'tar.gz' }}
        asset_content_type: ${{ matrix.goos == 'windows' && 'application/zip' || 'application/gzip' }}

  deploy:
    name: 部署文档
    runs-on: ubuntu-latest
    needs: [test, security]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - name: 构建文档
      run: |
        # 这里可以添加文档构建逻辑
        # 比如使用 mkdocs, gitbook 等工具
        mkdir -p docs-build
        cp README.md docs-build/
        cp CONTRIBUTING.md docs-build/
        cp CHANGELOG.md docs-build/
        cp -r docs/* docs-build/
    
    - name: 部署到GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./docs-build

  notify:
    name: 通知
    runs-on: ubuntu-latest
    needs: [test, security, docker, release]
    if: always()
    
    steps:
    - name: 发送通知
      uses: 8398a7/action-slack@v3
      if: always()
      with:
        status: ${{ job.status }}
        author_name: GitHub Actions
        text: |
          项目: ${{ github.repository }}
          分支: ${{ github.ref }}
          提交: ${{ github.sha }}
          状态: ${{ job.status }}
        webhook_url: ${{ secrets.SLACK_WEBHOOK }} 