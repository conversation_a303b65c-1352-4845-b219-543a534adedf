本目录提供了一个测试头像抠图的示例
其中：phototemple 文件夹包含一百多张各式各样的人像照片照片，需要研究一种算法用于精确识别，并进行裁切处理为标准头像。
人像照片里面各种类型的照片都有，有的拍的清楚，有的拍的模糊，其中 hr_attendance_face_recognition_pro 文件夹内有Human.js 提供了完整的人脸检测、识别、分析解决方案。
我需要研究一种算法，能够准确识别出照片中的人脸，并进行裁切处理为标准头像,参考 标准头像占位符.png


一、准确识别人脸

1.对用户上传的图片进行人脸识别，要对个人简介中提取的头像进行人脸检测，通过468个3D关键点，基于虹膜分析，人脸检测与旋转追踪，准确识别到包含人脸的区域
2.对于面部分辨率较低的照片进行超分放大处理，再进行识别。
3.基于人脸识别的结果进行校正，使人脸方向于标准头像的人脸方向相同，保存校正后的图片
4.目前用于测试的phototemple文件夹内经过人工核验100%包含人脸
5.对于不包含人脸的图片，给出提示信息。

二、头像标准裁切

1、头像比例是1：1
2、根据已经准确定位面部的位置，包括图片中人脸的长度，宽度比例，比对标准头像中的人脸的长度、宽度，并根据面部位置进行裁切，获取裁切后的图片，必须要保留完成的面部，根据原图质量如果有肩膀，保留一部分肩膀，如果没有则裁剪出完整的头像，如果有胸部，腰部以下部分，都可以裁切掉，不需要。
3、感觉之前的人脸检测定位，头部应完成填充进 标准头像占位符.png

三、去除背景
1、除了人像主体以外的背景是透明的，确保没有阴影和杂物干扰
2、生成的图片，正方向1：1的


你的批量处理，是全部储存在内存里面的吗？因为我们本次的开发目标是测试算法，我要确保phototemple里面的照片全部抠图成功，后面我们还需要往我的PPT信息提取工具的主程序里面做集成。

