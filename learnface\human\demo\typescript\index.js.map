{"version": 3, "sources": ["index.ts"], "sourcesContent": ["/**\n * Human demo for browsers\n * @default Human Library\n * @summary <https://github.com/vladmandic/human>\n * <AUTHOR>\n * @copyright <https://github.com/vladmandic>\n * @license MIT\n */\n\nimport * as H from '../../dist/human.esm.js'; // equivalent of @vladmandic/Human\n\nconst width = 1920; // used by webcam config as well as human maximum resultion // can be anything, but resolutions higher than 4k will disable internal optimizations\n\nconst humanConfig: Partial<H.Config> = { // user configuration for human, used to fine-tune behavior\n  debug: true,\n  backend: 'webgl',\n  // cacheSensitivity: 0,\n  // cacheModels: false,\n  // warmup: 'none',\n  // modelBasePath: '../../models',\n  modelBasePath: 'https://vladmandic.github.io/human-models/models/',\n  filter: { enabled: true, equalization: false, flip: false },\n  face: { enabled: true, detector: { rotation: false }, mesh: { enabled: true }, attention: { enabled: false }, iris: { enabled: true }, description: { enabled: true }, emotion: { enabled: true }, antispoof: { enabled: true }, liveness: { enabled: true } },\n  body: { enabled: false },\n  hand: { enabled: false },\n  object: { enabled: false },\n  segmentation: { enabled: false },\n  gesture: { enabled: true },\n};\n\nconst human = new H.Human(humanConfig); // create instance of human with overrides from user configuration\n\nhuman.env.perfadd = false; // is performance data showing instant or total values\nhuman.draw.options.font = 'small-caps 18px \"Lato\"'; // set font used to draw labels when using draw methods\nhuman.draw.options.lineHeight = 20;\nhuman.draw.options.drawPoints = true; // draw points on face mesh\n// human.draw.options.fillPolygons = true;\n\nconst dom = { // grab instances of dom objects so we dont have to look them up later\n  video: document.getElementById('video') as HTMLVideoElement,\n  canvas: document.getElementById('canvas') as HTMLCanvasElement,\n  log: document.getElementById('log') as HTMLPreElement,\n  fps: document.getElementById('status') as HTMLPreElement,\n  perf: document.getElementById('performance') as HTMLDivElement,\n};\nconst timestamp = { detect: 0, draw: 0, tensors: 0, start: 0 }; // holds information used to calculate performance and possible memory leaks\nconst fps = { detectFPS: 0, drawFPS: 0, frames: 0, averageMs: 0 }; // holds calculated fps information for both detect and screen refresh\n\nconst log = (...msg) => { // helper method to output messages\n  dom.log.innerText += msg.join(' ') + '\\n';\n  console.log(...msg); // eslint-disable-line no-console\n};\nconst status = (msg) => dom.fps.innerText = msg; // print status element\nconst perf = (msg) => dom.perf.innerText = 'tensors:' + human.tf.memory().numTensors.toString() + ' | performance: ' + JSON.stringify(msg).replace(/\"|{|}/g, '').replace(/,/g, ' | '); // print performance element\n\nasync function detectionLoop() { // main detection loop\n  if (!dom.video.paused) {\n    if (timestamp.start === 0) timestamp.start = human.now();\n    // log('profiling data:', await human.profile(dom.video));\n    await human.detect(dom.video); // actual detection; were not capturing output in a local variable as it can also be reached via human.result\n    const tensors = human.tf.memory().numTensors; // check current tensor usage for memory leaks\n    if (tensors - timestamp.tensors !== 0) log('allocated tensors:', tensors - timestamp.tensors); // printed on start and each time there is a tensor leak\n    timestamp.tensors = tensors;\n    fps.detectFPS = Math.round(1000 * 1000 / (human.now() - timestamp.detect)) / 1000;\n    fps.frames++;\n    fps.averageMs = Math.round(1000 * (human.now() - timestamp.start) / fps.frames) / 1000;\n    if (fps.frames % 100 === 0 && !dom.video.paused) log('performance', { ...fps, tensors: timestamp.tensors });\n  }\n  timestamp.detect = human.now();\n  requestAnimationFrame(detectionLoop); // start new frame immediately\n}\n\nasync function drawLoop() { // main screen refresh loop\n  if (!dom.video.paused) {\n    const interpolated = human.next(human.result); // smoothen result using last-known results\n    const processed = await human.image(dom.video); // get current video frame, but enhanced with human.filters\n    human.draw.canvas(processed.canvas as HTMLCanvasElement, dom.canvas);\n\n    const opt: Partial<H.DrawOptions> = { bodyLabels: `person confidence [score] and ${human.result?.body?.[0]?.keypoints.length} keypoints` };\n    await human.draw.all(dom.canvas, interpolated, opt); // draw labels, boxes, lines, etc.\n    perf(interpolated.performance); // write performance data\n  }\n  const now = human.now();\n  fps.drawFPS = Math.round(1000 * 1000 / (now - timestamp.draw)) / 1000;\n  timestamp.draw = now;\n  status(dom.video.paused ? 'paused' : `fps: ${fps.detectFPS.toFixed(1).padStart(5, ' ')} detect | ${fps.drawFPS.toFixed(1).padStart(5, ' ')} draw`); // write status\n  setTimeout(drawLoop, 30); // use to slow down refresh from max refresh rate to target of 30 fps\n}\n\nasync function webCam() {\n  const devices = await human.webcam.enumerate();\n  const id = devices[0].deviceId; // use first available video source\n  const webcamStatus = await human.webcam.start({ element: dom.video, crop: false, width, id }); // use human webcam helper methods and associate webcam stream with a dom element\n  log(webcamStatus);\n  dom.canvas.width = human.webcam.width;\n  dom.canvas.height = human.webcam.height;\n  dom.canvas.onclick = async () => { // pause when clicked on screen and resume on next click\n    if (human.webcam.paused) await human.webcam.play();\n    else human.webcam.pause();\n  };\n}\n\nasync function main() { // main entry point\n  log('human version:', human.version, '| tfjs version:', human.tf.version['tfjs-core']);\n  log('platform:', human.env.platform, '| agent:', human.env.agent);\n  status('loading...');\n  await human.load(); // preload all models\n  log('backend:', human.tf.getBackend(), '| available:', human.env.backends);\n  log('models stats:', human.models.stats());\n  log('models loaded:', human.models.loaded());\n  log('environment', human.env);\n  status('initializing...');\n  await human.warmup(); // warmup function to initialize backend for future faster detection\n  await webCam(); // start webcam\n  await detectionLoop(); // start detection loop\n  await drawLoop(); // start draw loop\n}\n\nwindow.onload = main;\n"], "mappings": ";;;;;;AASA,UAAYA,MAAO,0BAEnB,IAAMC,EAAQ,KAERC,EAAiC,CACrC,MAAO,GACP,QAAS,QAKT,cAAe,oDACf,OAAQ,CAAE,QAAS,GAAM,aAAc,GAAO,KAAM,EAAM,EAC1D,KAAM,CAAE,QAAS,GAAM,SAAU,CAAE,SAAU,EAAM,EAAG,KAAM,CAAE,QAAS,EAAK,EAAG,UAAW,CAAE,QAAS,EAAM,EAAG,KAAM,CAAE,QAAS,EAAK,EAAG,YAAa,CAAE,QAAS,EAAK,EAAG,QAAS,CAAE,QAAS,EAAK,EAAG,UAAW,CAAE,QAAS,EAAK,EAAG,SAAU,CAAE,QAAS,EAAK,CAAE,EAC7P,KAAM,CAAE,QAAS,EAAM,EACvB,KAAM,CAAE,QAAS,EAAM,EACvB,OAAQ,CAAE,QAAS,EAAM,EACzB,aAAc,CAAE,QAAS,EAAM,EAC/B,QAAS,CAAE,QAAS,EAAK,CAC3B,EAEMC,EAAQ,IAAM,QAAMD,CAAW,EAErCC,EAAM,IAAI,QAAU,GACpBA,EAAM,KAAK,QAAQ,KAAO,yBAC1BA,EAAM,KAAK,QAAQ,WAAa,GAChCA,EAAM,KAAK,QAAQ,WAAa,GAGhC,IAAMC,EAAM,CACV,MAAO,SAAS,eAAe,OAAO,EACtC,OAAQ,SAAS,eAAe,QAAQ,EACxC,IAAK,SAAS,eAAe,KAAK,EAClC,IAAK,SAAS,eAAe,QAAQ,EACrC,KAAM,SAAS,eAAe,aAAa,CAC7C,EACMC,EAAY,CAAE,OAAQ,EAAG,KAAM,EAAG,QAAS,EAAG,MAAO,CAAE,EACvDC,EAAM,CAAE,UAAW,EAAG,QAAS,EAAG,OAAQ,EAAG,UAAW,CAAE,EAE1DC,EAAM,IAAIC,IAAQ,CACtBJ,EAAI,IAAI,WAAaI,EAAI,KAAK,GAAG,EAAI;AAAA,EACrC,QAAQ,IAAI,GAAGA,CAAG,CACpB,EACMC,EAAUD,GAAQJ,EAAI,IAAI,UAAYI,EACtCE,EAAQF,GAAQJ,EAAI,KAAK,UAAY,WAAaD,EAAM,GAAG,OAAO,EAAE,WAAW,SAAS,EAAI,mBAAqB,KAAK,UAAUK,CAAG,EAAE,QAAQ,SAAU,EAAE,EAAE,QAAQ,KAAM,KAAK,EAEpL,eAAeG,GAAgB,CAC7B,GAAI,CAACP,EAAI,MAAM,OAAQ,CACjBC,EAAU,QAAU,IAAGA,EAAU,MAAQF,EAAM,IAAI,GAEvD,MAAMA,EAAM,OAAOC,EAAI,KAAK,EAC5B,IAAMQ,EAAUT,EAAM,GAAG,OAAO,EAAE,WAC9BS,EAAUP,EAAU,UAAY,GAAGE,EAAI,qBAAsBK,EAAUP,EAAU,OAAO,EAC5FA,EAAU,QAAUO,EACpBN,EAAI,UAAY,KAAK,MAAM,IAAO,KAAQH,EAAM,IAAI,EAAIE,EAAU,OAAO,EAAI,IAC7EC,EAAI,SACJA,EAAI,UAAY,KAAK,MAAM,KAAQH,EAAM,IAAI,EAAIE,EAAU,OAASC,EAAI,MAAM,EAAI,IAC9EA,EAAI,OAAS,MAAQ,GAAK,CAACF,EAAI,MAAM,QAAQG,EAAI,cAAe,CAAE,GAAGD,EAAK,QAASD,EAAU,OAAQ,CAAC,CAC5G,CACAA,EAAU,OAASF,EAAM,IAAI,EAC7B,sBAAsBQ,CAAa,CACrC,CAEA,eAAeE,GAAW,CAxE1B,IAAAC,EAAAC,EAAAC,EAyEE,GAAI,CAACZ,EAAI,MAAM,OAAQ,CACrB,IAAMa,EAAed,EAAM,KAAKA,EAAM,MAAM,EACtCe,EAAY,MAAMf,EAAM,MAAMC,EAAI,KAAK,EAC7CD,EAAM,KAAK,OAAOe,EAAU,OAA6Bd,EAAI,MAAM,EAEnE,IAAMe,EAA8B,CAAE,WAAY,kCAAiCH,GAAAD,GAAAD,EAAAX,EAAM,SAAN,YAAAW,EAAc,OAAd,YAAAC,EAAqB,KAArB,YAAAC,EAAyB,UAAU,MAAM,YAAa,EACzI,MAAMb,EAAM,KAAK,IAAIC,EAAI,OAAQa,EAAcE,CAAG,EAClDT,EAAKO,EAAa,WAAW,CAC/B,CACA,IAAMG,EAAMjB,EAAM,IAAI,EACtBG,EAAI,QAAU,KAAK,MAAM,IAAO,KAAQc,EAAMf,EAAU,KAAK,EAAI,IACjEA,EAAU,KAAOe,EACjBX,EAAOL,EAAI,MAAM,OAAS,SAAW,QAAQE,EAAI,UAAU,QAAQ,CAAC,EAAE,SAAS,EAAG,GAAG,CAAC,aAAaA,EAAI,QAAQ,QAAQ,CAAC,EAAE,SAAS,EAAG,GAAG,CAAC,OAAO,EACjJ,WAAWO,EAAU,EAAE,CACzB,CAEA,eAAeQ,GAAS,CAEtB,IAAMC,GADU,MAAMnB,EAAM,OAAO,UAAU,GAC1B,CAAC,EAAE,SAChBoB,EAAe,MAAMpB,EAAM,OAAO,MAAM,CAAE,QAASC,EAAI,MAAO,KAAM,GAAO,MAAAH,EAAO,GAAAqB,CAAG,CAAC,EAC5Ff,EAAIgB,CAAY,EAChBnB,EAAI,OAAO,MAAQD,EAAM,OAAO,MAChCC,EAAI,OAAO,OAASD,EAAM,OAAO,OACjCC,EAAI,OAAO,QAAU,SAAY,CAC3BD,EAAM,OAAO,OAAQ,MAAMA,EAAM,OAAO,KAAK,EAC5CA,EAAM,OAAO,MAAM,CAC1B,CACF,CAEA,eAAeqB,GAAO,CACpBjB,EAAI,iBAAkBJ,EAAM,QAAS,kBAAmBA,EAAM,GAAG,QAAQ,WAAW,CAAC,EACrFI,EAAI,YAAaJ,EAAM,IAAI,SAAU,WAAYA,EAAM,IAAI,KAAK,EAChEM,EAAO,YAAY,EACnB,MAAMN,EAAM,KAAK,EACjBI,EAAI,WAAYJ,EAAM,GAAG,WAAW,EAAG,eAAgBA,EAAM,IAAI,QAAQ,EACzEI,EAAI,gBAAiBJ,EAAM,OAAO,MAAM,CAAC,EACzCI,EAAI,iBAAkBJ,EAAM,OAAO,OAAO,CAAC,EAC3CI,EAAI,cAAeJ,EAAM,GAAG,EAC5BM,EAAO,iBAAiB,EACxB,MAAMN,EAAM,OAAO,EACnB,MAAMkB,EAAO,EACb,MAAMV,EAAc,EACpB,MAAME,EAAS,CACjB,CAEA,OAAO,OAASW", "names": ["H", "width", "humanConfig", "human", "dom", "timestamp", "fps", "log", "msg", "status", "perf", "detectionLoop", "tensors", "drawLoop", "_a", "_b", "_c", "interpolated", "processed", "opt", "now", "webCam", "id", "webcamStatus", "main"]}