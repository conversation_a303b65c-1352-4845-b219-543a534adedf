# -*- coding: utf-8 -*-

{
    'name': 'HR考勤人脸识-小工具图像预览',
    'summary': """添加原始大小的功能预览（打开/弹出）图像
，放大图像，产品图像预览，图片产品预览放大 """,
    'description': """
这是<field-widget=“image”>窗口小部件图像的扩展
==============================================
""",
    'author': "GejieSoft Inc",
    'website': "https://www.gejiesoft.com",

    # 类别可用于筛选模块列表中的模块
    'category': "Tools",
    'version': '********',
    # 此模块正常工作所需的前置模块
    'depends': ['web', 'mail'],
    "license": "LGPL-3",
    'images': [
            'static/description/preview.png',

    ],

    'assets': {
        'web.assets_backend': [
            'field_image_preview/static/src/js/image_field.js',
            'field_image_preview/static/src/xml/image.xml',
        ],
    },

    'installable': True,
    'application': False,
    # 如果为True，则在安装所有依赖项时将自动安装模块
    'auto_install': False,
}
