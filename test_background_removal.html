<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>背景移除功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .upload-area.dragover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        
        #fileInput {
            display: none;
        }
        
        .result-area {
            display: none;
            margin-top: 30px;
        }
        
        .image-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .image-container {
            text-align: center;
        }
        
        .image-container h3 {
            margin-bottom: 10px;
            color: #333;
        }
        
        .image-container img {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .processing {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .error {
            color: #e74c3c;
            background: #ffeaea;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            display: none;
        }
        
        .success {
            color: #27ae60;
            background: #eafaf1;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎭 背景移除功能测试</h1>
            <p>测试 rembg + u2net_human_seg 模型的背景移除效果</p>
        </div>
        
        <div class="content">
            <div class="upload-area" id="uploadArea">
                <div style="font-size: 48px; margin-bottom: 20px;">📸</div>
                <div style="font-size: 18px; margin-bottom: 10px;">点击或拖拽上传图片</div>
                <div style="color: #666;">支持 JPG、PNG、JPEG 格式</div>
            </div>
            <input type="file" id="fileInput" accept="image/*">
            
            <div class="processing" id="processing">
                <div class="spinner"></div>
                <div>正在处理中，请稍候...</div>
                <div style="font-size: 14px; color: #666; margin-top: 10px;">
                    首次使用可能需要下载模型文件（约176MB）
                </div>
            </div>
            
            <div class="error" id="errorMessage"></div>
            <div class="success" id="successMessage"></div>
            
            <div class="result-area" id="resultArea">
                <h2>处理结果</h2>
                <div class="image-comparison">
                    <div class="image-container">
                        <h3>原始图片</h3>
                        <img id="originalImage" alt="原始图片">
                    </div>
                    <div class="image-container">
                        <h3>背景移除后</h3>
                        <img id="processedImage" alt="处理后图片">
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn" onclick="downloadResult()">下载结果</button>
                    <button class="btn" onclick="resetTest()">重新测试</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const processing = document.getElementById('processing');
        const resultArea = document.getElementById('resultArea');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');
        const originalImage = document.getElementById('originalImage');
        const processedImage = document.getElementById('processedImage');
        
        let processedImageData = null;

        // 上传区域点击事件
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // 拖拽事件
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // 文件选择事件
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        // 处理文件
        async function handleFile(file) {
            if (!file.type.startsWith('image/')) {
                showError('请选择图片文件');
                return;
            }

            hideMessages();
            showProcessing();

            try {
                // 读取文件为base64
                const imageData = await fileToBase64(file);
                
                // 显示原始图片
                originalImage.src = imageData;
                
                // 调用背景移除API
                const response = await fetch('/api/remove-background', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        image_data: imageData,
                        model: 'u2net_human_seg'
                    })
                });

                const result = await response.json();

                if (result.success) {
                    processedImageData = result.data.image_data;
                    processedImage.src = processedImageData;
                    
                    hideProcessing();
                    showResult();
                    showSuccess('背景移除成功！');
                } else {
                    throw new Error(result.message);
                }

            } catch (error) {
                console.error('处理失败:', error);
                hideProcessing();
                showError('处理失败: ' + error.message);
            }
        }

        // 文件转base64
        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }

        // 显示/隐藏元素
        function showProcessing() {
            processing.style.display = 'block';
            resultArea.style.display = 'none';
        }

        function hideProcessing() {
            processing.style.display = 'none';
        }

        function showResult() {
            resultArea.style.display = 'block';
        }

        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
        }

        function showSuccess(message) {
            successMessage.textContent = message;
            successMessage.style.display = 'block';
        }

        function hideMessages() {
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
        }

        // 下载结果
        function downloadResult() {
            if (processedImageData) {
                const link = document.createElement('a');
                link.href = processedImageData;
                link.download = 'background_removed.png';
                link.click();
            }
        }

        // 重新测试
        function resetTest() {
            resultArea.style.display = 'none';
            hideMessages();
            fileInput.value = '';
            processedImageData = null;
        }
    </script>
</body>
</html>
