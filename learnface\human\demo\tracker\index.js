/*
  Human
  homepage: <https://github.com/vladmandic/human>
  author: <https://github.com/vladmandic>'
*/

import*as U from"../../dist/human.esm.js";var J=Object.defineProperty,z=Object.getOwnPropertyNames,w=(l,y)=>J(l,"name",{value:y,configurable:!0}),S=(l,y)=>function(){return y||(0,l[z(l)[0]])((y={exports:{}}).exports,y),y.exports},q=S({"node_modules/.pnpm/uuid@3.2.1/node_modules/uuid/lib/rng-browser.js"(l,y){var f=typeof crypto!="undefined"&&crypto.getRandomValues.bind(crypto)||typeof msCrypto!="undefined"&&msCrypto.getRandomValues.bind(msCrypto);f?(c=new Uint8Array(16),y.exports=w(function(){return f(c),c},"whatwgRNG")):(s=new Array(16),y.exports=w(function(){for(var g=0,h;g<16;g++)g&3||(h=Math.random()*4294967296),s[g]=h>>>((g&3)<<3)&255;return s},"mathRNG"));var c,s}}),X=S({"node_modules/.pnpm/uuid@3.2.1/node_modules/uuid/lib/bytesToUuid.js"(l,y){var f=[];for(s=0;s<256;++s)f[s]=(s+256).toString(16).substr(1);function c(i,g){var h=g||0,e=f;return e[i[h++]]+e[i[h++]]+e[i[h++]]+e[i[h++]]+"-"+e[i[h++]]+e[i[h++]]+"-"+e[i[h++]]+e[i[h++]]+"-"+e[i[h++]]+e[i[h++]]+"-"+e[i[h++]]+e[i[h++]]+e[i[h++]]+e[i[h++]]+e[i[h++]]+e[i[h++]]}w(c,"bytesToUuid"),y.exports=c;var s}}),j=S({"node_modules/.pnpm/uuid@3.2.1/node_modules/uuid/v4.js"(l,y){var f=q(),c=X();function s(i,g,h){var e=g&&h||0;typeof i=="string"&&(g=i==="binary"?new Array(16):null,i=null),i=i||{};var n=i.random||(i.rng||f)();if(n[6]=n[6]&15|64,n[8]=n[8]&63|128,g)for(var t=0;t<16;++t)g[e+t]=n[t];return g||c(n)}w(s,"v4"),y.exports=s}}),C=S({"utils.js"(l){l.isDetectionTooLarge=(c,s)=>c.w>=s;var y=w((c,s)=>{let i=c.x-c.w/2,g=c.x+c.w/2,h=c.y-c.h/2,e=c.y+c.h/2;return s.x>=i&&s.x<=g&&s.y>=h&&s.y<=e},"isInsideArea");l.isInsideArea=y,l.isInsideSomeAreas=(c,s)=>c.some(g=>y(g,s)),l.ignoreObjectsNotToDetect=(c,s)=>c.filter(i=>s.indexOf(i.name)>-1);var f=w(c=>({x0:c.x-c.w/2,y0:c.y-c.h/2,x1:c.x+c.w/2,y1:c.y+c.h/2}),"getRectangleEdges");l.getRectangleEdges=f,l.iouAreas=(c,s)=>{var i=f(c),g=f(s),h=Math.max(i.x0,g.x0),e=Math.max(i.y0,g.y0),n=Math.min(i.x1,g.x1),t=Math.min(i.y1,g.y1);if(n-h<=0||t-e<=0)return 0;{let r=c.w*c.h,a=s.w*s.h,o=(n-h)*(t-e),m=r+a-o;return o/m}},l.computeVelocityVector=(c,s,i)=>({dx:(s.x-c.x)/i,dy:(s.y-c.y)/i}),l.computeBearingIn360=function(c,s){var i=Math.atan(c/s)/(Math.PI/180);return i>0?s>0?i:180+i:c>0?180+i:360+i}}}),Y=S({"ItemTracked.js"(l){var y=j(),f=C().computeBearingIn360,c=C().computeVelocityVector;l.ITEM_HISTORY_MAX_LENGTH=15;var s=0;l.ItemTracked=function(i,g,h,e){var n=h,t={};return t.available=!0,t.delete=!1,t.fastDelete=e,t.frameUnmatchedLeftBeforeDying=h,t.isZombie=!1,t.appearFrame=g,t.disappearFrame=null,t.disappearArea={},t.nameCount={},t.nameCount[i.name]=1,t.x=i.x,t.y=i.y,t.w=i.w,t.h=i.h,t.name=i.name,t.confidence=i.confidence,t.itemHistory=[],t.itemHistory.push({x:i.x,y:i.y,w:i.w,h:i.h,confidence:i.confidence}),t.itemHistory.length>=l.ITEM_HISTORY_MAX_LENGTH&&t.itemHistory.shift(),t.velocity={dx:0,dy:0},t.nbTimeMatched=1,t.id=y(),t.idDisplay=s,s++,t.update=function(r,a){this.disappearFrame&&(this.disappearFrame=null,this.disappearArea={}),this.isZombie=!1,this.nbTimeMatched+=1,this.x=r.x,this.y=r.y,this.w=r.w,this.h=r.h,this.confidence=r.confidence,this.itemHistory.push({x:this.x,y:this.y,w:this.w,h:this.h,confidence:this.confidence}),t.itemHistory.length>=l.ITEM_HISTORY_MAX_LENGTH&&t.itemHistory.shift(),this.name=r.name,this.nameCount[r.name]?this.nameCount[r.name]++:this.nameCount[r.name]=1,this.frameUnmatchedLeftBeforeDying=n,this.velocity=this.updateVelocityVector()},t.makeAvailable=function(){return this.available=!0,this},t.makeUnavailable=function(){return this.available=!1,this},t.countDown=function(r){this.disappearFrame===null&&(this.disappearFrame=r,this.disappearArea={x:this.x,y:this.y,w:this.w,h:this.h}),this.frameUnmatchedLeftBeforeDying--,this.isZombie=!0,this.fastDelete&&this.nbTimeMatched<=1&&(this.frameUnmatchedLeftBeforeDying=-1)},t.updateTheoricalPositionAndSize=function(){this.itemHistory.push({x:this.x,y:this.y,w:this.w,h:this.h,confidence:this.confidence}),t.itemHistory.length>=l.ITEM_HISTORY_MAX_LENGTH&&t.itemHistory.shift(),this.x=this.x+this.velocity.dx,this.y=this.y+this.velocity.dy},t.predictNextPosition=function(){return{x:this.x+this.velocity.dx,y:this.y+this.velocity.dy,w:this.w,h:this.h}},t.isDead=function(){return this.frameUnmatchedLeftBeforeDying<0},t.updateVelocityVector=function(){if(l.ITEM_HISTORY_MAX_LENGTH<=2)return{dx:void 0,dy:void 0};if(this.itemHistory.length<=l.ITEM_HISTORY_MAX_LENGTH){let r=this.itemHistory[0],a=this.itemHistory[this.itemHistory.length-1];return c(r,a,this.itemHistory.length)}else{let r=this.itemHistory[this.itemHistory.length-l.ITEM_HISTORY_MAX_LENGTH],a=this.itemHistory[this.itemHistory.length-1];return c(r,a,l.ITEM_HISTORY_MAX_LENGTH)}},t.getMostlyMatchedName=function(){var r=0,a="";return Object.keys(this.nameCount).map(o=>{this.nameCount[o]>r&&(a=o,r=this.nameCount[o])}),a},t.toJSONDebug=function(r=!0){return{id:this.id,idDisplay:this.idDisplay,x:r?parseInt(this.x,10):this.x,y:r?parseInt(this.y,10):this.y,w:r?parseInt(this.w,10):this.w,h:r?parseInt(this.h,10):this.h,confidence:Math.round(this.confidence*100)/100,bearing:parseInt(f(this.velocity.dx,-this.velocity.dy)),name:this.getMostlyMatchedName(),isZombie:this.isZombie,appearFrame:this.appearFrame,disappearFrame:this.disappearFrame}},t.toJSON=function(r=!0){return{id:this.idDisplay,x:r?parseInt(this.x,10):this.x,y:r?parseInt(this.y,10):this.y,w:r?parseInt(this.w,10):this.w,h:r?parseInt(this.h,10):this.h,confidence:Math.round(this.confidence*100)/100,bearing:parseInt(f(this.velocity.dx,-this.velocity.dy),10),name:this.getMostlyMatchedName(),isZombie:this.isZombie}},t.toMOT=function(r){return`${r},${this.idDisplay},${this.x-this.w/2},${this.y-this.h/2},${this.w},${this.h},${this.confidence/100},-1,-1,-1`},t.toJSONGenericInfo=function(){return{id:this.id,idDisplay:this.idDisplay,appearFrame:this.appearFrame,disappearFrame:this.disappearFrame,disappearArea:this.disappearArea,nbActiveFrame:this.disappearFrame-this.appearFrame,name:this.getMostlyMatchedName()}},t},l.reset=function(){s=0}}}),W=S({"lib/kdTree-min.js"(l){(function(y,f){typeof define=="function"&&define.amd?define(["exports"],f):f(typeof l=="object"?l:y)})(l,function(y){function f(i,g,h){this.obj=i,this.left=null,this.right=null,this.parent=h,this.dimension=g}w(f,"Node");function c(i,g,h){var e=this;function n(r,a,o){var m=a%h.length,u,d;return r.length===0?null:r.length===1?new f(r[0],m,o):(r.sort(function(I,p){return I[h[m]]-p[h[m]]}),u=Math.floor(r.length/2),d=new f(r[u],m,o),d.left=n(r.slice(0,u),a+1,d),d.right=n(r.slice(u+1),a+1,d),d)}w(n,"buildTree");function t(r){e.root=r;function a(o){o.left&&(o.left.parent=o,a(o.left)),o.right&&(o.right.parent=o,a(o.right))}w(a,"restoreParent"),a(e.root)}w(t,"loadTree"),Array.isArray(i)?this.root=n(i,0,null):t(i,g,h),this.toJSON=function(r){r||(r=this.root);var a=new f(r.obj,r.dimension,null);return r.left&&(a.left=e.toJSON(r.left)),r.right&&(a.right=e.toJSON(r.right)),a},this.insert=function(r){function a(d,I){if(d===null)return I;var p=h[d.dimension];return r[p]<d.obj[p]?a(d.left,d):a(d.right,d)}w(a,"innerSearch");var o=a(this.root,null),m,u;if(o===null){this.root=new f(r,0,null);return}m=new f(r,(o.dimension+1)%h.length,o),u=h[o.dimension],r[u]<o.obj[u]?o.left=m:o.right=m},this.remove=function(r){var a;function o(u){if(u===null)return null;if(u.obj===r)return u;var d=h[u.dimension];return r[d]<u.obj[d]?o(u.left,u):o(u.right,u)}w(o,"nodeSearch");function m(u){var d,I,p;function v(b,k){var T,O,x,E,L;return b===null?null:(T=h[k],b.dimension===k?b.left!==null?v(b.left,k):b:(O=b.obj[T],x=v(b.left,k),E=v(b.right,k),L=b,x!==null&&x.obj[T]<O&&(L=x),E!==null&&E.obj[T]<L.obj[T]&&(L=E),L))}if(w(v,"findMin"),u.left===null&&u.right===null){if(u.parent===null){e.root=null;return}p=h[u.parent.dimension],u.obj[p]<u.parent.obj[p]?u.parent.left=null:u.parent.right=null;return}u.right!==null?(d=v(u.right,u.dimension),I=d.obj,m(d),u.obj=I):(d=v(u.left,u.dimension),I=d.obj,m(d),u.right=u.left,u.left=null,u.obj=I)}w(m,"removeNode"),a=o(e.root),a!==null&&m(a)},this.nearest=function(r,a,o){var m,u,d;d=new s(function(p){return-p[1]});function I(p){var v,b=h[p.dimension],k=g(r,p.obj),T={},O,x,E;function L($,G){d.push([$,G]),d.size()>a&&d.pop()}for(w(L,"saveNode"),E=0;E<h.length;E+=1)E===p.dimension?T[h[E]]=r[h[E]]:T[h[E]]=p.obj[h[E]];if(O=g(T,p.obj),p.right===null&&p.left===null){(d.size()<a||k<d.peek()[1])&&L(p,k);return}p.right===null?v=p.left:p.left===null?v=p.right:r[b]<p.obj[b]?v=p.left:v=p.right,I(v),(d.size()<a||k<d.peek()[1])&&L(p,k),(d.size()<a||Math.abs(O)<d.peek()[1])&&(v===p.left?x=p.right:x=p.left,x!==null&&I(x))}if(w(I,"nearestSearch"),o)for(m=0;m<a;m+=1)d.push([null,o]);for(e.root&&I(e.root),u=[],m=0;m<Math.min(a,d.content.length);m+=1)d.content[m][0]&&u.push([d.content[m][0].obj,d.content[m][1]]);return u},this.balanceFactor=function(){function r(o){return o===null?0:Math.max(r(o.left),r(o.right))+1}w(r,"height");function a(o){return o===null?0:a(o.left)+a(o.right)+1}return w(a,"count"),r(e.root)/(Math.log(a(e.root))/Math.log(2))}}w(c,"kdTree");function s(i){this.content=[],this.scoreFunction=i}w(s,"BinaryHeap"),s.prototype={push:function(i){this.content.push(i),this.bubbleUp(this.content.length-1)},pop:function(){var i=this.content[0],g=this.content.pop();return this.content.length>0&&(this.content[0]=g,this.sinkDown(0)),i},peek:function(){return this.content[0]},remove:function(i){for(var g=this.content.length,h=0;h<g;h++)if(this.content[h]==i){var e=this.content.pop();h!=g-1&&(this.content[h]=e,this.scoreFunction(e)<this.scoreFunction(i)?this.bubbleUp(h):this.sinkDown(h));return}throw new Error("Node not found.")},size:function(){return this.content.length},bubbleUp:function(i){for(var g=this.content[i];i>0;){var h=Math.floor((i+1)/2)-1,e=this.content[h];if(this.scoreFunction(g)<this.scoreFunction(e))this.content[h]=g,this.content[i]=e,i=h;else break}},sinkDown:function(i){for(var g=this.content.length,h=this.content[i],e=this.scoreFunction(h);;){var n=(i+1)*2,t=n-1,r=null;if(t<g){var a=this.content[t],o=this.scoreFunction(a);o<e&&(r=t)}if(n<g){var m=this.content[n],u=this.scoreFunction(m);u<(r==null?e:o)&&(r=n)}if(r!=null)this.content[i]=this.content[r],this.content[r]=h,i=r;else break}}},y.kdTree=c,y.BinaryHeap=s})}}),K=S({"node_modules/.pnpm/munkres-js@1.2.2/node_modules/munkres-js/munkres.js"(l,y){var f=parseInt(Number.MAX_SAFE_INTEGER/2)||4503599627370496,c=0;function s(){this.C=null,this.row_covered=[],this.col_covered=[],this.n=0,this.Z0_r=0,this.Z0_c=0,this.marked=null,this.path=null}w(s,"Munkres"),s.prototype.pad_matrix=function(e,n){n=n||c;var t=0,r=e.length,a;for(a=0;a<r;++a)e[a].length>t&&(t=e[a].length);r=t>r?t:r;var o=[];for(a=0;a<r;++a){for(var m=e[a]||[],u=m.slice();r>u.length;)u.push(n);o.push(u)}return o},s.prototype.compute=function(e,n){n=n||{},n.padValue=n.padValue||c,this.C=this.pad_matrix(e,n.padValue),this.n=this.C.length,this.original_length=e.length,this.original_width=e[0].length;for(var t=[];t.length<this.n;)t.push(!1);this.row_covered=t.slice(),this.col_covered=t.slice(),this.Z0_r=0,this.Z0_c=0,this.path=this.__make_matrix(this.n*2,0),this.marked=this.__make_matrix(this.n,0);for(var r=1,a={1:this.__step1,2:this.__step2,3:this.__step3,4:this.__step4,5:this.__step5,6:this.__step6};;){var o=a[r];if(!o)break;r=o.apply(this)}for(var m=[],u=0;u<this.original_length;++u)for(var d=0;d<this.original_width;++d)this.marked[u][d]==1&&m.push([u,d]);return m},s.prototype.__make_matrix=function(e,n){for(var t=[],r=0;r<e;++r){t[r]=[];for(var a=0;a<e;++a)t[r][a]=n}return t},s.prototype.__step1=function(){for(var e=0;e<this.n;++e)for(var n=Math.min.apply(Math,this.C[e]),t=0;t<this.n;++t)this.C[e][t]-=n;return 2},s.prototype.__step2=function(){for(var e=0;e<this.n;++e)for(var n=0;n<this.n;++n)if(this.C[e][n]===0&&!this.col_covered[n]&&!this.row_covered[e]){this.marked[e][n]=1,this.col_covered[n]=!0,this.row_covered[e]=!0;break}return this.__clear_covers(),3},s.prototype.__step3=function(){for(var e=0,n=0;n<this.n;++n)for(var t=0;t<this.n;++t)this.marked[n][t]==1&&this.col_covered[t]==!1&&(this.col_covered[t]=!0,++e);return e>=this.n?7:4},s.prototype.__step4=function(){for(var e=!1,n=-1,t=-1,r=-1;!e;){var a=this.__find_a_zero();if(n=a[0],t=a[1],n<0)return 6;if(this.marked[n][t]=2,r=this.__find_star_in_row(n),r>=0)t=r,this.row_covered[n]=!0,this.col_covered[t]=!1;else return this.Z0_r=n,this.Z0_c=t,5}},s.prototype.__step5=function(){var e=0;this.path[e][0]=this.Z0_r,this.path[e][1]=this.Z0_c;for(var n=!1;!n;){var t=this.__find_star_in_col(this.path[e][1]);if(t>=0?(e++,this.path[e][0]=t,this.path[e][1]=this.path[e-1][1]):n=!0,!n){var r=this.__find_prime_in_row(this.path[e][0]);e++,this.path[e][0]=this.path[e-1][0],this.path[e][1]=r}}return this.__convert_path(this.path,e),this.__clear_covers(),this.__erase_primes(),3},s.prototype.__step6=function(){for(var e=this.__find_smallest(),n=0;n<this.n;++n)for(var t=0;t<this.n;++t)this.row_covered[n]&&(this.C[n][t]+=e),this.col_covered[t]||(this.C[n][t]-=e);return 4},s.prototype.__find_smallest=function(){for(var e=f,n=0;n<this.n;++n)for(var t=0;t<this.n;++t)!this.row_covered[n]&&!this.col_covered[t]&&e>this.C[n][t]&&(e=this.C[n][t]);return e},s.prototype.__find_a_zero=function(){for(var e=0;e<this.n;++e)for(var n=0;n<this.n;++n)if(this.C[e][n]===0&&!this.row_covered[e]&&!this.col_covered[n])return[e,n];return[-1,-1]},s.prototype.__find_star_in_row=function(e){for(var n=0;n<this.n;++n)if(this.marked[e][n]==1)return n;return-1},s.prototype.__find_star_in_col=function(e){for(var n=0;n<this.n;++n)if(this.marked[n][e]==1)return n;return-1},s.prototype.__find_prime_in_row=function(e){for(var n=0;n<this.n;++n)if(this.marked[e][n]==2)return n;return-1},s.prototype.__convert_path=function(e,n){for(var t=0;t<=n;++t)this.marked[e[t][0]][e[t][1]]=this.marked[e[t][0]][e[t][1]]==1?0:1},s.prototype.__clear_covers=function(){for(var e=0;e<this.n;++e)this.row_covered[e]=!1,this.col_covered[e]=!1},s.prototype.__erase_primes=function(){for(var e=0;e<this.n;++e)for(var n=0;n<this.n;++n)this.marked[e][n]==2&&(this.marked[e][n]=0)};function i(e,n){var t,r;if(!n){var a=-1/0;for(t=0;t<e.length;++t)for(r=0;r<e[t].length;++r)e[t][r]>a&&(a=e[t][r]);n=w(function(u){return a-u},"inversion_function")}var o=[];for(t=0;t<e.length;++t){var m=e[t];for(o[t]=[],r=0;r<m.length;++r)o[t][r]=n(e[t][r])}return o}w(i,"make_cost_matrix");function g(e){var n=[],t,r;for(t=0;t<e.length;++t)for(r=0;r<e[t].length;++r){var a=String(e[t][r]).length;(!n[r]||a>=n[r])&&(n[r]=a)}var o="";for(t=0;t<e.length;++t){for(r=0;r<e[t].length;++r){for(var m=String(e[t][r]);m.length<n[r];)m=" "+m;o+=m,r!=e[t].length-1&&(o+=" ")}t!=e[t].length-1&&(o+=`
`)}return o}w(g,"format_matrix");function h(e,n){var t=new s;return t.compute(e,n)}w(h,"computeMunkres"),h.version="1.2.2",h.format_matrix=g,h.make_cost_matrix=i,h.Munkres=s,typeof y!="undefined"&&y.exports&&(y.exports=h)}}),Q=S({"tracker.js"(l){var y=Y(),f=y.ItemTracked,c=W().kdTree,s=C().iouAreas,i=K(),g=!1,h=w(function(a,o){var m=s(a,o),u=1-m;return u>1-e.iouLimit&&(u=e.distanceLimit+1),u},"iouDistance"),e={unMatchedFramesTolerance:5,iouLimit:.05,fastDelete:!0,distanceFunc:h,distanceLimit:1e4,matchingAlgorithm:"kdTree"},n=new Map,t=new Map,r=!1;l.computeDistance=h,l.updateTrackedItemsWithNewFrame=function(a,o){var m=new c(Array.from(n.values()),e.distanceFunc,["x","y","w","h"]),u=new c(a,e.distanceFunc,["x","y","w","h"]);if(n.size===0)a.forEach(function(v){var b=new f(v,o,e.unMatchedFramesTolerance,e.fastDelete);n.set(b.id,b),m.insert(b)});else{var d=new Array(a.length);if(d.fill(!1),a.length>0)if(e.matchingAlgorithm==="munkres"){var I=Array.from(n.keys()),p=Array.from(n.values()).map(v=>{var b=v.predictNextPosition();return a.map(k=>e.distanceFunc(b,k))});n.forEach(function(v){v.makeAvailable()}),i(p).filter(v=>p[v[0]][v[1]]<=e.distanceLimit).forEach(v=>{var b=n.get(I[v[0]]),k=a[v[1]];d[v[1]]={idDisplay:b.idDisplay},b.makeUnavailable().update(k,o)}),d.forEach(function(v,b){if(!v&&Math.min(...p.map(T=>T[b]))>e.distanceLimit){var k=f(a[b],o,e.unMatchedFramesTolerance,e.fastDelete);n.set(k.id,k),k.makeUnavailable(),p.push(a.map(T=>e.distanceFunc(k,T)))}})}else if(e.matchingAlgorithm==="kdTree")n.forEach(function(v){var b=v.predictNextPosition();v.makeAvailable();var k=u.nearest(b,1,e.distanceLimit)[0],T=u.nearest(v,1,e.distanceLimit)[0],O=u.nearest(b,2,e.distanceLimit);if(k){var x=a.indexOf(k[0]);if(!d[x]){d[x]={idDisplay:v.idDisplay};var E=a[x];n.get(v.id).makeUnavailable().update(E,o)}}});else throw`Unknown matching algorithm "${e.matchingAlgorithm}"`;else g&&console.log("[Tracker] Nothing detected for frame n\xBA"+o),n.forEach(function(v){v.makeAvailable()});e.matchingAlgorithm==="kdTree"&&n.size>0&&(m=new c(Array.from(n.values()),e.distanceFunc,["x","y","w","h"]),d.forEach(function(v,b){if(!v){var k=m.nearest(a[b],1,e.distanceLimit)[0];if(!k){var T=f(a[b],o,e.unMatchedFramesTolerance,e.fastDelete);n.set(T.id,T),m.insert(T),T.makeUnavailable()}}})),n.forEach(function(v){v.available&&(v.countDown(o),v.updateTheoricalPositionAndSize(),v.isDead()&&(n.delete(v.id),m.remove(v),r&&t.set(v.id,v)))})}},l.reset=function(){n=new Map,t=new Map,y.reset()},l.setParams=function(a){Object.keys(a).forEach(o=>{e[o]=a[o]})},l.enableKeepInMemory=function(){r=!0},l.disableKeepInMemory=function(){r=!1},l.getJSONOfTrackedItems=function(a=!0){return Array.from(n.values()).map(function(o){return o.toJSON(a)})},l.getJSONDebugOfTrackedItems=function(a=!0){return Array.from(n.values()).map(function(o){return o.toJSONDebug(a)})},l.getTrackedItemsInMOTFormat=function(a){return Array.from(n.values()).map(function(o){return o.toMOT(a)})},l.getAllTrackedItems=function(){return t},l.getJSONOfAllTrackedItems=function(){return Array.from(t.values()).map(function(a){return a.toJSONGenericInfo()})}}}),D=Q();var R={debug:!0,backend:"webgl",modelBasePath:"https://vladmandic.github.io/human-models/models",filter:{enabled:!0,equalization:!1,flip:!1},face:{enabled:!0,detector:{rotation:!1,maxDetected:10,minConfidence:.3},mesh:{enabled:!0},attention:{enabled:!1},iris:{enabled:!1},description:{enabled:!1},emotion:{enabled:!1},antispoof:{enabled:!1},liveness:{enabled:!1}},body:{enabled:!1,maxDetected:6,modelPath:"movenet-multipose.json"},hand:{enabled:!1},object:{enabled:!1,maxDetected:10},segmentation:{enabled:!1},gesture:{enabled:!1}},F={unMatchedFramesTolerance:100,iouLimit:.05,fastDelete:!1,distanceLimit:1e4,matchingAlgorithm:"kdTree"},M=new U.Human(R),_={video:document.getElementById("video"),canvas:document.getElementById("canvas"),log:document.getElementById("log"),fps:document.getElementById("status"),tracker:document.getElementById("tracker"),interpolation:document.getElementById("interpolation"),config:document.getElementById("config"),ctx:document.getElementById("canvas").getContext("2d")},A={detect:0,draw:0,tensors:0,start:0},N={detectFPS:0,drawFPS:0,frames:0,averageMs:0},H=(...l)=>{_.log.innerText+=l.join(" ")+`
`,console.log(...l)},B=l=>_.fps.innerText=l;async function V(){if(!_.video.paused&&_.video.readyState>=2){A.start===0&&(A.start=M.now()),await M.detect(_.video,R);let l=M.tf.memory().numTensors;l-A.tensors!==0&&H("allocated tensors:",l-A.tensors),A.tensors=l,N.detectFPS=Math.round(1e3*1e3/(M.now()-A.detect))/1e3,N.frames++,N.averageMs=Math.round(1e3*(M.now()-A.start)/N.frames)/1e3}A.detect=M.now(),requestAnimationFrame(V)}function Z(){if(!_.video.paused&&_.video.readyState>=2){let y=_.interpolation.checked?M.next(M.result):M.result,f=[];M.config.face.enabled?f=y.face:M.config.body.enabled?f=y.body:M.config.object.enabled?f=y.object:H("unknown object type");let c=[];if(_.tracker.checked){let s=f.map(i=>({x:i.box[0]+i.box[2]/2,y:i.box[1]+i.box[3]/2,w:i.box[2],h:i.box[3],name:i.label||(M.config.face.enabled?"face":"body"),confidence:i.score}));D.updateTrackedItemsWithNewFrame(s,N.frames),c=D.getJSONOfTrackedItems(!0)}M.draw.canvas(_.video,_.canvas);for(let s=0;s<f.length;s++){let i=f[s].label||(M.config.face.enabled?"face":"body");_.ctx.strokeRect(f[s].box[0],f[s].box[1],f[s].box[1],f[s].box[2]),_.ctx.fillText(`id: ${f[s].id} ${Math.round(100*f[s].score)}% ${i}`,f[s].box[0]+4,f[s].box[1]+16),c[s]&&_.ctx.fillText(`t: ${c[s].id} ${Math.round(100*c[s].confidence)}% ${c[s].name} ${c[s].isZombie?"zombie":""}`,f[s].box[0]+4,f[s].box[1]+34)}}let l=M.now();N.drawFPS=Math.round(1e3*1e3/(l-A.draw))/1e3,A.draw=l,B(_.video.paused?"paused":`fps: ${N.detectFPS.toFixed(1).padStart(5," ")} detect | ${N.drawFPS.toFixed(1).padStart(5," ")} draw`),setTimeout(Z,30)}async function P(l){let y=URL.createObjectURL(l);_.video.src=y,await _.video.play(),H("loaded video:",l.name,"resolution:",[_.video.videoWidth,_.video.videoHeight],"duration:",_.video.duration),_.canvas.width=_.video.videoWidth,_.canvas.height=_.video.videoHeight,_.ctx.strokeStyle="white",_.ctx.fillStyle="white",_.ctx.font="16px Segoe UI",_.video.playbackRate=.25}function ee(){document.body.addEventListener("dragenter",l=>l.preventDefault()),document.body.addEventListener("dragleave",l=>l.preventDefault()),document.body.addEventListener("dragover",l=>l.preventDefault()),document.body.addEventListener("drop",async l=>{var f,c;l.preventDefault(),l.dataTransfer&&(l.dataTransfer.dropEffect="copy");let y=(c=(f=l.dataTransfer)==null?void 0:f.files)==null?void 0:c[0];y&&await P(y),H(_.video.readyState)}),document.getElementById("inputvideo").onchange=async l=>{var f,c;l.preventDefault();let y=(c=(f=l.target)==null?void 0:f.files)==null?void 0:c[0];y&&await P(y)},_.config.onchange=()=>{F.distanceLimit=document.getElementById("distanceLimit").valueAsNumber,F.iouLimit=document.getElementById("iouLimit").valueAsNumber,F.unMatchedFramesTolerance=document.getElementById("unMatchedFramesTolerance").valueAsNumber,F.unMatchedFramesTolerance=document.getElementById("unMatchedFramesTolerance").valueAsNumber,F.matchingAlgorithm=document.getElementById("matchingAlgorithm-kdTree").checked?"kdTree":"munkres",D.setParams(F),document.getElementById("keepInMemory").checked?D.enableKeepInMemory():D.disableKeepInMemory(),D.reset(),H("tracker config change",JSON.stringify(F)),R.face.enabled=document.getElementById("box-face").checked,R.body.enabled=document.getElementById("box-body").checked,R.object.enabled=document.getElementById("box-object").checked},_.tracker.onchange=l=>{H("tracker",l.target.checked?"enabled":"disabled"),D.setParams(F),D.reset()}}async function te(){H("human version:",M.version,"| tfjs version:",M.tf.version["tfjs-core"]),H("platform:",M.env.platform,"| agent:",M.env.agent),B("loading..."),await M.load(),H("backend:",M.tf.getBackend(),"| available:",M.env.backends),H("models loaded:",M.models.loaded()),B("initializing..."),await M.warmup(),ee(),await V(),Z()}window.onload=te;
//# sourceMappingURL=index.js.map
