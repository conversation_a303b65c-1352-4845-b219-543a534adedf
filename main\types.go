package main

// ResumeInfo 简历信息结构
type ResumeInfo struct {
	Name           string `json:"姓名"`
	Hospital       string `json:"医院"`
	Department     string `json:"科室"`
	Title          string `json:"职称"`
	Description    string `json:"介绍"`
	ProfilePicture string `json:"头像,omitempty"`

	// 新增详细字段，按照需求文档格式范例
	Position       string `json:"职务职称,omitempty"` // 职务职称（可能包含多行）
	AcademicTitle  string `json:"学术任职,omitempty"` // 学术任职（可能包含多行）
	Specialty      string `json:"专业擅长,omitempty"` // 专业擅长
	AcademicOutput string `json:"学术成果,omitempty"` // 学术成果（可能包含多行）
	Other          string `json:"其他,omitempty"`   // 其他无法归类的信息

	// 结构化数据（可选，用于更精确的提取）
	Positions       []string `json:"职务职称列表,omitempty"` // 职务职称列表
	AcademicTitles  []string `json:"学术任职列表,omitempty"` // 学术任职列表
	AcademicOutputs []string `json:"学术成果列表,omitempty"` // 学术成果列表
}

// ProcessingStats 图片处理统计信息
type ProcessingStats struct {
	TotalImages     int           `json:"total_images"`
	PortraitImages  int           `json:"portrait_images"`
	ProcessedImages int           `json:"processed_images"`
	ProcessingTime  int64         `json:"processing_time"`
	BestPortrait    string        `json:"best_portrait,omitempty"`
	Results         []ImageResult `json:"results,omitempty"`
}

// ImageResult 单张图片处理结果
type ImageResult struct {
	OriginalPath      string  `json:"original_path"`
	ProcessedPath     string  `json:"processed_path,omitempty"`
	CutoutPath        string  `json:"cutout_path,omitempty"`
	StandardPhotoPath string  `json:"standard_photo_path,omitempty"`
	IsPortrait        bool    `json:"is_portrait"`
	Confidence        float64 `json:"confidence,omitempty"`
	ProcessingTime    int64   `json:"processing_time"`
	Error             string  `json:"error,omitempty"`

	// 新增字段：图片优化相关信息
	CompressionInfo *ImageCompressionInfo `json:"compression_info,omitempty"`
	QualityWarnings []string              `json:"quality_warnings,omitempty"`

	// 新增字段：增强后的图片路径（用于抠图处理）
	EnhancedPath string `json:"enhanced_path,omitempty"` // 增强后的图片路径
	UseEnhanced  bool   `json:"use_enhanced,omitempty"`  // 是否使用增强图片进行抠图
}

// ImageCompressionInfo 图片压缩信息
type ImageCompressionInfo struct {
	OriginalSizeMB   float64 `json:"original_size_mb"`   // 原始大小(MB)
	CompressedSizeMB float64 `json:"compressed_size_mb"` // 压缩后大小(MB)
	CompressionRatio float64 `json:"compression_ratio"`  // 压缩比例
	WasCompressed    bool    `json:"was_compressed"`     // 是否进行了压缩
	EstimatedDPI     int     `json:"estimated_dpi"`      // 估算的DPI
	ImageDimensions  Size    `json:"image_dimensions"`   // 图片尺寸
}

// FileInfo 文件信息结构
type FileInfo struct {
	Name string `json:"name"`
	Size int64  `json:"size"`
	Type string `json:"type"`
}

// Config 配置结构
type Config struct {
	AIAPIKey                   string                 `json:"ai_api_key"`
	AIModel                    string                 `json:"ai_model"`
	AIEndpoint                 string                 `json:"ai_endpoint"`
	TencentSecretID            string                 `json:"tencent_secret_id"`
	TencentSecretKey           string                 `json:"tencent_secret_key"`
	TencentRegion              string                 `json:"tencent_region"`
	TencentBucket              string                 `json:"tencent_bucket"`
	PortraitDetectionThreshold float64                `json:"portrait_detection_threshold"`
	EnableImageProcessing      bool                   `json:"enable_image_processing"`
	MaxImageSizeMB             int                    `json:"max_image_size_mb"`
	ImageProcessingTimeout     int                    `json:"image_processing_timeout"`
	EnablePhotoEnhancement     bool                   `json:"enable_photo_enhancement"`
	PhotoEnhancement           PhotoEnhancementConfig `json:"photo_enhancement"`
}

// PhotoEnhancementConfig 照片矫正配置
type PhotoEnhancementConfig struct {
	StandardWidth                 int     `json:"standard_width"`
	StandardHeight                int     `json:"standard_height"`
	AutoRotate                    bool    `json:"auto_rotate"`
	SkewThreshold                 float64 `json:"skew_threshold"`
	FaceDetection                 bool    `json:"face_detection"`
	Quality                       int     `json:"quality"`
	Compression                   int     `json:"compression"`
	PPTWidth                      int     `json:"ppt_width"`
	PPTHeight                     int     `json:"ppt_height"`
	MaintainAspect                bool    `json:"maintain_aspect"`
	MinDPI                        int     `json:"min_dpi"`
	EnableSizeCheck               bool    `json:"enable_size_check"`
	EnableDPICheck                bool    `json:"enable_dpi_check"`
	MaxImageSizeForAPI            float64 `json:"max_image_size_for_api"`
	CompressionQuality            int     `json:"compression_quality"`
	QualityPriority               bool    `json:"quality_priority"`
	SmartResizeThresholdWidth     int     `json:"smart_resize_threshold_width"`    // 智能缩放触发阈值
	SmartResizeTargetWidth        int     `json:"smart_resize_target_width"`       // 智能缩放目标宽度
	EnablePortraitStandardization bool    `json:"enable_portrait_standardization"` // 新增：启用智能头像标准化
}

// ImageEnhancementResult 图片增强结果
type ImageEnhancementResult struct {
	OriginalPath   string  `json:"original_path"`
	EnhancedPath   string  `json:"enhanced_path"`
	CutoutPath     string  `json:"cutout_path,omitempty"`
	OriginalSize   Size    `json:"original_size"`
	EnhancedSize   Size    `json:"enhanced_size"`
	RotationAngle  float64 `json:"rotation_angle"`
	SkewCorrection bool    `json:"skew_correction"`
	FaceCentered   bool    `json:"face_centered"`
	QualityScore   float64 `json:"quality_score"`
	ProcessingTime int64   `json:"processing_time"`
}

// Size 尺寸结构
type Size struct {
	Width  int `json:"width"`
	Height int `json:"height"`
}

// FaceInfo 人脸位置信息
type FaceInfo struct {
	X, Y, Width, Height int
	Confidence          float64
}

// PPT生成相关数据结构

// PPTGenerationRequest PPT生成请求结构
type PPTGenerationRequest struct {
	TemplatePath string                 `json:"template_path"` // 模板文件路径
	OutputDir    string                 `json:"output_dir"`    // 输出目录
	Data         map[string]interface{} `json:"data"`          // 替换数据
	Options      PPTGenerationOptions   `json:"options"`       // 生成选项
}

// PPTGenerationOptions PPT生成选项
type PPTGenerationOptions struct {
	ReplaceText  bool `json:"replace_text"`  // 是否替换文本
	ReplaceImage bool `json:"replace_image"` // 是否替换图片
	KeepFormat   bool `json:"keep_format"`   // 是否保持格式
	AutoResize   bool `json:"auto_resize"`   // 是否自动调整大小
}

// PPTGenerationResult PPT生成结果
type PPTGenerationResult struct {
	OutputPath     string `json:"output_path"`     // 输出文件路径
	FileName       string `json:"file_name"`       // 文件名
	ProcessingTime int64  `json:"processing_time"` // 处理时间(毫秒)
	Success        bool   `json:"success"`         // 是否成功
	Message        string `json:"message"`         // 结果消息
	Error          string `json:"error,omitempty"` // 错误信息
}

// TemplateMapping 模板映射结构
type TemplateMapping struct {
	TextPlaceholders  map[string]string `json:"text_placeholders"`  // 文本占位符映射
	ImagePlaceholders map[string]string `json:"image_placeholders"` // 图片占位符映射
	Styles            map[string]Style  `json:"styles"`             // 样式配置
}

// Style 样式配置
type Style struct {
	FontSize   int    `json:"font_size"`   // 字体大小
	FontFamily string `json:"font_family"` // 字体族
	Color      string `json:"color"`       // 颜色
	Bold       bool   `json:"bold"`        // 是否加粗
	Italic     bool   `json:"italic"`      // 是否斜体
}

// SlideContent 幻灯片内容结构
type SlideContent struct {
	SlideIndex   int                  `json:"slide_index"`  // 幻灯片索引
	Texts        []TextElement        `json:"texts"`        // 文本元素
	Images       []ImageElement       `json:"images"`       // 图片元素
	Shapes       []ShapeElement       `json:"shapes"`       // 形状元素
	Placeholders []PlaceholderElement `json:"placeholders"` // 占位符元素
}

// TextElement 文本元素
type TextElement struct {
	ID       string `json:"id"`        // 元素ID
	Content  string `json:"content"`   // 文本内容
	X        int    `json:"x"`         // X坐标
	Y        int    `json:"y"`         // Y坐标
	Width    int    `json:"width"`     // 宽度
	Height   int    `json:"height"`    // 高度
	FontSize int    `json:"font_size"` // 字体大小
}

// ImageElement 图片元素
type ImageElement struct {
	ID         string `json:"id"`          // 元素ID
	Path       string `json:"path"`        // 图片路径
	X          int    `json:"x"`           // X坐标
	Y          int    `json:"y"`           // Y坐标
	Width      int    `json:"width"`       // 宽度
	Height     int    `json:"height"`      // 高度
	KeepAspect bool   `json:"keep_aspect"` // 保持宽高比
}

// ShapeElement 形状元素
type ShapeElement struct {
	ID     string `json:"id"`     // 元素ID
	Type   string `json:"type"`   // 形状类型
	X      int    `json:"x"`      // X坐标
	Y      int    `json:"y"`      // Y坐标
	Width  int    `json:"width"`  // 宽度
	Height int    `json:"height"` // 高度
}

// PlaceholderElement 占位符元素
type PlaceholderElement struct {
	ID          string `json:"id"`          // 元素ID
	Type        string `json:"type"`        // 占位符类型
	Placeholder string `json:"placeholder"` // 占位符文本
	X           int    `json:"x"`           // X坐标
	Y           int    `json:"y"`           // Y坐标
	Width       int    `json:"width"`       // 宽度
	Height      int    `json:"height"`      // 高度
}
