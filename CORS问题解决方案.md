# 🔧 CORS问题解决方案

## 问题描述

当直接在浏览器中打开HTML文件时（file://协议），会遇到CORS（跨域资源共享）错误，导致无法加载Human.js的模型文件。

## 🚀 推荐解决方案

### 方案1：Python HTTP服务器（最简单）

**前提条件**: 已安装Python

1. **双击运行** `启动服务器.bat`
2. **等待启动** 看到"服务器运行中..."提示
3. **打开浏览器** 访问 `http://localhost:8000/start.html`

```bash
# 或者手动在命令行中运行：
cd "D:\Dev\头像抠图测试示例"
python -m http.server 8000
```

### 方案2：Node.js HTTP服务器

**前提条件**: 已安装Node.js

#### 选项A：使用http-server包
1. **双击运行** `启动服务器-Node.bat`
2. **打开浏览器** 访问 `http://localhost:8000/start.html`

#### 选项B：使用自定义服务器
1. **双击运行** `server.js` 或在命令行中运行：
```bash
cd "D:\Dev\头像抠图测试示例"
node server.js
```
2. **打开浏览器** 访问 `http://localhost:8000/start.html`

### 方案3：Chrome禁用安全策略（临时方案）

⚠️ **注意**: 此方法会临时禁用浏览器安全策略，仅用于测试

1. **关闭所有Chrome窗口**
2. **双击运行** `启动Chrome-禁用安全.bat`
3. **在打开的Chrome中** 直接使用系统

## 📋 各方案对比

| 方案 | 优点 | 缺点 | 推荐度 |
|------|------|------|--------|
| Python服务器 | 简单、安全、无需额外安装 | 需要Python环境 | ⭐⭐⭐⭐⭐ |
| Node.js服务器 | 功能完整、性能好 | 需要Node.js环境 | ⭐⭐⭐⭐ |
| Chrome禁用安全 | 无需服务器 | 安全风险、仅临时 | ⭐⭐ |

## 🛠️ 详细步骤

### 使用Python服务器（推荐）

1. **检查Python安装**
   ```bash
   python --version
   ```
   如果没有安装，请访问 https://www.python.org/downloads/

2. **启动服务器**
   - 双击 `启动服务器.bat`
   - 或在项目目录打开命令行运行：`python -m http.server 8000`

3. **访问系统**
   - 打开浏览器
   - 访问 `http://localhost:8000/start.html`

4. **停止服务器**
   - 在命令行窗口按 `Ctrl+C`

### 使用Node.js服务器

1. **检查Node.js安装**
   ```bash
   node --version
   ```
   如果没有安装，请访问 https://nodejs.org/

2. **安装http-server（可选）**
   ```bash
   npm install -g http-server
   ```

3. **启动服务器**
   - 双击 `启动服务器-Node.bat`
   - 或运行自定义服务器：`node server.js`

4. **访问系统**
   - 打开浏览器
   - 访问 `http://localhost:8000/start.html`

## 🔍 故障排除

### 问题1：端口被占用
**错误信息**: `EADDRINUSE: address already in use`

**解决方案**:
- 更换端口号（如8001、8080等）
- 或关闭占用端口的程序

### 问题2：Python/Node.js未安装
**解决方案**:
- Python: https://www.python.org/downloads/
- Node.js: https://nodejs.org/

### 问题3：防火墙阻止
**解决方案**:
- 允许Python/Node.js通过防火墙
- 或使用Chrome禁用安全策略方案

### 问题4：模型文件缺失
**检查路径**:
```
learnface/hr_attendance_face_recognition_pro/static/src/js/models/
├── blazeface.json
├── facemesh.json
├── antispoof.json
└── faceres.json
```

## 🎯 快速开始

1. **最快方式**: 双击 `启动服务器.bat`
2. **等待提示**: "服务器运行中..."
3. **打开浏览器**: 访问 `http://localhost:8000/start.html`
4. **开始使用**: 选择功能开始处理头像

## 💡 小贴士

- 保持服务器窗口打开，关闭会停止服务
- 建议使用Chrome或Edge浏览器以获得最佳体验
- 首次使用建议先运行系统测试确保环境正常
- 处理大量图片时建议关闭其他占用内存的程序

---

**🚀 立即开始**: 选择一个方案启动服务器，然后访问 `http://localhost:8000/start.html`
