#!/bin/bash

# GejieSoft PDF Tools - 测试运行脚本

set -e

echo "======================================="
echo "  GejieSoft PDF Tools - 测试套件"
echo "======================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT=$(cd "$(dirname "$0")/.." && pwd)
cd "$PROJECT_ROOT"

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：运行单元测试
run_unit_tests() {
    print_message $BLUE "📋 运行单元测试..."
    
    cd main
    
    # 运行所有测试
    if go test -v ./...; then
        print_message $GREEN "✅ 单元测试通过"
    else
        print_message $RED "❌ 单元测试失败"
        exit 1
    fi
    
    cd ..
}

# 函数：生成测试覆盖率报告
generate_coverage() {
    print_message $BLUE "📊 生成测试覆盖率报告..."
    
    cd main
    
    # 生成覆盖率报告
    go test -coverprofile=coverage.out ./...
    
    # 显示覆盖率统计
    go tool cover -func=coverage.out
    
    # 生成HTML报告
    go tool cover -html=coverage.out -o ../coverage.html
    
    print_message $GREEN "✅ 覆盖率报告已生成: coverage.html"
    
    cd ..
}

# 函数：运行基准测试
run_benchmarks() {
    print_message $BLUE "⚡ 运行基准测试..."
    
    cd main
    
    # 运行基准测试
    go test -bench=. -benchmem ./...
    
    print_message $GREEN "✅ 基准测试完成"
    
    cd ..
}

# 函数：运行代码检查
run_code_checks() {
    print_message $BLUE "🔍 运行代码检查..."
    
    # 检查Go代码格式
    if ! gofmt -l main/*.go | grep -q .; then
        print_message $GREEN "✅ Go代码格式正确"
    else
        print_message $RED "❌ Go代码格式有问题，需要运行: gofmt -w main/*.go"
        exit 1
    fi
    
    # 运行go vet
    cd main
    if go vet ./...; then
        print_message $GREEN "✅ go vet 检查通过"
    else
        print_message $RED "❌ go vet 检查失败"
        exit 1
    fi
    cd ..
    
    # 检查是否有未使用的依赖
    cd main
    if go mod tidy -v; then
        print_message $GREEN "✅ 依赖检查通过"
    else
        print_message $RED "❌ 依赖检查失败"
        exit 1
    fi
    cd ..
}

# 函数：运行Python测试
run_python_tests() {
    print_message $BLUE "🐍 运行Python测试..."
    
    cd main/ppt_generator
    
    # 检查Python文件语法
    if python3 -m py_compile ppt_generator.py; then
        print_message $GREEN "✅ Python语法检查通过"
    else
        print_message $RED "❌ Python语法检查失败"
        exit 1
    fi
    
    # 运行Python测试（如果存在）
    if [ -f "test_ppt_generator.py" ]; then
        if python3 -m pytest test_ppt_generator.py -v; then
            print_message $GREEN "✅ Python测试通过"
        else
            print_message $RED "❌ Python测试失败"
            exit 1
        fi
    else
        print_message $YELLOW "⚠️  未找到Python测试文件"
    fi
    
    cd ../..
}

# 函数：运行集成测试
run_integration_tests() {
    print_message $BLUE "🔗 运行集成测试..."
    
    # 检查环境变量
    if [ -z "$AIBOT_API_KEY" ] || [ -z "$TENCENT_SECRET_ID" ] || [ -z "$TENCENT_SECRET_KEY" ]; then
        print_message $YELLOW "⚠️  跳过集成测试：缺少API密钥"
        return
    fi
    
    # 构建应用
    cd main
    if go build -o ../test_app; then
        print_message $GREEN "✅ 应用构建成功"
    else
        print_message $RED "❌ 应用构建失败"
        exit 1
    fi
    cd ..
    
    # 启动应用（后台运行）
    ./test_app &
    APP_PID=$!
    sleep 5
    
    # 测试健康检查端点
    if curl -s http://localhost:8088/api/health > /dev/null; then
        print_message $GREEN "✅ 集成测试通过"
    else
        print_message $RED "❌ 集成测试失败"
        kill $APP_PID
        exit 1
    fi
    
    # 清理
    kill $APP_PID
    rm -f test_app
}

# 函数：清理测试文件
cleanup() {
    print_message $BLUE "🧹 清理测试文件..."
    
    cd main
    rm -f coverage.out
    cd ..
    
    rm -f coverage.html
    rm -f test_app
    
    print_message $GREEN "✅ 清理完成"
}

# 主函数
main() {
    case "${1:-all}" in
        "unit")
            run_unit_tests
            ;;
        "coverage")
            run_unit_tests
            generate_coverage
            ;;
        "bench")
            run_benchmarks
            ;;
        "check")
            run_code_checks
            ;;
        "python")
            run_python_tests
            ;;
        "integration")
            run_integration_tests
            ;;
        "cleanup")
            cleanup
            ;;
        "all")
            run_code_checks
            run_unit_tests
            generate_coverage
            run_python_tests
            run_integration_tests
            cleanup
            ;;
        *)
            print_message $YELLOW "用法: $0 [unit|coverage|bench|check|python|integration|cleanup|all]"
            echo
            echo "选项说明:"
            echo "  unit        - 运行单元测试"
            echo "  coverage    - 生成测试覆盖率报告"
            echo "  bench       - 运行基准测试"
            echo "  check       - 运行代码检查"
            echo "  python      - 运行Python测试"
            echo "  integration - 运行集成测试"
            echo "  cleanup     - 清理测试文件"
            echo "  all         - 运行所有测试（默认）"
            exit 1
            ;;
    esac
    
    print_message $GREEN "🎉 测试完成！"
}

# 错误处理
trap 'print_message $RED "❌ 测试过程中发生错误"; cleanup; exit 1' ERR

# 运行主函数
main "$@" 