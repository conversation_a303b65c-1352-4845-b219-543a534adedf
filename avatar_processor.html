<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>头像抠图处理系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .main-content {
            padding: 40px;
        }
        
        .upload-section {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 60px 20px;
            background: #f8f9ff;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .upload-area:hover {
            border-color: #764ba2;
            background: #f0f2ff;
            transform: translateY(-2px);
        }
        
        .upload-area.dragover {
            border-color: #764ba2;
            background: #e8ebff;
            transform: scale(1.02);
        }
        
        .upload-icon {
            font-size: 4em;
            color: #667eea;
            margin-bottom: 20px;
        }
        
        .upload-text {
            font-size: 1.3em;
            color: #333;
            margin-bottom: 10px;
        }
        
        .upload-hint {
            color: #666;
            font-size: 0.9em;
        }
        
        #fileInput {
            display: none;
        }
        
        .processing-section {
            display: none;
            text-align: center;
            margin: 40px 0;
        }
        
        .progress-container {
            background: #f0f0f0;
            border-radius: 25px;
            padding: 5px;
            margin: 20px 0;
        }
        
        .progress-bar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 30px;
            border-radius: 20px;
            width: 0%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .results-section {
            display: none;
            margin-top: 40px;
        }
        
        .image-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        
        .image-container {
            text-align: center;
            background: #f8f9ff;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .image-container h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.2em;
        }
        
        .image-container img {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }
        
        .image-container canvas {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><rect width="10" height="10" fill="%23f0f0f0"/><rect x="10" y="10" width="10" height="10" fill="%23f0f0f0"/></svg>');
        }
        
        .download-section {
            text-align: center;
            margin-top: 30px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .status-message {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
            font-weight: bold;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .face-info {
            background: #e8f4fd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .face-info h4 {
            margin: 0 0 10px 0;
            color: #0066cc;
        }
        
        .face-info ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .face-info li {
            margin: 5px 0;
            color: #333;
        }
        
        @media (max-width: 768px) {
            .image-comparison {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .main-content {
                padding: 20px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎭 头像抠图处理系统</h1>
            <p>基于Human.js的智能人脸识别与头像裁切技术</p>
        </div>
        
        <div class="main-content">
            <!-- 上传区域 -->
            <div class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📸</div>
                    <div class="upload-text">点击或拖拽上传照片</div>
                    <div class="upload-hint">支持 JPG、PNG、JPEG 格式，建议分辨率不低于 300x300</div>
                </div>
                <input type="file" id="fileInput" accept="image/*" multiple>
            </div>
            
            <!-- 处理进度 -->
            <div class="processing-section" id="processingSection">
                <h3>🔄 正在处理中...</h3>
                <div class="progress-container">
                    <div class="progress-bar" id="progressBar">0%</div>
                </div>
                <p id="processingStatus">初始化Human.js模型...</p>
            </div>
            
            <!-- 状态消息 -->
            <div id="statusMessage"></div>
            
            <!-- 结果展示 -->
            <div class="results-section" id="resultsSection">
                <h2>📊 处理结果</h2>
                
                <!-- 人脸检测信息 -->
                <div class="face-info" id="faceInfo" style="display: none;">
                    <h4>🎯 人脸检测信息</h4>
                    <ul id="faceDetails"></ul>
                </div>
                
                <!-- 图像对比 -->
                <div class="image-comparison" id="imageComparison">
                    <div class="image-container">
                        <h3>📷 原始图像</h3>
                        <img id="originalImage" alt="原始图像">
                    </div>
                    <div class="image-container">
                        <h3>🎯 人脸检测</h3>
                        <canvas id="detectionCanvas"></canvas>
                    </div>
                    <div class="image-container">
                        <h3>✨ 标准头像 (已移除背景)</h3>
                        <canvas id="avatarCanvas"></canvas>
                        <div style="font-size: 12px; color: #666; margin-top: 5px;">
                            🎭 使用 AI 模型自动移除背景
                        </div>
                    </div>
                </div>
                
                <!-- 下载区域 -->
                <div class="download-section">
                    <button class="btn" id="downloadBtn" onclick="downloadAvatar()">
                        💾 下载头像
                    </button>
                    <button class="btn" id="processMoreBtn" onclick="resetProcessor()">
                        🔄 处理更多照片
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 配置文件 -->
    <script src="config.js"></script>

    <!-- Human.js 库 -->
    <script src="learnface/hr_attendance_face_recognition_pro/static/src/js/lib/human.js"></script>
    
    <script>
        // 全局变量
        let human = null;
        let currentImageData = null;
        let processedAvatarData = null;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeHuman();
            setupEventListeners();
        });
        
        // 设置事件监听器
        function setupEventListeners() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            
            // 点击上传
            uploadArea.addEventListener('click', () => fileInput.click());
            
            // 文件选择
            fileInput.addEventListener('change', handleFileSelect);
            
            // 拖拽上传
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);
        }
        
        // 初始化Human.js
        async function initializeHuman() {
            try {
                updateProgress(10, '正在加载Human.js模型...');

                // 使用配置文件中的设置
                const config = getConfig('human');

                human = new Human.Human(config);
                await human.load();

                updateProgress(100, 'Human.js模型加载完成！');
                hideProcessing();
                showStatus('Human.js模型已成功加载，可以开始处理照片了！', 'success');

            } catch (error) {
                console.error('Human.js初始化失败:', error);
                showStatus('模型加载失败: ' + error.message, 'error');
                hideProcessing();
            }
        }

        // 文件处理相关函数
        function handleFileSelect(event) {
            const files = event.target.files;
            if (files.length > 0) {
                processFile(files[0]);
            }
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.currentTarget.classList.remove('dragover');
        }

        function handleDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');

            const files = event.dataTransfer.files;
            if (files.length > 0) {
                processFile(files[0]);
            }
        }

        // 处理上传的文件
        async function processFile(file) {
            if (!human) {
                showStatus('Human.js模型尚未加载完成，请稍候...', 'warning');
                return;
            }

            // 验证文件类型
            if (!file.type.startsWith('image/')) {
                showStatus('请上传有效的图像文件！', 'error');
                return;
            }

            try {
                showProcessing();
                updateProgress(0, '正在读取图像...');

                // 读取图像
                const imageData = await readImageFile(file);
                currentImageData = imageData;

                updateProgress(20, '正在进行人脸检测...');

                // 人脸检测
                const result = await human.detect(imageData.canvas);

                if (!result.face || result.face.length === 0) {
                    throw new Error('未检测到人脸，请确保照片中包含清晰的人脸');
                }

                updateProgress(40, '正在分析人脸特征...');

                // 显示检测结果
                displayDetectionResult(imageData, result);

                updateProgress(60, '正在生成标准头像...');

                // 生成标准头像
                const avatar = await generateStandardAvatar(imageData, result.face[0]);
                processedAvatarData = avatar;

                updateProgress(80, '正在应用 AI 背景移除 (u2net_human_seg)...');

                // 背景移除
                const finalAvatar = await removeBackground(avatar, result.face[0]);

                updateProgress(100, '处理完成！背景已成功移除');

                // 显示结果
                displayResults(imageData, result, finalAvatar);
                hideProcessing();
                showStatus('🎉 头像处理完成！背景已自动移除，生成透明背景头像', 'success');

            } catch (error) {
                console.error('处理失败:', error);
                showStatus('处理失败: ' + error.message, 'error');
                hideProcessing();
            }
        }

        // 读取图像文件
        function readImageFile(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = new Image();
                    img.onload = function() {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        // 设置画布尺寸
                        canvas.width = img.width;
                        canvas.height = img.height;

                        // 绘制图像
                        ctx.drawImage(img, 0, 0);

                        resolve({
                            canvas: canvas,
                            image: img,
                            width: img.width,
                            height: img.height
                        });
                    };
                    img.onerror = reject;
                    img.src = e.target.result;
                };
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }

        // 显示检测结果 - 增强调试版本
        function displayDetectionResult(imageData, result) {
            const face = result.face[0];

            // 计算面部对称轴用于调试
            let faceSymmetryX = null;
            let debugInfo = '';

            if (face.mesh && face.mesh.length >= 468) {
                // 使用眼睛关键点计算对称轴
                const leftEyeInner = face.mesh[133];   // 左眼内角
                const leftEyeOuter = face.mesh[33];    // 左眼外角
                const rightEyeInner = face.mesh[362];  // 右眼内角
                const rightEyeOuter = face.mesh[263];  // 右眼外角

                if (leftEyeInner && leftEyeOuter && rightEyeInner && rightEyeOuter) {
                    // 计算两眼中心点
                    const leftEyeCenterX = (leftEyeInner[0] + leftEyeOuter[0]) / 2;
                    const rightEyeCenterX = (rightEyeInner[0] + rightEyeOuter[0]) / 2;

                    // 两眼中心的中点就是面部对称轴
                    faceSymmetryX = (leftEyeCenterX + rightEyeCenterX) / 2;
                }

                // 生成调试信息
                debugInfo = `
                    <li>左眼内角: ${leftEyeInner ? `(${leftEyeInner[0].toFixed(1)}, ${leftEyeInner[1].toFixed(1)})` : '未检测'}</li>
                    <li>左眼外角: ${leftEyeOuter ? `(${leftEyeOuter[0].toFixed(1)}, ${leftEyeOuter[1].toFixed(1)})` : '未检测'}</li>
                    <li>右眼内角: ${rightEyeInner ? `(${rightEyeInner[0].toFixed(1)}, ${rightEyeInner[1].toFixed(1)})` : '未检测'}</li>
                    <li>右眼外角: ${rightEyeOuter ? `(${rightEyeOuter[0].toFixed(1)}, ${rightEyeOuter[1].toFixed(1)})` : '未检测'}</li>
                    <li>左眼中心X: ${leftEyeInner && leftEyeOuter ? ((leftEyeInner[0] + leftEyeOuter[0]) / 2).toFixed(1) : '未计算'}</li>
                    <li>右眼中心X: ${rightEyeInner && rightEyeOuter ? ((rightEyeInner[0] + rightEyeOuter[0]) / 2).toFixed(1) : '未计算'}</li>
                    <li>计算对称轴X: ${faceSymmetryX ? faceSymmetryX.toFixed(1) : '未计算'}</li>
                    <li>人脸框中心X: ${(face.box[0] + face.box[2] / 2).toFixed(1)}</li>
                `;
            }

            // 显示人脸信息
            const faceInfo = document.getElementById('faceInfo');
            const faceDetails = document.getElementById('faceDetails');

            faceDetails.innerHTML = `
                <li>检测置信度: ${(face.boxScore * 100).toFixed(1)}%</li>
                <li>人脸质量评分: ${(face.faceScore * 100).toFixed(1)}%</li>
                <li>关键点数量: ${face.mesh ? face.mesh.length : 0}个</li>
                <li>人脸角度: ${face.angle ? face.angle.toFixed(1) + '°' : '正面'}</li>
                <li>真实性检测: ${face.real ? (face.real * 100).toFixed(1) + '%' : '未检测'}</li>
                ${debugInfo}
            `;

            faceInfo.style.display = 'block';

            // 绘制检测结果
            const detectionCanvas = document.getElementById('detectionCanvas');
            const ctx = detectionCanvas.getContext('2d');

            detectionCanvas.width = imageData.width;
            detectionCanvas.height = imageData.height;

            // 绘制原图
            ctx.drawImage(imageData.canvas, 0, 0);

            // 绘制人脸框
            if (face.box) {
                ctx.strokeStyle = '#00ff00';
                ctx.lineWidth = 3;
                ctx.strokeRect(face.box[0], face.box[1], face.box[2], face.box[3]);

                // 绘制人脸框中心线
                const boxCenterX = face.box[0] + face.box[2] / 2;
                ctx.strokeStyle = '#00ff00';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(boxCenterX, face.box[1]);
                ctx.lineTo(boxCenterX, face.box[1] + face.box[3]);
                ctx.stroke();
            }

            // 绘制关键点
            if (face.mesh) {
                ctx.fillStyle = '#ff0000';
                face.mesh.forEach(point => {
                    ctx.beginPath();
                    ctx.arc(point[0], point[1], 1, 0, 2 * Math.PI);
                    ctx.fill();
                });

                // 高亮显示眼睛关键点
                const eyeKeyPoints = {
                    leftEyeInner: face.mesh[133],   // 左眼内角
                    leftEyeOuter: face.mesh[33],    // 左眼外角
                    rightEyeInner: face.mesh[362],  // 右眼内角
                    rightEyeOuter: face.mesh[263]   // 右眼外角
                };

                ctx.fillStyle = '#0000ff';
                Object.values(eyeKeyPoints).forEach(point => {
                    if (point) {
                        ctx.beginPath();
                        ctx.arc(point[0], point[1], 4, 0, 2 * Math.PI);
                        ctx.fill();
                    }
                });

                // 绘制计算出的对称轴
                if (faceSymmetryX) {
                    ctx.strokeStyle = '#ff0000';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(faceSymmetryX, 0);
                    ctx.lineTo(faceSymmetryX, imageData.height);
                    ctx.stroke();
                }
            }
        }

        // 生成标准头像 - 基于面部对称轴的精确居中算法
        async function generateStandardAvatar(imageData, face) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            // 使用配置文件中的头像尺寸
            const avatarConfig = getConfig('avatar');
            const avatarSize = avatarConfig.outputSize;
            canvas.width = avatarSize;
            canvas.height = avatarSize;

            // 基于面部特征的标准化裁切算法
            let faceSymmetryX, eyeCenterY, mouthCenterY, eyeDistance, faceHeight;
            let cropSize, cropX, cropY;

            if (face.mesh && face.mesh.length >= 468) {
                // 获取关键面部特征点
                const leftEyeInner = face.mesh[133];   // 左眼内角
                const leftEyeOuter = face.mesh[33];    // 左眼外角
                const rightEyeInner = face.mesh[362];  // 右眼内角
                const rightEyeOuter = face.mesh[263];  // 右眼外角

                // 鼻子和嘴巴关键点（常用索引）
                const noseTip = face.mesh[1] || face.mesh[2];           // 鼻尖
                const mouthLeft = face.mesh[61];                        // 左嘴角
                const mouthRight = face.mesh[291];                      // 右嘴角
                const upperLip = face.mesh[13];                         // 上唇中心
                const lowerLip = face.mesh[14];                         // 下唇中心

                if (leftEyeInner && leftEyeOuter && rightEyeInner && rightEyeOuter) {
                    // 计算眼睛中心点
                    const leftEyeCenterX = (leftEyeInner[0] + leftEyeOuter[0]) / 2;
                    const leftEyeCenterY = (leftEyeInner[1] + leftEyeOuter[1]) / 2;
                    const rightEyeCenterX = (rightEyeInner[0] + rightEyeOuter[0]) / 2;
                    const rightEyeCenterY = (rightEyeInner[1] + rightEyeOuter[1]) / 2;

                    // 面部对称轴
                    faceSymmetryX = (leftEyeCenterX + rightEyeCenterX) / 2;
                    eyeCenterY = (leftEyeCenterY + rightEyeCenterY) / 2;

                    // 计算两眼距离
                    eyeDistance = Math.sqrt(
                        Math.pow(rightEyeCenterX - leftEyeCenterX, 2) +
                        Math.pow(rightEyeCenterY - leftEyeCenterY, 2)
                    );

                    // 计算嘴巴中心
                    if (mouthLeft && mouthRight) {
                        mouthCenterY = (mouthLeft[1] + mouthRight[1]) / 2;
                    } else if (upperLip && lowerLip) {
                        mouthCenterY = (upperLip[1] + lowerLip[1]) / 2;
                    } else {
                        mouthCenterY = eyeCenterY + eyeDistance * 1.2; // 估算
                    }

                    // 计算面部高度（眼睛到嘴巴的距离）
                    faceHeight = Math.abs(mouthCenterY - eyeCenterY);

                    console.log('=== 面部特征分析 ===');
                    console.log('两眼距离:', eyeDistance.toFixed(1));
                    console.log('面部高度(眼到嘴):', faceHeight.toFixed(1));
                    console.log('眼睛中心Y:', eyeCenterY.toFixed(1));
                    console.log('嘴巴中心Y:', mouthCenterY.toFixed(1));
                } else {
                    // 备用方案：使用人脸框
                    faceSymmetryX = face.box[0] + face.box[2] / 2;
                    eyeCenterY = face.box[1] + face.box[3] * 0.4;
                    mouthCenterY = face.box[1] + face.box[3] * 0.7;
                    eyeDistance = face.box[2] * 0.3;
                    faceHeight = face.box[3] * 0.3;
                }
            } else {
                // 备用方案：使用人脸框
                faceSymmetryX = face.box[0] + face.box[2] / 2;
                eyeCenterY = face.box[1] + face.box[3] * 0.4;
                mouthCenterY = face.box[1] + face.box[3] * 0.7;
                eyeDistance = face.box[2] * 0.3;
                faceHeight = face.box[3] * 0.3;
            }

            // 标准化裁切算法：基于面部特征确定裁切大小和位置

            // 定义标准比例（针对扩大30%的裁切范围重新优化）
            const standardRatios = {
                eyeDistanceRatio: 0.19,    // 两眼距离占头像宽度的19%（因为裁切范围扩大30%）
                eyePositionY: 0.40,        // 眼睛位置在头像高度的40%处
                mouthPositionY: 0.65,      // 嘴巴位置在头像高度的65%处
                faceHeightRatio: 0.25      // 眼到嘴距离占头像高度的25%
            };

            // 方法1：基于两眼距离确定裁切大小
            const cropSizeByEyeDistance = eyeDistance / standardRatios.eyeDistanceRatio;

            // 方法2：基于面部高度确定裁切大小
            const cropSizeByFaceHeight = faceHeight / standardRatios.faceHeightRatio;

            // 取两种方法的平均值，确保面部特征都能合适地放置
            cropSize = (cropSizeByEyeDistance + cropSizeByFaceHeight) / 2;

            // 智能头顶空间检测
            const faceBoxTop = face.box[1];
            const eyeToFaceTop = eyeCenterY - faceBoxTop;
            const estimatedHeadTopSpace = eyeToFaceTop * 1.3; // 估算需要的头顶空间

            // 基于头顶空间调整裁切大小
            const minCropSizeForHead = estimatedHeadTopSpace / (1 - standardRatios.eyePositionY);

            // 确保裁切大小合理（不要太大或太小）
            const minCropSize = Math.max(eyeDistance * 3, faceHeight * 2.5, minCropSizeForHead);
            const maxCropSize = Math.min(imageData.width, imageData.height) * 0.9;
            let baseCropSize = Math.max(minCropSize, Math.min(cropSize, maxCropSize));

            // 扩大裁切范围：上下左右各扩15%，总体放大30%
            const expansionFactor = 1.3; // 扩大30%（上下左右各15%）
            cropSize = baseCropSize * expansionFactor;

            // 重新检查最大尺寸限制
            const finalMaxCropSize = Math.min(imageData.width, imageData.height) * 0.95;
            cropSize = Math.min(cropSize, finalMaxCropSize);

            console.log('=== 头顶空间分析 ===');
            console.log('眼睛到人脸框顶部距离:', eyeToFaceTop.toFixed(1));
            console.log('估算头顶空间需求:', estimatedHeadTopSpace.toFixed(1));
            console.log('基于头顶的最小裁切大小:', minCropSizeForHead.toFixed(1));

            console.log('=== 裁切范围扩大分析 ===');
            console.log('基础裁切大小:', baseCropSize.toFixed(1));
            console.log('扩大系数:', expansionFactor, '(上下左右各扩15%)');
            console.log('扩大后裁切大小:', cropSize.toFixed(1));
            console.log('实际扩大比例:', ((cropSize / baseCropSize - 1) * 100).toFixed(1) + '%');

            // 基于标准位置计算裁切坐标
            cropX = faceSymmetryX - cropSize / 2;  // 水平居中

            // 垂直位置：让眼睛位于标准位置
            const targetEyeY = cropSize * standardRatios.eyePositionY;
            cropY = eyeCenterY - targetEyeY;

            console.log('=== 标准化裁切计算 ===');
            console.log('基于眼距的裁切大小:', cropSizeByEyeDistance.toFixed(1));
            console.log('基于面高的裁切大小:', cropSizeByFaceHeight.toFixed(1));
            console.log('最终裁切大小:', cropSize.toFixed(1));
            console.log('眼睛在头像中的目标位置Y:', targetEyeY.toFixed(1));
            console.log('计算的裁切位置: X=' + cropX.toFixed(1) + ', Y=' + cropY.toFixed(1));

            // 验证最终效果
            const finalEyeYInAvatar = (eyeCenterY - cropY) / cropSize;
            const finalMouthYInAvatar = (mouthCenterY - cropY) / cropSize;

            console.log('=== 最终位置验证 ===');
            console.log('眼睛在头像中的实际位置Y比例:', finalEyeYInAvatar.toFixed(3), '(目标:', standardRatios.eyePositionY, ')');
            console.log('嘴巴在头像中的实际位置Y比例:', finalMouthYInAvatar.toFixed(3), '(目标:', standardRatios.mouthPositionY, ')');
            console.log('图像尺寸:', imageData.width, 'x', imageData.height);

            // 创建扩展canvas以支持超出边界的裁切（允许空白，确保居中）
            const extendedSize = Math.max(imageData.width, imageData.height) + cropSize;
            const extendedCanvas = document.createElement('canvas');
            const extendedCtx = extendedCanvas.getContext('2d');
            extendedCanvas.width = extendedSize;
            extendedCanvas.height = extendedSize;

            // 将原图绘制到扩展canvas的中心
            const offsetX = (extendedSize - imageData.width) / 2;
            const offsetY = (extendedSize - imageData.height) / 2;
            extendedCtx.drawImage(imageData.canvas, offsetX, offsetY);

            // 调整裁切坐标到扩展canvas坐标系
            const adjustedCropX = cropX + offsetX;
            const adjustedCropY = cropY + offsetY;

            console.log('扩展canvas尺寸:', extendedSize);
            console.log('原图偏移:', offsetX, offsetY);
            console.log('调整后裁切坐标:', adjustedCropX, adjustedCropY);
            console.log('对称轴在最终头像中的位置:', faceSymmetryX - cropX, '应该等于cropSize/2:', cropSize/2);

            console.log('最终裁切参数:');
            console.log('源区域: x=' + adjustedCropX + ', y=' + adjustedCropY + ', size=' + cropSize);
            console.log('目标区域: x=0, y=0, size=' + avatarSize);
            console.log('=== 调试信息结束 ===');

            // 从扩展canvas裁切图像（确保对称轴居中）
            ctx.drawImage(
                extendedCanvas,
                adjustedCropX, adjustedCropY, cropSize, cropSize,
                0, 0, avatarSize, avatarSize
            );

            return canvas;
        }

        // 背景移除 - 使用 rembg 和 u2net_human_seg 模型
        async function removeBackground(avatarCanvas, face) {
            try {
                console.log('🎭 开始背景移除处理...');
                console.log('📊 使用模型: u2net_human_seg');
                console.log('📐 输入尺寸:', avatarCanvas.width + 'x' + avatarCanvas.height);

                // 将canvas转换为base64数据
                const imageData = avatarCanvas.toDataURL('image/png');
                console.log('✅ 图片数据准备完成');

                // 调用背景移除API
                const response = await fetch('/api/remove-background', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        image_data: imageData,
                        model: 'u2net_human_seg'
                    })
                });

                const result = await response.json();

                if (!result.success) {
                    console.error('❌ 背景移除失败:', result.message);
                    console.log('🔄 使用原始头像作为备选方案');
                    // 如果背景移除失败，返回原始头像
                    return avatarCanvas;
                }

                console.log('✅ 背景移除成功！');
                console.log('📦 返回数据大小:', Math.round(result.data.image_data.length / 1024) + 'KB');

                // 创建新的canvas来显示处理后的图片
                const processedCanvas = document.createElement('canvas');
                const ctx = processedCanvas.getContext('2d');

                // 创建新的图片对象
                const img = new Image();

                return new Promise((resolve) => {
                    img.onload = function() {
                        processedCanvas.width = avatarCanvas.width;
                        processedCanvas.height = avatarCanvas.height;

                        // 绘制处理后的图片
                        ctx.drawImage(img, 0, 0, processedCanvas.width, processedCanvas.height);

                        console.log('背景移除完成');
                        resolve(processedCanvas);
                    };

                    img.onerror = function() {
                        console.error('处理后的图片加载失败，返回原始头像');
                        resolve(avatarCanvas);
                    };

                    // 设置图片源为处理后的base64数据
                    img.src = result.data.image_data;
                });

            } catch (error) {
                console.error('❌ 背景移除过程中发生错误:', error);
                console.log('🔄 使用原始头像作为备选方案');
                // 如果出现错误，返回原始头像
                return avatarCanvas;
            }
        }

        // 显示最终结果
        function displayResults(imageData, result, finalAvatar) {
            // 显示原始图像
            const originalImage = document.getElementById('originalImage');
            originalImage.src = imageData.canvas.toDataURL();

            // 显示最终头像
            const avatarCanvas = document.getElementById('avatarCanvas');
            const ctx = avatarCanvas.getContext('2d');
            avatarCanvas.width = finalAvatar.width;
            avatarCanvas.height = finalAvatar.height;
            ctx.drawImage(finalAvatar, 0, 0);

            // 显示结果区域
            document.getElementById('resultsSection').style.display = 'block';

            // 保存最终结果
            processedAvatarData = finalAvatar.toDataURL('image/png');
        }

        // UI 辅助函数
        function showProcessing() {
            document.getElementById('processingSection').style.display = 'block';
            document.getElementById('resultsSection').style.display = 'none';
        }

        function hideProcessing() {
            document.getElementById('processingSection').style.display = 'none';
        }

        function updateProgress(percent, status) {
            const progressBar = document.getElementById('progressBar');
            const processingStatus = document.getElementById('processingStatus');

            progressBar.style.width = percent + '%';
            progressBar.textContent = percent + '%';
            processingStatus.textContent = status;
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.innerHTML = `<div class="status-${type}">${message}</div>`;

            // 自动隐藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    statusDiv.innerHTML = '';
                }, 5000);
            }
        }

        // 下载头像
        function downloadAvatar() {
            if (!processedAvatarData) {
                showStatus('没有可下载的头像数据', 'error');
                return;
            }

            const link = document.createElement('a');
            link.download = 'avatar_' + Date.now() + '.png';
            link.href = processedAvatarData;
            link.click();
        }

        // 重置处理器
        function resetProcessor() {
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('faceInfo').style.display = 'none';
            document.getElementById('statusMessage').innerHTML = '';
            document.getElementById('fileInput').value = '';
            currentImageData = null;
            processedAvatarData = null;
        }
    </script>
</body>
</html>
