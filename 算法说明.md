一、准确识别人脸

1.对用户上传的图片进行人脸识别，要对个人简介中提取的头像进行人脸检测，通过468个3D关键点，基于虹膜分析，人脸检测与旋转追踪，准确识别到包含人脸的图片。
2.对于面部分辨率较低的照片进行超分放大处理，再进行识别。
3.基于人脸识别的结果进行校正，使人脸方向于标准头像的人脸方向相同，保存校正后的图片
4.对于不包含人脸的图片，给出提示信息。

二、头像标准裁切

1、头像比例是1：1
2、根据已经准确定位面部的位置，包括图片中人脸的长度，宽度比例，比对标准头像中的人脸的长度、宽度，并根据面部位置进行裁切，获取裁切后的图片，必须要保留完成的面部，根据原图质量如果有肩膀，保留一部分肩膀，如果没有则裁剪出完整的头像，如果有胸部，腰部以下部分，都可以裁切掉，不需要。
3、头部应占据照片的大部分空间，但需留有足够的空白边缘

三、去除背景
1、除了人像主体以外的背景是透明的，确保没有阴影和杂物干扰