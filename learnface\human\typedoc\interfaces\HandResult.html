<!DOCTYPE html><html class="default" lang="en" data-base=".."><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>HandResult | @vladmandic/human - v3.3.5</title><meta name="description" content="Documentation for @vladmandic/human"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script><script async src="../assets/hierarchy.js" id="tsd-hierarchy-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search"><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@vladmandic/human - v3.3.5</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../index.html">@vladmandic/human</a></li><li><a href="HandResult.html">HandResult</a></li></ul><h1>Interface HandResult</h1></div><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><p>Hand results</p>
</div><div class="tsd-comment tsd-typography"></div></section><div class="tsd-signature"><span class="tsd-signature-keyword">interface</span> <span class="tsd-kind-interface">HandResult</span> <span class="tsd-signature-symbol">{</span><br/>    <a class="tsd-kind-property" href="HandResult.html#annotations">annotations</a><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><a href="../types/Finger.html" class="tsd-signature-type tsd-kind-type-alias">Finger</a><span class="tsd-signature-symbol">,</span> <a href="../types/Point.html" class="tsd-signature-type tsd-kind-type-alias">Point</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="HandResult.html#box">box</a><span class="tsd-signature-symbol">:</span> <a href="../types/Box.html" class="tsd-signature-type tsd-kind-type-alias">Box</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="HandResult.html#boxraw">boxRaw</a><span class="tsd-signature-symbol">:</span> <a href="../types/Box.html" class="tsd-signature-type tsd-kind-type-alias">Box</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="HandResult.html#boxscore">boxScore</a><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="HandResult.html#fingerscore">fingerScore</a><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="HandResult.html#id">id</a><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="HandResult.html#keypoints">keypoints</a><span class="tsd-signature-symbol">:</span> <a href="../types/Point.html" class="tsd-signature-type tsd-kind-type-alias">Point</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="HandResult.html#label">label</a><span class="tsd-signature-symbol">:</span> <a href="../types/HandType.html" class="tsd-signature-type tsd-kind-type-alias">HandType</a><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="HandResult.html#landmarks">landmarks</a><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><a href="../types/Finger.html" class="tsd-signature-type tsd-kind-type-alias">Finger</a><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">curl</span><span class="tsd-signature-symbol">:</span> <a href="../types/FingerCurl.html" class="tsd-signature-type tsd-kind-type-alias">FingerCurl</a><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">direction</span><span class="tsd-signature-symbol">:</span> <a href="../types/FingerDirection.html" class="tsd-signature-type tsd-kind-type-alias">FingerDirection</a> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">;</span><br/>    <a class="tsd-kind-property" href="HandResult.html#score">score</a><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">;</span><br/><span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/vladmandic/human/blob/main/src/result.ts#L127">src/result.ts:127</a></li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg> Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Properties</h3><div class="tsd-index-list"><a href="HandResult.html#annotations" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>annotations</span></a>
<a href="HandResult.html#box" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>box</span></a>
<a href="HandResult.html#boxraw" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>box<wbr/>Raw</span></a>
<a href="HandResult.html#boxscore" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>box<wbr/>Score</span></a>
<a href="HandResult.html#fingerscore" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>finger<wbr/>Score</span></a>
<a href="HandResult.html#id" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>id</span></a>
<a href="HandResult.html#keypoints" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>keypoints</span></a>
<a href="HandResult.html#label" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>label</span></a>
<a href="HandResult.html#landmarks" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>landmarks</span></a>
<a href="HandResult.html#score" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>score</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Properties"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Properties</h2></summary><section><section class="tsd-panel tsd-member"><a id="annotations" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>annotations</span><a href="#annotations" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">annotations</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><a href="../types/Finger.html" class="tsd-signature-type tsd-kind-type-alias">Finger</a><span class="tsd-signature-symbol">,</span> <a href="../types/Point.html" class="tsd-signature-type tsd-kind-type-alias">Point</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></div><div class="tsd-comment tsd-typography"><p>detected hand keypoints combined into annotated parts</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/vladmandic/human/blob/main/src/result.ts#L145">src/result.ts:145</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="box" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>box</span><a href="#box" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">box</span><span class="tsd-signature-symbol">:</span> <a href="../types/Box.html" class="tsd-signature-type tsd-kind-type-alias">Box</a></div><div class="tsd-comment tsd-typography"><p>detected hand box</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/vladmandic/human/blob/main/src/result.ts#L137">src/result.ts:137</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="boxraw" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>box<wbr/>Raw</span><a href="#boxraw" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">boxRaw</span><span class="tsd-signature-symbol">:</span> <a href="../types/Box.html" class="tsd-signature-type tsd-kind-type-alias">Box</a></div><div class="tsd-comment tsd-typography"><p>detected hand box normalized to 0..1</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/vladmandic/human/blob/main/src/result.ts#L139">src/result.ts:139</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="boxscore" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>box<wbr/>Score</span><a href="#boxscore" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">boxScore</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>hand detection score</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/vladmandic/human/blob/main/src/result.ts#L133">src/result.ts:133</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="fingerscore" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>finger<wbr/>Score</span><a href="#fingerscore" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">fingerScore</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>hand skelton score</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/vladmandic/human/blob/main/src/result.ts#L135">src/result.ts:135</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="id" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>id</span><a href="#id" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">id</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>hand id</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/vladmandic/human/blob/main/src/result.ts#L129">src/result.ts:129</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="keypoints" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>keypoints</span><a href="#keypoints" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">keypoints</span><span class="tsd-signature-symbol">:</span> <a href="../types/Point.html" class="tsd-signature-type tsd-kind-type-alias">Point</a><span class="tsd-signature-symbol">[]</span></div><div class="tsd-comment tsd-typography"><p>detected hand keypoints</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/vladmandic/human/blob/main/src/result.ts#L141">src/result.ts:141</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="label" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>label</span><a href="#label" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">label</span><span class="tsd-signature-symbol">:</span> <a href="../types/HandType.html" class="tsd-signature-type tsd-kind-type-alias">HandType</a></div><div class="tsd-comment tsd-typography"><p>detected hand class</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/vladmandic/human/blob/main/src/result.ts#L143">src/result.ts:143</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="landmarks" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>landmarks</span><a href="#landmarks" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">landmarks</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><a href="../types/Finger.html" class="tsd-signature-type tsd-kind-type-alias">Finger</a><span class="tsd-signature-symbol">,</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-kind-property">curl</span><span class="tsd-signature-symbol">:</span> <a href="../types/FingerCurl.html" class="tsd-signature-type tsd-kind-type-alias">FingerCurl</a><span class="tsd-signature-symbol">;</span> <span class="tsd-kind-property">direction</span><span class="tsd-signature-symbol">:</span> <a href="../types/FingerDirection.html" class="tsd-signature-type tsd-kind-type-alias">FingerDirection</a> <span class="tsd-signature-symbol">}</span><span class="tsd-signature-symbol">&gt;</span></div><div class="tsd-comment tsd-typography"><p>detected hand parts annotated with part gestures</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/vladmandic/human/blob/main/src/result.ts#L147">src/result.ts:147</a></li></ul></aside></section><section class="tsd-panel tsd-member"><a id="score" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>score</span><a href="#score" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">score</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>hand overal score</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in <a href="https://github.com/vladmandic/human/blob/main/src/result.ts#L131">src/result.ts:131</a></li></ul></aside></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-filter-visibility"><span class="settings-label">Member Visibility</span><ul id="tsd-filter-options"><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-inherited" name="inherited" checked/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Inherited</span></label></li></ul></div><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Properties"><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Properties</summary><div><a href="#annotations" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>annotations</span></a><a href="#box" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>box</span></a><a href="#boxraw" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>box<wbr/>Raw</span></a><a href="#boxscore" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>box<wbr/>Score</span></a><a href="#fingerscore" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>finger<wbr/>Score</span></a><a href="#id" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>id</span></a><a href="#keypoints" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>keypoints</span></a><a href="#label" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>label</span></a><a href="#landmarks" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>landmarks</span></a><a href="#score" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-1024"></use></svg><span>score</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../index.html">@vladmandic/human - v3.3.5</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer></footer><div class="overlay"></div></body></html>
