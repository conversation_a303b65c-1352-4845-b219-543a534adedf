# 项目结构说明 v1.1

本文档旨在详细说明 `gejiesoft-pdf-tools` 项目的目录结构、主要文件及其功能、重要性以及是否可以删除。

## 根目录

*   **`docs/`**:
    *   **用途**: 存放项目相关的文档，如需求说明、设计文档、会议记录、待办事项列表等。
    *   **重要性**: 高，包含项目的重要说明和历史信息。
    *   **可删除性**: 不可删除。

*   **`frontend/`**:
    *   **用途**: 存放Web用户界面的前端代码，通常包含 `index.html` (主页面), `script.js` (JavaScript逻辑) 和 `style.css` (样式表)。
    *   **重要性**: 高，用户与程序交互的界面部分。
    *   **可删除性**: 不可删除。

*   **`main/`**:
    *   **用途**: 存放项目的后端Go语言源代码。这是项目的核心逻辑所在。
    *   **重要性**: 高，项目的核心业务逻辑和API实现。
    *   **可删除性**: 不可删除。

*   **`output/`**:
    *   **用途**: 存放程序运行时生成的输出文件，如PPT生成结果、图片处理结果等。
    *   **重要性**: 中，用于存储程序运行的成果。
    *   **可删除性**: 内容可定期清理，目录本身建议保留。

*   **`templates/`**:
    *   **用途**: 最初用于PPT信息提取的测试文件目录。可能包含用于测试提取功能的PDF或PPT样本文件。
    *   **重要性**: 低，主要用于测试。
    *   **可删除性**: 如果不再需要相关测试或样本文件，可以删除。

*   **`templates-ppt/`**:
    *   **用途**: 存放用于生成新PPT的模板文件（例如 `.pptx` 文件）。
    *   **重要性**: 高，PPT生成功能依赖这些模板。
    *   **可删除性**: 不可删除（除非PPT生成功能不再需要）。

*   **`gejiesoft-pdf-tools.exe`**:
    *   **用途**: 项目编译后的可执行文件，用于启动整个应用程序。
    *   **重要性**: 高，是用户直接运行程序的文件。
    *   **可删除性**: 可删除，因为可以通过 `build.bat` (或 `main/build.bat`) 重新生成。

*   **`docker-compose.yml`**:
    *   **用途**: Docker Compose配置文件，用于定义和运行多容器Docker应用程序（例如，如果项目包含数据库或其他服务）。
    *   **重要性**: 高，用于Docker化部署。
    *   **可删除性**: 不可删除（如果需要Docker部署）。

*   **`docker-compose-stable.yml`**:
    *   **用途**: 可能是 `docker-compose.yml` 的一个稳定版本或特定配置，用于生产环境或特定部署场景。
    *   **重要性**: 中，取决于其使用场景。
    *   **可删除性**: 如果项目不再需要特定的稳定版或部署配置，可以删除。

*   **`Dockerfile`**:
    *   **用途**: Docker镜像构建文件，用于定义如何构建应用程序的Docker镜像。
    *   **重要性**: 高，用于Docker化部署。
    *   **可删除性**: 不可删除（如果需要Docker部署）。

*   **`env.example`**:
    *   **用途**: 环境变量的示例文件，说明了应用程序运行时所需的各种环境变量及其格式。
    *   **重要性**: 中，为开发者提供了配置指南。
    *   **可删除性**: 可删除，但建议保留作为参考。

*   **`.env`**:
    *   **用途**: 实际的环境变量文件，通常包含敏感信息或本地配置，如API密钥、数据库连接字符串等。`main/config.go` 会优先加载此文件中的环境变量。通常不应该提交到版本控制。
    *   **重要性**: 高，包含程序运行所需的实际配置。
    *   **可删除性**: 不可删除（运行时需要），但应确保其内容不被版本控制系统追踪。

*   **`LICENSE`**:
    *   **用途**: 项目的开源许可证文件，规定了他人使用、分发和修改代码的条款。
    *   **重要性**: 高，法律文档。
    *   **可删除性**: 不可删除。

*   **`README.md`**:
    *   **用途**: 项目的介绍文件，通常包含项目描述、安装指南、使用说明、贡献指南等。
    *   **重要性**: 高，项目的门面文档。
    *   **可删除性**: 不可删除。

*   **`build_standalone.bat`**:
    *   **用途**: Windows批处理脚本，用于构建独立运行的应用程序。可能与 `main/build.bat` 功能类似或互补。
    *   **重要性**: 高，构建流程的一部分。
    *   **可删除性**: 不可删除，除非不再需要独立构建。

*   **`check_encoding.bat`**:
    *   **用途**: Windows批处理脚本，用于检查项目文件的编码格式。
    *   **重要性**: 低，辅助开发工具。
    *   **可删除性**: 可删除，如果团队没有编码规范检查需求。

*   **`dockerimages/`**:
    *   **用途**: 存放Docker镜像相关的文件，例如预构建的Docker镜像压缩包（`.tar` 文件）。
    *   **重要性**: 中，取决于是否使用预构建镜像。
    *   **可删除性**: 如果不使用预构建镜像或有其他方式管理镜像，可以删除其内容。

*   **`temp/`**:
    *   **用途**: 临时文件目录，用于存放程序运行时的临时数据，例如解压后的文件、临时日志等。
    *   **重要性**: 低，临时存储。
    *   **可删除性**: 可删除，程序运行时会重新创建或使用。

*   **`temp_ppt_extract/`**:
    *   **用途**: 临时PPT解压目录，用于PPT信息提取过程中解压PPT文件到此。
    *   **重要性**: 低，临时存储。
    *   **可删除性**: 可删除，程序运行时会自动创建或使用。

*   **`test_docker.bat`**:
    *   **用途**: Windows批处理脚本，用于测试Docker相关的功能或部署。
    *   **重要性**: 低，测试脚本。
    *   **可删除性**: 可删除，如果不再需要相关测试。

*   **`test_env_complete.bat`**:
    *   **用途**: Windows批处理脚本，用于测试完整的环境变量配置是否正确。
    *   **重要性**: 低，测试脚本。
    *   **可删除性**: 可删除，如果不再需要相关测试。

*   **`test_env_local.bat`**:
    *   **用途**: Windows批处理脚本，用于测试本地开发环境中的环境变量配置。
    *   **重要性**: 低，测试脚本。
    *   **可删除性**: 可删除，如果不再需要相关测试。

*   **`test_fixes.bat`**:
    *   **用途**: Windows批处理脚本，可能用于测试特定bug修复后的功能。
    *   **重要性**: 低，测试脚本。
    *   **可删除性**: 可删除，如果相关修复已验证且不再需要。

*   **`test_local.bat`**:
    *   **用途**: Windows批处理脚本，用于在本地环境中运行一般性测试。
    *   **重要性**: 低，测试脚本。
    *   **可删除性**: 可删除，如果不再需要相关测试。

*   **`test_new_features.bat`**:
    *   **用途**: Windows批处理脚本，用于测试新开发的功能。
    *   **重要性**: 低，测试脚本。
    *   **可删除性**: 可删除，如果新功能已稳定且不再需要。

*   **`test_ppt_generator.bat`**:
    *   **用途**: Windows批处理脚本，专门用于测试PPT生成功能。
    *   **重要性**: 低，测试脚本。
    *   **可删除性**: 可删除，如果PPT生成功能已稳定且不再需要此脚本。

*   **`verify_security.bat`**:
    *   **用途**: Windows批处理脚本，可能用于执行安全相关的检查或测试。
    *   **重要性**: 低，辅助开发工具。
    *   **可删除性**: 可删除，如果团队没有安全验证需求。

*   **`.gitignore`**:
    *   **用途**: Git版本控制忽略文件，指定哪些文件和目录不应该被Git追踪。
    *   **重要性**: 高，管理版本控制。
    *   **可删除性**: 不可删除。

*   **`.dockerignore`**:
    *   **用途**: Docker构建忽略文件，指定在构建Docker镜像时应忽略哪些文件和目录。
    *   **重要性**: 高，优化Docker镜像大小和构建速度。
    *   **可删除性**: 不可删除（如果需要Docker部署）。

## `main/` 目录

*   **`main.go`**:
    *   **用途**: Go程序的入口点。负责初始化应用程序、加载配置、启动Web服务器等。
    *   **重要性**: 高，程序的启动核心。
    *   **可删除性**: 不可删除。

*   **`web_server.go`**:
    *   **用途**: 实现Web服务器逻辑，定义API路由、处理HTTP请求、调用后端服务等。
    *   **重要性**: 高，提供API服务。
    *   **可删除性**: 不可删除。

*   **`ppt_generator.go`**:
    *   **用途**: 包含 Go 语言的 PPT 生成逻辑。在当前架构下，它主要负责集成和调用 `main/ppt_generator/` 目录下的 Python PPT 生成模块，并处理与 Go 后端的数据交互。
    *   **重要性**: 高，PPT 生成功能在 Go 后端的核心接口。
    *   **可删除性**: 不可删除（如果需要PPT生成功能）。

*   **`ppt_generator/`**:
    *   **用途**: 存放 Python 实现的 PPT 生成模块代码，包括 `ppt_generator.py` 和其依赖（如 `requirements.txt`、字体文件等）。这是实际执行 PPT 生成的核心逻辑。
    *   **重要性**: 高，是当前 PPT 生成功能的主要实现。
    *   **可删除性**: 不可删除（如果需要PPT生成功能）。

*   **`ppt_generator_test.go`**:
    *   **用途**: 包含 `ppt_generator.go` 的单元测试。
    *   **重要性**: 中，确保PPT生成逻辑的正确性。
    *   **可删除性**: 可删除，但不利于代码质量维护。

*   **`photo_enhancer.go`**:
    *   **用途**: 包含照片增强和矫正功能的逻辑。
    *   **重要性**: 高，如果项目有图片处理需求（如人像抠图）。
    *   **可删除性**: 不可删除（如果相关功能在使用）。

*   **`image_processor.go`**:
    *   **用途**: 包含通用的图片处理核心逻辑。可能与 `photo_enhancer.go` 协同工作。
    *   **重要性**: 高，如果项目有图片处理需求（如人像检测、图片分类）。
    *   **可删除性**: 不可删除（如果相关功能在使用）。

*   **`config.go`**:
    *   **用途**: 负责应用程序的配置管理，支持从环境变量、配置文件（如 `config.json`）加载配置。
    *   **重要性**: 高，管理程序配置。
    *   **可删除性**: 不可删除。

*   **`config.json`**:
    *   **用途**: 应用程序的配置文件，存放各种配置参数。
    *   **重要性**: 高，运行时配置。
    *   **可删除性**: 不可删除（程序运行时需要加载）。

*   **`go.mod`**:
    *   **用途**: Go模块文件，定义了项目的模块路径、Go版本以及项目依赖的外部模块。
    *   **重要性**: 高，Go项目依赖管理的关键。
    *   **可删除性**: 不可删除。

*   **`go.sum`**:
    *   **用途**: Go模块的校验和文件，用于验证项目依赖的完整性和安全性。
    *   **重要性**: 高，Go项目依赖管理的关键。
    *   **可删除性**: 不可删除。

*   **`build.bat`**:
    *   **用途**: 位于 `main` 目录下的Windows批处理脚本，专门用于构建Go应用程序，生成可执行文件。
    *   **重要性**: 高，构建Go程序的脚本。
    *   **可删除性**: 不可删除。

*   **`types.go`**:
    *   **用途**: 定义Go语言中的各种数据结构（如 `struct`）、接口、枚举等，通常用于规范数据模型。你提到的 `ResumeInfo` 结构体很可能定义在这里。
    *   **重要性**: 高，定义数据模型。
    *   **可删除性**: 不可删除。

*   **`post_aibot_api.go`**:
    *   **用途**: 负责与自定义 Aibot API 进行交互，发送文本内容进行分析，并接收结构化响应（如简历信息）。
    *   **重要性**: 高，是项目与 AI 服务的核心集成点。
    *   **可删除性**: 不可删除（如果需要 AI 信息提取功能）。

*   **`template_analyzer.go`**:
    *   **用途**: 用于分析 PPTX 模板文件，识别并提取文本和图片占位符，并生成模板映射配置。这对于 PPT 自动生成至关重要。
    *   **重要性**: 高，是 PPT 生成功能的基础。
    *   **可删除性**: 不可删除（如果需要 PPT 自动生成功能）。

*   **`test_output/`**:
    *   **用途**: 存放Go测试运行时生成的临时输出文件。
    *   **重要性**: 低，临时文件。
    *   **可删除性**: 可删除，通常在测试运行后可清空。

*   **`templates/` (在 `main` 目录下)**:
    *   **用途**: 包含 Go 程序内部使用的模板文件，例如 `template_mapping.json`，用于存储 PPT 模板的分析结果和占位符映射。此目录下的文件与根目录的 `templates/` 目录用途不同。
    *   **重要性**: 高，PPT 生成功能的关键配置。
    *   **可删除性**: 不可删除。

*   **`test_sdk/`**:
    *   **用途**: 可能用于存放与某个SDK（Software Development Kit）相关的测试代码或测试数据。
    *   **重要性**: 低，测试相关。
    *   **可删除性**: 可删除，如果不再需要相关SDK测试。

--- 