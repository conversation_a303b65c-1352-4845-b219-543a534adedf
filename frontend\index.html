<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>格界PPT信息提取工具</title>
    <link href="tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="font-awesome.min.css">
    <style>
        .drop-zone {
            border: 2px dashed #cbd5e0;
            transition: all 0.3s ease;
        }
        .drop-zone.dragover {
            border-color: #4299e1;
            background-color: #ebf8ff;
        }
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- 头部 -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">
                <i class="fas fa-file-powerpoint text-blue-600 mr-3"></i>
                格界PPT信息提取工具
            </h1>
            <p class="text-gray-600">上传PPT文件，自动提取嘉宾简历信息</p>
        </div>

        <!-- 主要内容区域 -->
        <div class="max-w-4xl mx-auto">
            <!-- 文件上传区域 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div id="uploadZone" class="drop-zone rounded-lg p-8 text-center cursor-pointer">
                    <div class="mb-4">
                        <i class="fas fa-cloud-upload-alt text-4xl text-gray-400"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-700 mb-2">拖拽PPT文件到此处</h3>
                    <p class="text-gray-500 mb-4">或点击选择文件</p>
                    <input type="file" id="fileInput" accept=".ppt,.pptx" class="hidden">
                    <button id="selectFileBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                        <i class="fas fa-folder-open mr-2"></i>选择文件
                    </button>
                </div>
                
                <div id="fileInfo" class="hidden mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-600 mr-3"></i>
                        <div>
                            <p class="font-semibold text-green-800" id="fileName"></p>
                            <p class="text-sm text-green-600" id="fileSize"></p>
                        </div>
                    </div>
                </div>

                <!-- 新增PPT模板上传入口 -->
                <!--
                <div class="mt-6">
                    <label class="block text-gray-700 font-medium mb-2" for="pptTemplateInput">
                        可选：上传自定义PPT模板（不上传则使用默认模板）
                    </label>
                    <input type="file" id="pptTemplateInput" accept=".ppt,.pptx" class="border border-gray-300 rounded px-3 py-2 w-full" />
                    <p class="text-sm text-blue-600 mt-2">
                        未上传模板时，系统将默认使用 <span class="font-mono text-xs bg-gray-100 px-2 py-1 rounded">D:\Dev\gejiesoft-pdf-tools\templates-ppt\ppt-teample1.pptx</span>
                    </p>
                </div>
                -->
            </div>

            <!-- 提取按钮 -->
            <div class="text-center mb-6">
                <button id="extractBtn" class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg text-lg font-semibold transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                    <i class="fas fa-magic mr-2"></i>开始提取
                </button>
            </div>

            <!-- 处理日志区域 -->
            <div id="processingLogSection" class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">
                    <i class="fas fa-terminal text-purple-600 mr-2"></i>处理日志
                </h3>
                <div id="processingLog" class="bg-gray-50 p-4 rounded-lg max-h-60 overflow-y-auto font-mono text-sm">
                    <div class="text-gray-500 text-center py-4">
                        <i class="fas fa-info-circle mr-2"></i>
                        处理日志将在这里显示
                    </div>
                </div>
            </div>

            <!-- 结果展示区域 -->
            <div id="resultSection" class="hidden">
                <!-- 新增 原始图片提取预览 卡片 -->
                <div id="originalImagePreviewSection" class="bg-white rounded-lg shadow-md p-6 mb-6 fade-in hidden">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">
                        <i class="fas fa-image text-blue-600 mr-2"></i>原始图片提取预览
                    </h3>
                    <div id="originalExtractedImagesContainer" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 max-h-80 overflow-y-auto p-2 bg-gray-50 rounded-lg">
                        <!-- 原始提取的图片将由 script.js 动态添加到这里 -->
                    </div>
                </div>

                <!-- 提取结果预览卡片 -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6 fade-in">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">
                        <i class="fas fa-eye text-purple-600 mr-2"></i>提取结果预览
                    </h3>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- JSON预览 -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-semibold text-gray-700 mb-3 flex items-center">
                                <i class="fas fa-file-code text-blue-600 mr-2"></i>JSON数据预览
                            </h4>
                            <div id="jsonPreview" class="bg-white border rounded p-3 max-h-80 overflow-y-auto font-mono text-sm text-gray-700">
                                <div class="text-gray-400 text-center py-8">
                                    <i class="fas fa-info-circle mr-2"></i>
                                    提取完成后将显示JSON数据
                                </div>
                            </div>
                        </div>
                        
                        <!-- 提取的照片预览 -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-semibold text-gray-700 mb-3 flex items-center">
                                <i class="fas fa-images text-green-600 mr-2"></i>提取照片预览
                            </h4>
                            <div id="extractedPhotosPreview" class="grid grid-cols-2 gap-2 max-h-80 overflow-y-auto">
                                <div class="text-gray-400 text-center py-8 col-span-2">
                                    <i class="fas fa-camera mr-2"></i>
                                    提取完成后将显示照片
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 下载按钮区域 -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6 fade-in">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">
                        <i class="fas fa-download text-green-600 mr-2"></i>PPT信息提取结果下载
                    </h3>
                    
                    <div class="flex space-x-4">
                        <button id="downloadJsonBtn" class="flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
                            <i class="fas fa-file-code mr-2"></i>
                            下载JSON文件
                        </button>
                        
                        <button id="downloadImagesBtn" class="flex items-center justify-center bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-colors">
                            <i class="fas fa-images mr-2"></i>
                            下载图片文件
                        </button>
                    </div>
                </div>

                <!-- PPT文字卡片 -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6 fade-in">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">
                        <i class="fas fa-file-alt text-blue-600 mr-2"></i>PPT文字
                    </h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <i class="fas fa-user text-gray-500 w-6"></i>
                            <span class="font-medium text-gray-700 mr-3">姓名:</span>
                            <span id="extractedName" class="text-gray-900"></span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-hospital text-gray-500 w-6"></i>
                            <span class="font-medium text-gray-700 mr-3">医院:</span>
                            <span id="extractedHospital" class="text-gray-900"></span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-stethoscope text-gray-500 w-6"></i>
                            <span class="font-medium text-gray-700 mr-3">科室:</span>
                            <span id="extractedDepartment" class="text-gray-900"></span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-award text-gray-500 w-6"></i>
                            <span class="font-medium text-gray-700 mr-3">职称:</span>
                            <span id="extractedTitle" class="text-gray-900"></span>
                        </div>
                    </div>
                    
                    <!-- 详细简历信息 -->
                    <div class="mt-6 space-y-4">
                        <!-- 职务职称 -->
                        <div id="positionSection" class="hidden">
                            <h4 class="font-medium text-gray-700 mb-2">
                                <i class="fas fa-briefcase text-blue-600 mr-2"></i>职务职称:
                            </h4>
                            <div id="extractedPosition" class="bg-gray-50 p-4 rounded-lg text-gray-800 text-sm leading-relaxed max-h-32 overflow-y-auto"></div>
                        </div>
                        
                        <!-- 学术任职 -->
                        <div id="academicTitleSection" class="hidden">
                            <h4 class="font-medium text-gray-700 mb-2">
                                <i class="fas fa-graduation-cap text-green-600 mr-2"></i>学术任职:
                            </h4>
                            <div id="extractedAcademicTitle" class="bg-gray-50 p-4 rounded-lg text-gray-800 text-sm leading-relaxed max-h-32 overflow-y-auto"></div>
                        </div>
                        
                        <!-- 专业擅长 -->
                        <div id="specialtySection" class="hidden">
                            <h4 class="font-medium text-gray-700 mb-2">
                                <i class="fas fa-heartbeat text-red-600 mr-2"></i>专业擅长:
                            </h4>
                            <div id="extractedSpecialty" class="bg-gray-50 p-4 rounded-lg text-gray-800 text-sm leading-relaxed max-h-24 overflow-y-auto"></div>
                        </div>
                        
                        <!-- 学术成果 -->
                        <div id="academicOutputSection" class="hidden">
                            <h4 class="font-medium text-gray-700 mb-2">
                                <i class="fas fa-trophy text-yellow-600 mr-2"></i>学术成果:
                            </h4>
                            <div id="extractedAcademicOutput" class="bg-gray-50 p-4 rounded-lg text-gray-800 text-sm leading-relaxed max-h-32 overflow-y-auto"></div>
                        </div>
                        
                        <!-- 其他信息 -->
                        <div id="otherSection" class="hidden">
                            <h4 class="font-medium text-gray-700 mb-2">
                                <i class="fas fa-info-circle text-purple-600 mr-2"></i>其他信息:
                            </h4>
                            <div id="extractedOther" class="bg-gray-50 p-4 rounded-lg text-gray-800 text-sm leading-relaxed max-h-24 overflow-y-auto"></div>
                        </div>
                    </div>
                    
                    <!-- 详细介绍 -->
                    <div class="mt-6">
                        <h4 class="font-medium text-blue-700 mb-2">介绍:</h4>
                        <div id="extractedDescription" class="bg-gray-50 p-4 rounded-lg text-blue-800 text-sm leading-relaxed max-h-40 overflow-y-auto"></div>
                    </div>
                </div>

                <!-- 智能头像处理预览 区块 -->
                <div id="photoPreviewSection" class="bg-white rounded-lg shadow-md p-6 mb-6 fade-in hidden">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">
                        <i class="fas fa-robot text-indigo-600 mr-2"></i>智能头像处理预览
                    </h3>
                    
                    <!-- 成功预览区域 -->
                    <div id="photoPreviewSuccess" class="flex justify-center items-center h-48 bg-gray-50 rounded-lg" style="padding-top: 9.6px; padding-left: 9.6px; padding-right: 9.6px; padding-bottom: 0;">
                        <img id="bestPortraitImage" src="" alt="最佳人像照片" class="max-h-full max-w-full object-contain">
                    </div>
                    
                    <!-- 错误提示区域 -->
                    <div id="photoPreviewError" class="hidden bg-red-50 border border-red-200 rounded-lg p-6 text-center">
                        <div class="mb-4">
                            <i class="fas fa-exclamation-triangle text-red-600 text-4xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-red-800 mb-2">人像抠图失败</h4>
                        <div class="text-red-700 text-sm space-y-1">
                            <p>• 请检查PPT中的图片是否包含清晰的人像</p>
                            <p>• 请确保图片质量足够高（建议100dpi以上）</p>
                            <p>• 请检查图片中人脸是否清晰可见</p>
                        </div>
                        <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded text-yellow-800 text-sm">
                            <i class="fas fa-info-circle mr-2"></i>
                            无法生成带头像的PPT，请修复图片后重新上传
                        </div>
                    </div>
                    
                    <p class="text-center text-gray-600 text-sm mt-2" id="photoPreviewCaption">此处将显示抠图后的人像</p>
                </div>



                <!-- PPT生成区域 -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6 fade-in">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">
                        <i class="fas fa-file-powerpoint text-yellow-600 mr-2"></i>基于提取的结果生成PPT
                    </h3>
                    <div class="flex space-x-4">
                        <button id="generatePPTBtn" class="flex items-center justify-center bg-yellow-600 hover:bg-yellow-700 text-white px-6 py-3 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-file-powerpoint mr-2"></i>
                            生成PPT
                        </button>
                        <button id="downloadPPTBtn" class="flex items-center justify-center bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            <i class="fas fa-download mr-2"></i>
                            下载PPT文件
                        </button>
                        <button id="downloadCutoutBtn" class="flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed hidden" disabled>
                            <i class="fas fa-user-circle mr-2"></i>
                            下载抠图结果
                        </button>
                    </div>
                </div>
            </div>

            <!-- 错误提示 -->
            <div id="errorMessage" class="hidden bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle text-red-600 mr-3"></i>
                    <span id="errorText" class="text-red-800"></span>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html> 