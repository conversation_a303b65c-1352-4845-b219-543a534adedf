# -*- coding: utf-8 -*-

from odoo import models, fields, _


class ResUsers(models.Model):
    _inherit = "res.users"

    res_users_image_ids = fields.One2many(
        'res.users.image', 'res_user_id', string="人脸识别用户的图像", copy=True)
    face_emotion = fields.Selection(selection=[
        ('neutral', '自然'),
        ('happy', '快乐'),
        ('sad', '忧伤'),
        ('angry', '生气'),
        ('fearful', '恐惧'),
        ('disgusted', '厌恶的表情'),
        ('surprised', '吃惊'),
        ('any', '任意表情')], string="表情", required=True, default='any', help="允许访问必须做出的表情")
    face_gender = fields.Selection(selection=[
        ('male', '男性'),
        ('female', '女性'),
        ('any', '任何'),
    ], string="性别", required=True, default='any',
        help="允许访问的性别")
    face_age = fields.Selection(selection=[
        ('20', '0-20'),
        ('30', '20-30'),
        ('40', '30-40'),
        ('50', '40-50'),
        ('60', '50-60'),
        ('70', '60-任何'),
        ('any', '任何'),
    ], string="年龄", required=True, default='any',
        help="允许访年龄")


class UserImage(models.Model):
    _name = 'res.users.image'
    _description = "User Image"
    _inherit = ['image.mixin']
    _order = 'sequence, id'

    name = fields.Char("Name", required=True)
    sequence = fields.Integer(default=10, index=True)
    image = fields.Image("Face snapshot photo", required=True)
    image_detection = fields.Image("Face detection photo")
    res_user_id = fields.Many2one(
        'res.users', "User", index=True, ondelete='cascade')
    descriptor = fields.Char("Descriptor FR", readonly="1")

    _sql_constraints = [
        ('check_descriptor', 'check(length(descriptor)>50)',
         'Descriptor length must be more then 50'),
    ]
