16.7.5.1 [IMP] human.js new version lib
16.7.4.14 [FIX] warning Image field error
16.7.4.13 [FIX] chrome 113 error [ADD] select engine
16.7.4.12 [FIX] warning start server srting attribute to string
16.7.4.11 [FIX] res.user add photo "save and new" button error
16.7.4.10 [FIX] kiosk page as default odoo
[FIX] kiosk mode face config
[ADD] timeout feature when many failed 
16.7.3.10 [DEL] kiosk login handle
16.7.3.9 [FIX] new config for detector lib
16.6.3.9 [FIX] path to models
16.6.3.8 [ADD] mesh display [IMP] detect speed
16.6.2.8 [IMP] human.js new version lib
15.6.2.7 [ADD] new version libs + webgl backend
15.5.2.7 [ADD] new access rules for HR PRO groups, you can set only 'Manual Attendance' groups from base
15.5.2.6 [FIX] button media show in kanban
15.5.2.3 [FIX] speedup
14.3.2.3 [ADD] set scales face similifity and anti spoofing from config
14.3.2.3 [FIX] face rec webcam display
14.3.2.2 [FIX] face rec security add delete admin
[ADD] copability with breaks
13.2.2.2 - [FIX] kiosk mode with different users and employee work correct
13.2.2.1 - [ADD] work in kiosk mode with auto check in/out
13.1.2.1 - [ADD] work in kiosk mode
13.0.2.1 - [ADD] popup message if no users photos uploaded
13.0.1.1 - [ADD] user can upload image
13.0.0.1 - init commit
