odoo.define('hr_attendance_face_recognition.my_attendances', function (require) {
    "use strict";

    var core = require('web.core');
    var Attendances = require('hr_attendance.my_attendances');
    var _t = core._t;
    var config = require('web.config');
    var Dialog = require('web.Dialog');

    var FaceRecognitionDialog = Dialog.extend({
        template: 'WebCamDialogFaceRecognition',
        init: function (parent, options) {
            options = options || {};
            options.fullscreen = config.device.isMobile;
            options.fullscreen = true;
            options.dialogClass = options.dialogClass || '' + ' o_act_window';
            options.size = 'large';
            options.title = _t("人脸识别程序执行中，请稍后.....");
            this.labels_ids = options.labels_ids;
            this.descriptor_ids = options.descriptor_ids;
            this.labels_ids_emp = options.labels_ids_emp || [];
            // if face_recognition_mode true, after finded employee
            // call my_attendance for that employee without face_recognition
            console.log(options, 'options DIALOG')
            console.log(this)
            this.face_recognition_mode = options.face_recognition_mode;
            this.break = options.break;
            this.parent = parent;
            this._super(parent, options);
        },

        start: function () {
            let self = this;
            return this._super.apply(this, arguments).then(() => {
                Webcam.set({
                    width: document.body.scrollWidth,
                    height: document.body.scrollHeight,
                    dest_width: document.body.scrollWidth,
                    dest_height: document.body.scrollHeight,
                    image_format: 'jpeg',
                    jpeg_quality: 100,
                    //force_flash: false,
                    //fps: 24,
                    swfURL: '/hr_attendance_face_recognition_pro/static/src/libs/webcam.swf',
                    constraints: { optional: [{ minWidth: 600 }] }
                });
                Webcam.attach(this.$('#live_webcam')[0]);
                Webcam.on('live', (data) => {
                    // this.canvasGorizonCenter(canvas, 'inherit');
                    this.count_fail = 0;
                    self.drawVideo();
                });
            });
        },

        check_in_out: function (canvas, employee) {
            // var debounced = _.debounce(() => {
            this.parent.face_recognition_access = true;

            if (this.face_recognition_mode) {
                this.parent.do_action({
                    type: 'ir.actions.client',
                    tag: 'hr_attendance_my_attendances',
                    context: {
                        // check in/out without face recognition
                        'face_recognition_force': true,
                        'employee': employee,
                        'face_recognition_auto': this.parent.face_recognition_auto,
                        'webcam_snapshot': this.parent.webcam_snapshot,
                        'face_recognition_image': this.parent.face_recognition_image
                    },
                });
                return
            }
            // если нажата кнопка перерыва
            if (this.break)
                this.parent.update_break_resume();
            else
                this.parent.update_attendance();
            // }, 500, true);
            // debounced();
        },

        // check_face_filter: function (age, gender, emotion) {
        //     var age_access = false, gender_access = false, emotion_access = false;

        //     var p1 = this.parent.face_age.split('-')[0];
        //     var p2 = this.parent.face_age.split('-')[1];
        //     if (p1 === 'any')
        //         p1 = 0;
        //     if (p2 === 'any')
        //         p2 = 1000;
        //     p1 = Number(p1)
        //     p2 = Number(p2)

        //     if (age >= p1 && age <= p2)
        //         age_access = true;
        //     if (gender === this.parent.face_gender)
        //         gender_access = true;
        //     if (emotion === this.parent.face_emotion)
        //         emotion_access = true;

        //     if (this.parent.face_age === 'any-any')
        //         age_access = true;
        //     if (this.parent.face_gender === 'any')
        //         gender_access = true;
        //     if (this.parent.face_emotion === 'any')
        //         emotion_access = true;

        //     if (!age_access || !gender_access || !emotion_access)
        //         return false;
        //     return true;
        // },

        antiSpoofingCheck: async function (image) {
            await tf.ready();
            // load model
            const model = await tf.loadGraphModel('/hr_attendance_face_recognition_pro/static/src/js/models/anti-spoofing.json');
            const resized = tf.image.resizeBilinear(image, [128, 128]);
            const expanded = tf.expandDims(resized, 0);
            const res = model.execute(expanded, ['activation_4']);
            return res.dataSync()[0]
        },

        face_detection: async function (video, canvas) {
            const result = await this.parent.human.detect(canvas);

            if (result.face.length) {
                await this.parent.human.draw.all(canvas, result);

                for (let i = 0; i < this.descriptor_ids.length; i++) {
                    if (this.parent.face_recognition_pro_photo_check) {
                        let check = result.face[0].real;
                        console.log("check: " + check, this.parent.face_recognition_pro_scale_spoofing / 100)
                        if (check < this.parent.face_recognition_pro_scale_spoofing / 100)
                            continue
                        // let live = result.face[0].live;
                        // if (live < 80)
                        //     continue
                    }
                    const found = result.face[0].embedding
                    // debugger
                    const similarity = this.parent.human.match.similarity(Array.from(this.descriptor_ids[i]), found);
                    // console.log(found, 'found')
                    // console.log(Array.from(this.descriptor_ids[i]), 'db')
                    console.log(`face ${i} is ${100 * similarity}% simmilar`);
                    // Found employee
                    console.log(100 * similarity)
                    if (100 * similarity > this.parent.face_recognition_pro_scale_recognition) {
                        if (this.parent.face_recognition_store) {
                            await Webcam.snap(data_uri => {
                                this.parent.webcam_snapshot = data_uri.split(',')[1];
                            });
                            let imageDescriptorBase64 = await this._getImageFromHuman(canvas, result);
                            this.parent.face_recognition_image = imageDescriptorBase64.split(',')[1];
                        }
                        this.check_in_out(canvas, this.labels_ids[i]);
                        return 'stop'
                    }
                }
            }
            return false;
        },

        _getImageFromHuman: async function (size, result) {
            // create new canvas
            let can = document.createElement('canvas');
            can.width = size.width;
            can.height = size.height;

            // draw human output and return as image base64
            this.parent.human.draw.all(can, result);
            return can.toDataURL();
        },

        sleep: function (ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        },

        // canvasGorizonCenter: function (canvas, position) {
        //     canvas.style.position = position;
        //     canvas.style.left = '50%';
        //     canvas.style.marginLeft = '-' + ($(canvas).width() / 2).toString() + 'px';
        // },

        drawVideo: async function () {
            // 1. текущую картинку с тега видео
            if (!Webcam.live)
                return
            const video = this.$el.find("video")[0];
            const canvas = this.$el.find("#ocr_canvas")[0];
            if (!canvas || !video)
                return

            //如果相机的图像太大
            //将其缩小到对话框的大小
            if (video.videoWidth > this.$el.width()) {
                canvas.width = this.$el.width();
                canvas.height = (this.$el.width() * video.videoHeight) / video.videoWidth;
            }
            else {
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
            }

            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
            const res = await this.face_detection(video, canvas);
            if (res == 'stop')
                return
            this.count_fail += 1;
            this.trigger_up('display_notification', { title: 'Move Zig', message: 'All your bases are belong to us' });
            if (this.count_fail > this.parent.face_recognition_pro_timeout
                 && this.parent.face_recognition_pro_timeout != 0) {
                this.destroy()
                this.tempAlert(`人脸识别失败，超时 ${this.parent.face_recognition_pro_timeout}x0.1秒，请注意您的环境光线，在光线好的情况下重新尝试。`, 5000)
                return
            }
            await this.sleep(50);
            this.drawVideo(video, canvas, ctx);
        },

        tempAlert: function (msg, duration) {
            let el = document.createElement("div")
            el.innerHTML = `
            <div class="cookiesContent" id="cookiesPopup">
              <img src="/hr_attendance_face_recognition_pro/static/description/fail.png" alt="cookies-img" />
              <p>${msg}</p>
              <button class="accept">人脸数据比对不通过</button>
            </div>`;
            el.setAttribute("style", "position:absolute;top:50%;left:50%;transform: translate(-50%,-50%);");
            setTimeout(function () {
                el.parentNode.removeChild(el);
            }, duration);
            document.body.appendChild(el);
        },

        destroy: function () {
            if ($('.modal-footer .btn-primary').length)
                $('.modal-footer .btn-primary')[0].click();
            this.stop = true;
            Webcam.off('live');
            Webcam.reset();
            this._super.apply(this, arguments);
        },
    });

    var MyAttendances = Attendances.include({
        events: {
            "click .o_hr_attendance_sign_in_out_icon": _.debounce(function () {
                this.update_attendance_with_recognition();
            }, 200, true),
            "click .o_hr_attendance_break_resume_icon": _.debounce(function () {
                this.update_break_resume_with_recognition();
            }, 200, true),
            "click .o_hr_attendance_back_button": _.debounce(function () {
                this.do_action({
                    type: 'ir.actions.client',
                    tag: 'hr_attendance_kiosk_mode',
                });
            }, 200, true)
        },

        // parse data setting from server
        parse_data_face_recognition: function () {
                const self = this;
                const data = this.data;
                self.face_recognition_pro_scale_recognition = data.face_recognition_pro_scale_recognition;
                self.face_recognition_pro_scale_spoofing = data.face_recognition_pro_scale_spoofing;
                self.face_recognition_pro_timeout = data.face_recognition_pro_timeout;
                self.face_recognition_enable = data.face_recognition_enable;
                self.face_recognition_store = data.face_recognition_store;
                self.face_recognition_pro_photo_check = data.face_recognition_pro_photo_check;
                self.face_recognition_pro_backend = data.face_recognition_pro_backend;
                self.face_emotion = data.face_emotion;
                self.face_gender = data.face_gender;
                const age_map = {
                    '20': '0-20',
                    '30': '20-30',
                    '40': '30-40',
                    '50': '40-50',
                    '60': '50-60',
                    '70': '60-any',
                    'any': 'any-any'
                }
                if (data.face_age === 'any')
                    self.face_age = 'any-any';
                else
                    self.face_age = age_map[Math.ceil(data.face_age).toString()];

                if (!self.face_recognition_access)
                    self.face_recognition_access = false;

                self.labels_ids = data.labels_ids;
                self.descriptor_ids = [];
                for (const f32base64 of data.descriptor_ids) {
                    self.descriptor_ids.push(new Float32Array(new Uint8Array([...atob(f32base64)].map(c => c.charCodeAt(0))).buffer))
                }
                self.face_photo = true;
                if (!self.labels_ids.length || !self.descriptor_ids.length)
                    self.face_photo = false;
                self.state_save.resolve();
        },

        load_models: async function () {
            let def = $.Deferred();
            const myConfig = {
                debug: false,
                async: false,
                backend: this.face_recognition_pro_backend || "humangl",
                modelBasePath: '/hr_attendance_face_recognition_pro/static/src/js/models',
                face: { // runs all face models
                    face: { enabled: true },
                    mesh: { enabled: true },
                    antispoof: { enabled: true },
                    description: { enabled: true },

                    detector: { rotation: false },
                    iris: { enabled: false },
                    emotion: { enabled: false },
                },
                hand: { enabled: false },
                body: { enabled: false },
                object: { enabled: false },
                gesture: { enabled: false },
                segmentation: { enabled: false },
                filter: { enabled: false },
            };
            this.human = new Human.Human(myConfig);
            await this.human.load();
            console.log(this.human, 'HUMAN')
            def.resolve();
            return def
        },

        start: function () {
            this.state_read.then(data =>{
                this.parse_data_face_recognition();
                this.promise_face_recognition = this.load_models();
                this.promise_face_recognition.then(
                    result => {
                        this.state_render.then(
                            render => {
                                console.log("models success loaded");
                                if (this.face_photo) {
                                    this.$('.o_form_binary_file_web_cam').removeClass('btn-warning');
                                    this.$('.o_form_binary_file_web_cam').addClass('btn-success');
                                    this.$('.o_form_binary_file_web_cam').text('人脸识别已开启');
                                }
                                else {
                                    this.$('.o_form_binary_file_web_cam').removeClass('btn-warning');
                                    this.$('.o_form_binary_file_web_cam').addClass('btn-danger');
                                    this.$('.o_form_binary_file_web_cam').text('未上传人脸识比对照片');
                                }
                            })
                    })
            })
            return $.when(this._super.apply(this, arguments));
        },

        update_break_resume_with_recognition: function () {
            // if kiosk mode enable, recognition already done
            if (!this.face_recognition_enable || this.kiosk) {
                this.face_recognition_access = true;
                this.update_break_resume();
                return
            }

            this.promise_face_recognition.then(
                result => {
                    if (this.face_photo)
                        new FaceRecognitionDialog(this, {
                            labels_ids: this.labels_ids,
                            descriptor_ids: this.descriptor_ids,
                            break: true
                        }).open();
                    else
                        Swal.fire({
                            title: '没有上传任何图片/照片',
                            text: "请转到您的个人资料并上传1张照片",
                            icon: '错误',
                            confirmButtonColor: '#3085d6',
                            confirmButtonText: 'Ok'
                        });
                },
                error => {
                    console.log(error);
                });
        },

        update_attendance_with_recognition: function () {
            // if kiosk mode enable, recognition already done
            if (!this.face_recognition_enable || this.kiosk) {
                this.face_recognition_access = true;
                this.update_attendance();
                return
            }

            this.promise_face_recognition.then(
                result => {
                    if (this.face_photo)
                        new FaceRecognitionDialog(this, {
                            labels_ids: this.labels_ids,
                            descriptor_ids: this.descriptor_ids
                        }).open();
                    else
                        Swal.fire({
                            title: '没有上传任何图片/照片',
                            text: "请转到您的个人资料并上传1张照片",
                            icon: '错误',
                            confirmButtonColor: '#3085d6',
                            confirmButtonText: 'Ok'
                        });
                },
                error => {
                    console.log(error);
                });
        }

    });
    return { FaceRecognitionDialog: FaceRecognitionDialog }
});