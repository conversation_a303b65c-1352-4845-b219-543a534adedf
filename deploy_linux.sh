#!/bin/bash

# GejieSoft PDF Tools - Linux 自动化部署脚本
# 使用方法: ./deploy_linux.sh

set -e  # 遇到错误时立即退出

echo "===== GejieSoft PDF Tools - Linux 部署脚本 ====="
echo "开始自动化部署..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查是否为root用户
if [[ $EUID -eq 0 ]]; then
   echo -e "${RED}警告: 不建议使用root用户运行此脚本${NC}"
   echo "请使用普通用户运行，脚本会在需要时提示输入sudo密码"
   exit 1
fi

# 检查系统类型
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo -e "${GREEN}✓ 检测到Linux系统${NC}"
else
    echo -e "${RED}✗ 此脚本仅支持Linux系统${NC}"
    exit 1
fi

# 检测发行版
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    VER=$VERSION_ID
    echo -e "${GREEN}✓ 检测到系统: $OS $VER${NC}"
fi

# 函数：检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo -e "${RED}✗ $1 未找到${NC}"
        return 1
    else
        echo -e "${GREEN}✓ $1 已安装${NC}"
        return 0
    fi
}

# 函数：安装依赖
install_dependencies() {
    echo -e "${YELLOW}正在安装系统依赖...${NC}"
    
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        sudo apt update
        sudo apt install -y golang-go python3 python3-pip python3-venv git build-essential curl wget
    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
        sudo yum install -y golang python3 python3-pip git gcc gcc-c++ make curl wget
    elif [[ "$OS" == *"Fedora"* ]]; then
        sudo dnf install -y golang python3 python3-pip git gcc gcc-c++ make curl wget
    else
        echo -e "${YELLOW}未识别的发行版，请手动安装依赖：golang, python3, python3-pip, git, build-essential${NC}"
        read -p "是否继续？(y/n) " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 函数：检查Go版本
check_go_version() {
    if check_command go; then
        GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
        REQUIRED_VERSION="1.24"
        
        if version_ge "$GO_VERSION" "$REQUIRED_VERSION"; then
            echo -e "${GREEN}✓ Go版本符合要求: $GO_VERSION${NC}"
        else
            echo -e "${YELLOW}! Go版本过低 ($GO_VERSION)，需要 >= $REQUIRED_VERSION${NC}"
            echo "正在安装最新版本的Go..."
            install_latest_go
        fi
    else
        echo -e "${RED}✗ Go未安装，正在安装...${NC}"
        install_latest_go
    fi
}

# 函数：版本比较
version_ge() {
    printf '%s\n%s\n' "$2" "$1" | sort -V -C
}

# 函数：安装最新Go
install_latest_go() {
    GO_VERSION="1.24.4"
    GO_TARBALL="go${GO_VERSION}.linux-amd64.tar.gz"
    
    echo "下载Go ${GO_VERSION}..."
    cd /tmp
    wget "https://dl.google.com/go/${GO_TARBALL}"
    
    echo "安装Go..."
    sudo rm -rf /usr/local/go
    sudo tar -C /usr/local -xzf "${GO_TARBALL}"
    
    # 设置环境变量
    if ! grep -q "/usr/local/go/bin" ~/.bashrc; then
        echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
    fi
    
    export PATH=$PATH:/usr/local/go/bin
    
    echo -e "${GREEN}✓ Go ${GO_VERSION} 安装完成${NC}"
    rm -f "${GO_TARBALL}"
}

# 函数：设置Go代理
setup_go_proxy() {
    echo -e "${YELLOW}设置Go模块代理...${NC}"
    export GOPROXY=https://goproxy.cn,direct
    export GO111MODULE=on
    echo 'export GOPROXY=https://goproxy.cn,direct' >> ~/.bashrc
    echo 'export GO111MODULE=on' >> ~/.bashrc
    echo -e "${GREEN}✓ Go代理设置完成${NC}"
}

# 函数：创建Python虚拟环境
setup_python_env() {
    echo -e "${YELLOW}创建Python虚拟环境...${NC}"
    
    if [ ! -d "venv" ]; then
        python3 -m venv venv
    fi
    
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r main/ppt_generator/requirements.txt
    
    echo -e "${GREEN}✓ Python环境设置完成${NC}"
}

# 函数：构建应用
build_application() {
    echo -e "${YELLOW}构建应用...${NC}"
    
    echo "安装Go依赖..."
    go mod tidy
    
    echo "构建二进制文件..."
    cd main
    CGO_ENABLED=0 go build -o ../gejiesoft-pdf-tools
    cd ..
    
    if [ -f "gejiesoft-pdf-tools" ]; then
        chmod +x gejiesoft-pdf-tools
        echo -e "${GREEN}✓ 应用构建完成${NC}"
    else
        echo -e "${RED}✗ 应用构建失败${NC}"
        exit 1
    fi
}

# 函数：配置环境变量
configure_environment() {
    echo -e "${YELLOW}配置环境变量...${NC}"
    
    if [ ! -f ".env" ]; then
        if [ -f "env.example" ]; then
            cp env.example .env
            echo -e "${GREEN}✓ 已复制环境变量模板${NC}"
        else
            echo -e "${RED}✗ 未找到环境变量模板文件${NC}"
            exit 1
        fi
    fi
    
    echo -e "${YELLOW}请编辑 .env 文件，填入必要的API密钥：${NC}"
    echo "  - AIBOT_API_KEY"
    echo "  - TENCENT_SECRET_ID"
    echo "  - TENCENT_SECRET_KEY"
    echo "  - TENCENT_BUCKET"
    echo
    read -p "是否现在编辑环境变量文件？(y/n) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        ${EDITOR:-nano} .env
    fi
}

# 函数：创建系统服务
create_systemd_service() {
    read -p "是否创建systemd服务以便开机自启？(y/n) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        CURRENT_DIR=$(pwd)
        CURRENT_USER=$(whoami)
        
        sudo tee /etc/systemd/system/gejiesoft-pdf-tools.service > /dev/null <<EOF
[Unit]
Description=GejieSoft PDF Tools
After=network.target

[Service]
Type=simple
User=$CURRENT_USER
WorkingDirectory=$CURRENT_DIR
ExecStart=$CURRENT_DIR/gejiesoft-pdf-tools
Restart=always
RestartSec=10
EnvironmentFile=$CURRENT_DIR/.env

[Install]
WantedBy=multi-user.target
EOF
        
        sudo systemctl daemon-reload
        sudo systemctl enable gejiesoft-pdf-tools
        
        echo -e "${GREEN}✓ 系统服务已创建${NC}"
        echo "使用以下命令管理服务："
        echo "  启动: sudo systemctl start gejiesoft-pdf-tools"
        echo "  停止: sudo systemctl stop gejiesoft-pdf-tools"
        echo "  状态: sudo systemctl status gejiesoft-pdf-tools"
        echo "  日志: sudo journalctl -u gejiesoft-pdf-tools -f"
    fi
}

# 函数：配置防火墙
configure_firewall() {
    PORT=$(grep -E "^PORT=" .env 2>/dev/null | cut -d'=' -f2 || echo "8088")
    
    if check_command ufw; then
        read -p "是否配置UFW防火墙以允许端口${PORT}？(y/n) " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            sudo ufw allow ${PORT}/tcp
            sudo ufw reload
            echo -e "${GREEN}✓ UFW防火墙配置完成${NC}"
        fi
    elif check_command firewall-cmd; then
        read -p "是否配置firewalld防火墙以允许端口${PORT}？(y/n) " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            sudo firewall-cmd --permanent --add-port=${PORT}/tcp
            sudo firewall-cmd --reload
            echo -e "${GREEN}✓ firewalld防火墙配置完成${NC}"
        fi
    fi
}

# 函数：启动应用
start_application() {
    echo -e "${YELLOW}启动应用...${NC}"
    
    read -p "是否立即启动应用？(y/n) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if [ -f "/etc/systemd/system/gejiesoft-pdf-tools.service" ]; then
            sudo systemctl start gejiesoft-pdf-tools
            echo -e "${GREEN}✓ 应用已通过systemd启动${NC}"
        else
            echo "直接启动应用..."
            nohup ./gejiesoft-pdf-tools > app.log 2>&1 &
            echo $! > app.pid
            echo -e "${GREEN}✓ 应用已启动（PID: $(cat app.pid)）${NC}"
            echo "日志文件: $(pwd)/app.log"
        fi
        
        PORT=$(grep -E "^PORT=" .env 2>/dev/null | cut -d'=' -f2 || echo "8088")
        echo -e "${GREEN}✓ 应用访问地址: http://localhost:${PORT}${NC}"
    fi
}

# 主函数
main() {
    echo -e "${YELLOW}步骤 1/8: 检查系统环境${NC}"
    check_command curl || install_dependencies
    check_command wget || install_dependencies
    check_command git || install_dependencies
    
    echo -e "${YELLOW}步骤 2/8: 检查Go环境${NC}"
    check_go_version
    
    echo -e "${YELLOW}步骤 3/8: 设置Go代理${NC}"
    setup_go_proxy
    
    echo -e "${YELLOW}步骤 4/8: 检查Python环境${NC}"
    check_command python3 || install_dependencies
    
    echo -e "${YELLOW}步骤 5/8: 设置Python环境${NC}"
    setup_python_env
    
    echo -e "${YELLOW}步骤 6/8: 构建应用${NC}"
    build_application
    
    echo -e "${YELLOW}步骤 7/8: 配置环境${NC}"
    configure_environment
    configure_firewall
    create_systemd_service
    
    echo -e "${YELLOW}步骤 8/8: 启动应用${NC}"
    start_application
    
    echo -e "${GREEN}===== 部署完成！ =====${NC}"
    echo
    echo "后续操作："
    echo "1. 编辑 .env 文件，填入正确的API密钥"
    echo "2. 将PPT模板文件放入 templates-ppt/ 目录"
    echo "3. 访问Web界面进行测试"
    echo
    echo "常用命令："
    echo "  查看日志: tail -f app.log"
    echo "  停止应用: kill \$(cat app.pid)"
    echo "  重启应用: ./gejiesoft-pdf-tools"
}

# 运行主函数
main "$@" 