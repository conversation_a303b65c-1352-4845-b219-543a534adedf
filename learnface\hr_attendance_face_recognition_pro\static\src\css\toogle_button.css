.only-descriptor {
  display: block;
}

.face-recognition-detectors {
  z-index: 99999;
  position: absolute !important;
  width: 600px !important;
  height: 400px !important;
}

.face-recognition-origin {
  width: 600px !important;
  height: 400px !important;
}


.cookiesContent {
  width: 320px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  color: #000;
  text-align: center;
  border-radius: 20px;
  padding: 30px 30px 40px !important;

  font-family: "Roboto", sans-serif;
  padding: 0;
  margin: 0;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cookiesContent button.close {
  width: 30px;
  font-size: 20px;
  color: #c0c5cb;
  align-self: flex-end;
  background-color: transparent;
  border: none;
  margin-bottom: 10px;
}

.cookiesContent img {
  width: 82px;
  margin-bottom: 15px;
}

.cookiesContent p {
  margin-bottom: 40px;
  font-size: 18px;
}

.cookiesContent button.accept {
  background-color: #ed6755;
  border: none;
  border-radius: 5px;
  width: 200px;
  padding: 14px;
  font-size: 16px;
  color: white;
  box-shadow: 0px 6px 18px -5px #ed6755;
}