[Unit]
Description=human
After=network.target network-online.target

[Service]
Type=simple
Environment="NODE_ENV=production"
ExecStart=<path-to-node> <your-project-folder>/node_modules/@vladmandic/build/src/build.js --profile serve
WorkingDirectory=<your-project-folder>
StandardOutput=inherit
StandardError=inherit
Restart=always
RestartSec=300
User=vlado
StandardOutput=null

[Install]
WantedBy=multi-user.target
