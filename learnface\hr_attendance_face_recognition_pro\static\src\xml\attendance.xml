<?xml version="1.0" encoding="utf-8"?>
<template xml:space="preserve">

    <t t-extend="KanbanView.buttons">
        <t t-jquery="button" t-operation="after">
            <t t-if="model == 'res.users.image' || model == 'hr.employee.image'">
                <button class="btn btn-secondary o-kanban-button-hide-face-recognition badge-warning" type="button">
                    <span class="only-descriptor">Hide faces on images</span>
                    <span class="only-descriptor" style="display:none">Show faces on images</span>
                </button>
            </t>
        </t>
    </t>

    <t t-extend="HrAttendanceMyMainMenu">
        <t t-jquery="h3:first" t-operation="after">
            <t t-if="widget.kiosk">
                <div class="o_hr_attendance_back_button">
                    <span class="btn btn-secondary btn-lg d-block d-md-none">
                        <i class="fa fa-chevron-left mr8"/>
 Go back</span>
                    <span class="btn btn-secondary d-none d-md-inline-block">
                        <i class="fa fa-chevron-left" role="img" aria-label="Go back" title="Go back"/>
                    </span>
                </div>
            </t>
            <span class="btn btn-warning fa-lg float-center o_form_binary_file_web_cam" title="Face recognition" aria-label="Face recognition" style="position:absolute;top:0;left:0;">
                <span>
                    <i class="fa fa-camera"/>
                </span>
                    人脸识别程序加载中...
            </span>
        </t>
    </t>
    <div t-name="WebCamDialogFaceRecognition" id="WebCamDialogFaceRecognition">
        <div style="display: flex; justify-content: center; align-content: space-between;  align-items: center;flex-direction: column;">
            <div id="live_webcam" style="display:none"/>
            <canvas id="ocr_canvas"></canvas>
        </div>
    </div>
    <t t-name="ImageRecognition-img">
        <button class="btn btn-secondary o-kanban-button-hide-face-recognition badge-warning" type="button" style="position:absolute">
            <span class="only-descriptor">Hide faces on images</span>
            <span class="only-descriptor" style="display:none">Show faces on images</span>
        </button>
        <img style="border-radius: 45px;" class="img" t-att-width="face_width" t-att-height="face_height" alt="Binary file" t-att-src='url' t-att-border="widget.readonly ? 0 : 1" t-att-name="widget.name"/>
    </t>

</template>