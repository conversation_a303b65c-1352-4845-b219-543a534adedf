• 项目概述：本程序是一个智能的PPT信息提取与**生成**工具，支持文本提取、图片自动提取、人像识别与抠图、照片矫正优化等功能，并能基于数据生成定制化的PPT幻灯片。
• 环境要求 Go 1.24+ (本地运行)、Docker & Docker Compose (容器化部署)、Windows/Linux/macOS 支持
• 技术栈：列出主要的编程语言、框架和库。

根目录为程序的起点
main 为核心模块程序，里面包含了一些子模块

注意事项：
1、我们基于windows环境开发
2、PowerShell不支持&&语法，需要分别执行命令
3、每个功能性的动作，前端必须能显示日志。
4、安装依赖包，使用清华源会更快一点



主程序测试方法：
1、构建主程序在根目录执行  ./main/build.bat
2、运行主程序在根目录执行  ./gejiesoft-pdf-tools.exe
3、如果同时构建和执行 .\main\build.bat ; .\gejiesoft-pdf-tools.exe



主程序用于PPT的预置母板：
存放路径：/templates-ppt/ppt-teample1.pptx  ppt-teample2.pptx ppt-teample3.pptx 依次类推
