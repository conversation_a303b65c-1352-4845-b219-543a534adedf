<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量头像处理系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .main-content {
            padding: 40px;
        }
        
        .control-panel {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            align-items: center;
        }
        
        .control-group {
            text-align: center;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
            color: #333;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        }
        
        .progress-section {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            display: none;
        }
        
        .overall-progress {
            margin-bottom: 20px;
        }
        
        .progress-container {
            background: #e9ecef;
            border-radius: 25px;
            padding: 5px;
            margin: 10px 0;
        }
        
        .progress-bar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 30px;
            border-radius: 20px;
            width: 0%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .current-processing {
            background: #e8f4fd;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .result-item {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .result-item:hover {
            transform: translateY(-5px);
        }
        
        .result-item.success {
            border-left: 5px solid #28a745;
        }
        
        .result-item.error {
            border-left: 5px solid #dc3545;
        }
        
        .result-item img {
            max-width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 10px;
            margin-bottom: 10px;
        }
        
        .result-item h4 {
            margin: 10px 0 5px 0;
            font-size: 0.9em;
            color: #333;
            word-break: break-all;
        }
        
        .result-item .status {
            font-size: 0.8em;
            padding: 5px 10px;
            border-radius: 15px;
            margin: 5px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .statistics {
            background: #e8f4fd;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .stat-item {
            text-align: center;
            background: white;
            border-radius: 10px;
            padding: 20px;
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .download-all {
            text-align: center;
            margin-top: 30px;
        }
        
        #fileInput {
            display: none;
        }
        
        .status-message {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
            font-weight: bold;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 批量头像处理系统</h1>
            <p>基于Human.js的智能批量人脸识别与头像生成</p>
        </div>
        
        <div class="main-content">
            <!-- 控制面板 -->
            <div class="control-panel">
                <div class="control-group">
                    <label>📁 选择照片文件夹</label>
                    <button class="btn" onclick="selectFolder()">选择文件夹</button>
                    <input type="file" id="fileInput" webkitdirectory multiple accept="image/*">
                </div>
                
                <div class="control-group">
                    <label>⚙️ 处理控制</label>
                    <button class="btn" id="startBtn" onclick="startBatchProcessing()" disabled>开始处理</button>
                    <button class="btn btn-danger" id="stopBtn" onclick="stopProcessing()" disabled>停止处理</button>
                </div>
                
                <div class="control-group">
                    <label>💾 结果下载</label>
                    <button class="btn btn-secondary" id="downloadBtn" onclick="downloadAllResults()" disabled>下载全部</button>
                </div>
            </div>
            
            <!-- 状态消息 -->
            <div id="statusMessage"></div>
            
            <!-- 进度显示 -->
            <div class="progress-section" id="progressSection">
                <div class="overall-progress">
                    <h3>📊 整体进度</h3>
                    <div class="progress-container">
                        <div class="progress-bar" id="overallProgress">0%</div>
                    </div>
                    <p id="progressText">准备开始...</p>
                </div>
                
                <div class="current-processing" id="currentProcessing">
                    <h4>🔄 当前处理</h4>
                    <p id="currentFile">无</p>
                    <div class="progress-container">
                        <div class="progress-bar" id="currentProgress">0%</div>
                    </div>
                </div>
            </div>
            
            <!-- 统计信息 -->
            <div class="statistics" id="statistics" style="display: none;">
                <div class="stat-item">
                    <div class="stat-number" id="totalFiles">0</div>
                    <div class="stat-label">总文件数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="processedFiles">0</div>
                    <div class="stat-label">已处理</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="successFiles">0</div>
                    <div class="stat-label">成功</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="errorFiles">0</div>
                    <div class="stat-label">失败</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="processingTime">0</div>
                    <div class="stat-label">处理时间(秒)</div>
                </div>
            </div>
            
            <!-- 结果展示 -->
            <div class="results-grid" id="resultsGrid"></div>
            
            <!-- 下载全部按钮 -->
            <div class="download-all" id="downloadSection" style="display: none;">
                <button class="btn btn-secondary" onclick="downloadAllResults()">
                    📦 打包下载所有头像
                </button>
            </div>
        </div>
    </div>

    <!-- 配置文件 -->
    <script src="config.js"></script>

    <!-- Human.js 库 -->
    <script src="learnface/hr_attendance_face_recognition_pro/static/src/js/lib/human.js"></script>
    
    <script>
        // 全局变量
        let human = null;
        let selectedFiles = [];
        let processedResults = [];
        let isProcessing = false;
        let processingStats = {
            total: 0,
            processed: 0,
            success: 0,
            error: 0,
            startTime: null
        };
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeHuman();
            setupEventListeners();
        });
        
        // 设置事件监听器
        function setupEventListeners() {
            const fileInput = document.getElementById('fileInput');
            fileInput.addEventListener('change', handleFolderSelect);
        }
        
        // 初始化Human.js
        async function initializeHuman() {
            try {
                showStatus('正在加载Human.js模型...', 'warning');
                
                // 使用配置文件中的设置
                const config = getConfig('human');
                
                human = new Human.Human(config);
                await human.load();
                
                showStatus('Human.js模型加载完成，可以开始批量处理！', 'success');
                
            } catch (error) {
                console.error('Human.js初始化失败:', error);
                showStatus('模型加载失败: ' + error.message, 'error');
            }
        }

        // 选择文件夹
        function selectFolder() {
            document.getElementById('fileInput').click();
        }

        // 处理文件夹选择
        function handleFolderSelect(event) {
            const files = Array.from(event.target.files).filter(file =>
                file.type.startsWith('image/')
            );

            if (files.length === 0) {
                showStatus('所选文件夹中没有找到图像文件', 'warning');
                return;
            }

            selectedFiles = files;
            processingStats.total = files.length;

            // 更新UI
            document.getElementById('startBtn').disabled = false;
            document.getElementById('totalFiles').textContent = files.length;
            document.getElementById('statistics').style.display = 'grid';

            showStatus(`已选择 ${files.length} 个图像文件，可以开始批量处理`, 'success');
        }

        // 开始批量处理
        async function startBatchProcessing() {
            if (!human) {
                showStatus('Human.js模型尚未加载完成', 'error');
                return;
            }

            if (selectedFiles.length === 0) {
                showStatus('请先选择要处理的文件夹', 'warning');
                return;
            }

            isProcessing = true;
            processingStats.startTime = Date.now();
            processingStats.processed = 0;
            processingStats.success = 0;
            processingStats.error = 0;
            processedResults = [];

            // 更新UI
            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            document.getElementById('progressSection').style.display = 'block';
            document.getElementById('resultsGrid').innerHTML = '';

            showStatus('开始批量处理...', 'warning');

            // 逐个处理文件
            for (let i = 0; i < selectedFiles.length && isProcessing; i++) {
                const file = selectedFiles[i];
                await processFileInBatch(file, i + 1);

                // 更新整体进度
                const overallProgress = Math.round(((i + 1) / selectedFiles.length) * 100);
                updateOverallProgress(overallProgress, `已处理 ${i + 1}/${selectedFiles.length} 个文件`);

                // 短暂延迟，避免浏览器卡顿
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            // 处理完成
            finishBatchProcessing();
        }

        // 处理单个文件
        async function processFileInBatch(file, index) {
            const fileName = file.name;
            updateCurrentProcessing(fileName, 0);

            try {
                // 读取图像
                updateCurrentProcessing(fileName, 20);
                const imageData = await readImageFile(file);

                // 人脸检测
                updateCurrentProcessing(fileName, 40);
                const result = await human.detect(imageData.canvas);

                if (!result.face || result.face.length === 0) {
                    throw new Error('未检测到人脸');
                }

                // 生成头像
                updateCurrentProcessing(fileName, 60);
                const avatar = await generateStandardAvatar(imageData, result.face[0]);

                // 背景移除
                updateCurrentProcessing(fileName, 80);
                const finalAvatar = await removeBackground(avatar, result.face[0]);

                // 准备保存数据（不立即下载，避免干扰批量处理）
                updateCurrentProcessing(fileName, 90);

                updateCurrentProcessing(fileName, 100);

                // 保存结果
                const resultData = {
                    fileName: fileName,
                    originalImage: imageData.canvas.toDataURL(),
                    avatarImage: finalAvatar.toDataURL('image/png'),
                    faceData: result.face[0],
                    status: 'success'
                };

                processedResults.push(resultData);
                processingStats.success++;

                // 显示结果
                displayResultItem(resultData);

            } catch (error) {
                console.error(`处理文件 ${fileName} 失败:`, error);

                const resultData = {
                    fileName: fileName,
                    error: error.message,
                    status: 'error'
                };

                processedResults.push(resultData);
                processingStats.error++;

                // 显示错误结果
                displayResultItem(resultData);
            }

            processingStats.processed++;
            updateStatistics();
        }

        // 停止处理
        function stopProcessing() {
            isProcessing = false;
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            showStatus('处理已停止', 'warning');
        }

        // 完成批量处理
        function finishBatchProcessing() {
            isProcessing = false;
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('progressSection').style.display = 'none';

            if (processingStats.success > 0) {
                document.getElementById('downloadBtn').disabled = false;
                document.getElementById('downloadSection').style.display = 'block';
            }

            const successRate = Math.round((processingStats.success / processingStats.total) * 100);
            showStatus(`批量处理完成！成功率: ${successRate}% (${processingStats.success}/${processingStats.total})`, 'success');
        }

        // 读取图像文件
        function readImageFile(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = new Image();
                    img.onload = function() {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        canvas.width = img.width;
                        canvas.height = img.height;
                        ctx.drawImage(img, 0, 0);

                        resolve({
                            canvas: canvas,
                            image: img,
                            width: img.width,
                            height: img.height
                        });
                    };
                    img.onerror = reject;
                    img.src = e.target.result;
                };
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }

        // 生成标准头像 - 基于面部对称轴的精确居中算法
        async function generateStandardAvatar(imageData, face) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            const avatarSize = 512;
            canvas.width = avatarSize;
            canvas.height = avatarSize;

            // 基于面部特征的标准化裁切算法
            let faceSymmetryX, eyeCenterY, mouthCenterY, eyeDistance, faceHeight;
            let cropSize, cropX, cropY;

            if (face.mesh && face.mesh.length >= 468) {
                // 获取关键面部特征点
                const leftEyeInner = face.mesh[133];   // 左眼内角
                const leftEyeOuter = face.mesh[33];    // 左眼外角
                const rightEyeInner = face.mesh[362];  // 右眼内角
                const rightEyeOuter = face.mesh[263];  // 右眼外角

                // 鼻子和嘴巴关键点（常用索引）
                const noseTip = face.mesh[1] || face.mesh[2];           // 鼻尖
                const mouthLeft = face.mesh[61];                        // 左嘴角
                const mouthRight = face.mesh[291];                      // 右嘴角
                const upperLip = face.mesh[13];                         // 上唇中心
                const lowerLip = face.mesh[14];                         // 下唇中心

                if (leftEyeInner && leftEyeOuter && rightEyeInner && rightEyeOuter) {
                    // 计算眼睛中心点
                    const leftEyeCenterX = (leftEyeInner[0] + leftEyeOuter[0]) / 2;
                    const leftEyeCenterY = (leftEyeInner[1] + leftEyeOuter[1]) / 2;
                    const rightEyeCenterX = (rightEyeInner[0] + rightEyeOuter[0]) / 2;
                    const rightEyeCenterY = (rightEyeInner[1] + rightEyeOuter[1]) / 2;

                    // 面部对称轴
                    faceSymmetryX = (leftEyeCenterX + rightEyeCenterX) / 2;
                    eyeCenterY = (leftEyeCenterY + rightEyeCenterY) / 2;

                    // 计算两眼距离
                    eyeDistance = Math.sqrt(
                        Math.pow(rightEyeCenterX - leftEyeCenterX, 2) +
                        Math.pow(rightEyeCenterY - leftEyeCenterY, 2)
                    );

                    // 计算嘴巴中心
                    if (mouthLeft && mouthRight) {
                        mouthCenterY = (mouthLeft[1] + mouthRight[1]) / 2;
                    } else if (upperLip && lowerLip) {
                        mouthCenterY = (upperLip[1] + lowerLip[1]) / 2;
                    } else {
                        mouthCenterY = eyeCenterY + eyeDistance * 1.2; // 估算
                    }

                    // 计算面部高度（眼睛到嘴巴的距离）
                    faceHeight = Math.abs(mouthCenterY - eyeCenterY);
                } else {
                    // 备用方案：使用人脸框
                    faceSymmetryX = face.box[0] + face.box[2] / 2;
                    eyeCenterY = face.box[1] + face.box[3] * 0.4;
                    mouthCenterY = face.box[1] + face.box[3] * 0.7;
                    eyeDistance = face.box[2] * 0.3;
                    faceHeight = face.box[3] * 0.3;
                }
            } else {
                // 备用方案：使用人脸框
                faceSymmetryX = face.box[0] + face.box[2] / 2;
                eyeCenterY = face.box[1] + face.box[3] * 0.4;
                mouthCenterY = face.box[1] + face.box[3] * 0.7;
                eyeDistance = face.box[2] * 0.3;
                faceHeight = face.box[3] * 0.3;
            }

            // 标准化裁切算法：基于面部特征确定裁切大小和位置

            // 定义标准比例（针对扩大30%的裁切范围重新优化）
            const standardRatios = {
                eyeDistanceRatio: 0.19,    // 两眼距离占头像宽度的19%（因为裁切范围扩大30%）
                eyePositionY: 0.40,        // 眼睛位置在头像高度的40%处
                mouthPositionY: 0.65,      // 嘴巴位置在头像高度的65%处
                faceHeightRatio: 0.25      // 眼到嘴距离占头像高度的25%
            };

            // 方法1：基于两眼距离确定裁切大小
            const cropSizeByEyeDistance = eyeDistance / standardRatios.eyeDistanceRatio;

            // 方法2：基于面部高度确定裁切大小
            const cropSizeByFaceHeight = faceHeight / standardRatios.faceHeightRatio;

            // 取两种方法的平均值，确保面部特征都能合适地放置
            cropSize = (cropSizeByEyeDistance + cropSizeByFaceHeight) / 2;

            // 智能头顶空间检测
            const faceBoxTop = face.box[1];
            const eyeToFaceTop = eyeCenterY - faceBoxTop;
            const estimatedHeadTopSpace = eyeToFaceTop * 1.3; // 估算需要的头顶空间

            // 基于头顶空间调整裁切大小
            const minCropSizeForHead = estimatedHeadTopSpace / (1 - standardRatios.eyePositionY);

            // 确保裁切大小合理（不要太大或太小）
            const minCropSize = Math.max(eyeDistance * 3, faceHeight * 2.5, minCropSizeForHead);
            const maxCropSize = Math.min(imageData.width, imageData.height) * 0.9;
            let baseCropSize = Math.max(minCropSize, Math.min(cropSize, maxCropSize));

            // 扩大裁切范围：上下左右各扩15%，总体放大30%
            const expansionFactor = 1.3; // 扩大30%（上下左右各15%）
            cropSize = baseCropSize * expansionFactor;

            // 重新检查最大尺寸限制
            const finalMaxCropSize = Math.min(imageData.width, imageData.height) * 0.95;
            cropSize = Math.min(cropSize, finalMaxCropSize);

            // 基于标准位置计算裁切坐标
            cropX = faceSymmetryX - cropSize / 2;  // 水平居中

            // 垂直位置：让眼睛位于标准位置
            const targetEyeY = cropSize * standardRatios.eyePositionY;
            cropY = eyeCenterY - targetEyeY;

            // 创建扩展canvas以支持超出边界的裁切（允许空白，确保居中）
            const extendedSize = Math.max(imageData.width, imageData.height) + cropSize;
            const extendedCanvas = document.createElement('canvas');
            const extendedCtx = extendedCanvas.getContext('2d');
            extendedCanvas.width = extendedSize;
            extendedCanvas.height = extendedSize;

            // 将原图绘制到扩展canvas的中心
            const offsetX = (extendedSize - imageData.width) / 2;
            const offsetY = (extendedSize - imageData.height) / 2;
            extendedCtx.drawImage(imageData.canvas, offsetX, offsetY);

            // 调整裁切坐标到扩展canvas坐标系
            const adjustedCropX = cropX + offsetX;
            const adjustedCropY = cropY + offsetY;

            // 从扩展canvas裁切图像（确保对称轴居中）
            ctx.drawImage(
                extendedCanvas,
                adjustedCropX, adjustedCropY, cropSize, cropSize,
                0, 0, avatarSize, avatarSize
            );

            return canvas;
        }

        // 背景移除 - 修改为正方形1:1输出，不做圆形虚化
        async function removeBackground(avatarCanvas, face) {
            // 直接返回正方形头像，不做圆形处理
            return avatarCanvas;
        }



        // 显示单个结果项
        function displayResultItem(resultData) {
            const resultsGrid = document.getElementById('resultsGrid');
            const resultItem = document.createElement('div');
            resultItem.className = `result-item ${resultData.status}`;

            if (resultData.status === 'success') {
                resultItem.innerHTML = `
                    <img src="${resultData.avatarImage}" alt="头像">
                    <h4>${resultData.fileName}</h4>
                    <div class="status success">✅ 处理成功</div>
                    <button class="btn" onclick="downloadSingleResult('${resultData.fileName}')">下载</button>
                `;
            } else {
                resultItem.innerHTML = `
                    <div style="height: 150px; background: #f8d7da; display: flex; align-items: center; justify-content: center; border-radius: 10px; margin-bottom: 10px;">
                        <span style="font-size: 2em;">❌</span>
                    </div>
                    <h4>${resultData.fileName}</h4>
                    <div class="status error">❌ ${resultData.error}</div>
                `;
            }

            resultsGrid.appendChild(resultItem);
        }

        // 更新整体进度
        function updateOverallProgress(percent, text) {
            const progressBar = document.getElementById('overallProgress');
            const progressText = document.getElementById('progressText');

            progressBar.style.width = percent + '%';
            progressBar.textContent = percent + '%';
            progressText.textContent = text;
        }

        // 更新当前处理进度
        function updateCurrentProcessing(fileName, percent) {
            const currentFile = document.getElementById('currentFile');
            const currentProgress = document.getElementById('currentProgress');

            currentFile.textContent = fileName;
            currentProgress.style.width = percent + '%';
            currentProgress.textContent = percent + '%';
        }

        // 更新统计信息
        function updateStatistics() {
            document.getElementById('processedFiles').textContent = processingStats.processed;
            document.getElementById('successFiles').textContent = processingStats.success;
            document.getElementById('errorFiles').textContent = processingStats.error;

            if (processingStats.startTime) {
                const elapsed = Math.round((Date.now() - processingStats.startTime) / 1000);
                document.getElementById('processingTime').textContent = elapsed;
            }
        }

        // 下载单个结果
        function downloadSingleResult(fileName) {
            const result = processedResults.find(r => r.fileName === fileName);
            if (result && result.avatarImage) {
                const link = document.createElement('a');
                link.download = 'avatar_' + fileName.replace(/\.[^/.]+$/, '') + '.png';
                link.href = result.avatarImage;
                link.click();
            }
        }

        // 下载所有结果到phototemple-output文件夹
        function downloadAllResults() {
            const successResults = processedResults.filter(r => r.status === 'success');

            if (successResults.length === 0) {
                showStatus('没有成功处理的头像可供下载', 'warning');
                return;
            }

            // 提示用户创建phototemple-output文件夹
            const message = `即将下载 ${successResults.length} 个头像文件。\n\n建议操作：\n1. 在项目文件夹中创建 "phototemple-output" 文件夹\n2. 选择该文件夹作为保存位置\n3. 文件将以 "原文件名_avatar.png" 格式保存\n\n点击确定开始下载`;

            if (!confirm(message)) {
                return;
            }

            // 批量下载，文件名格式：原文件名_avatar.png
            successResults.forEach((result, index) => {
                const nameWithoutExt = result.fileName.replace(/\.[^/.]+$/, "");
                const outputFileName = `${nameWithoutExt}_avatar.png`;

                setTimeout(() => {
                    const link = document.createElement('a');
                    link.download = outputFileName;
                    link.href = result.avatarImage;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }, index * 300); // 300ms间隔，避免浏览器阻止
            });

            showStatus(`开始下载 ${successResults.length} 个头像文件，请选择 phototemple-output 文件夹保存`, 'success');
        }

        // 显示状态消息
        function showStatus(message, type) {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.innerHTML = `<div class="status-${type}">${message}</div>`;

            if (type === 'success') {
                setTimeout(() => {
                    statusDiv.innerHTML = '';
                }, 5000);
            }
        }
    </script>
</body>
</html>
