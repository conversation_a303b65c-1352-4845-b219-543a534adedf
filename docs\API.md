# API 文档

GejieSoft PDF Tools 提供了一系列 HTTP API 接口，用于 PPT 文件处理和管理。

## 📋 目录

- [基础信息](#基础信息)
- [认证说明](#认证说明)
- [API 接口](#api-接口)
- [数据结构](#数据结构)
- [错误码](#错误码)
- [示例代码](#示例代码)

## 🌐 基础信息

### 基础URL
```
http://localhost:8088
```

### 请求格式
- **Content-Type**: `application/json` (JSON数据)
- **Content-Type**: `multipart/form-data` (文件上传)

### 响应格式
所有API响应都使用JSON格式：

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 🔐 认证说明

目前API不需要认证，但建议在生产环境中实施适当的认证机制。

## 📡 API 接口

### 1. 文件上传和处理

#### 上传PPT文件
```http
POST /api/upload
Content-Type: multipart/form-data
```

**请求参数：**
- `file`: PPT文件 (required)
- `options`: 处理选项 (optional)

**响应示例：**
```json
{
  "success": true,
  "data": {
    "task_id": "task_123456",
    "filename": "example.pptx",
    "status": "processing"
  },
  "message": "文件上传成功，开始处理"
}
```

#### 查询处理状态
```http
GET /api/status/{task_id}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "task_id": "task_123456",
    "status": "completed",
    "progress": 100,
    "result": {
      "extracted_text": "...",
      "images_processed": 5,
      "portraits_detected": 2,
      "json_file": "/output/result.json",
      "generated_ppt": "/output/generated.pptx"
    }
  }
}
```

### 2. 文件下载

#### 下载JSON结果
```http
GET /api/download-json/{filename}
```

#### 下载生成的PPT
```http
GET /api/download-ppt/{filename}
```

#### 下载处理后的图片
```http
GET /api/download-image/{filename}
```

### 3. 系统信息

#### 获取系统状态
```http
GET /api/health
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "uptime": "2h30m",
    "memory_usage": "245MB",
    "active_tasks": 2
  }
}
```

#### 获取配置信息
```http
GET /api/config
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "supported_formats": ["pptx"],
    "max_file_size": "100MB",
    "max_concurrent_tasks": 10,
    "features": {
      "image_processing": true,
      "ai_extraction": true,
      "ppt_generation": true
    }
  }
}
```

### 4. 任务管理

#### 获取任务列表
```http
GET /api/tasks
```

#### 取消任务
```http
DELETE /api/tasks/{task_id}
```

#### 清理完成的任务
```http
POST /api/tasks/cleanup
```

## 📊 数据结构

### ResumeInfo
```json
{
  "姓名": "张三",
  "医院": "北京协和医院",
  "科室": "心内科",
  "职称": "主任医师",
  "介绍": "详细的个人介绍...",
  "头像": "/output/portrait.png",
  "职务职称": "主任医师、教授",
  "学术任职": "中华医学会心血管病学分会委员",
  "专业擅长": "心血管疾病诊治",
  "学术成果": "发表论文50余篇",
  "其他": "其他相关信息"
}
```

### ProcessingStats
```json
{
  "total_images": 10,
  "processed_images": 10,
  "portraits_detected": 3,
  "non_portraits": 7,
  "aibot_calls": 1,
  "successful_aibot": 1,
  "failed_aibot": 0,
  "total_processing_time": "2m30s",
  "errors": [],
  "generated_ppt_path": "/output/generated.pptx",
  "generated_json_path": "/output/result.json"
}
```

### TaskStatus
```json
{
  "task_id": "task_123456",
  "filename": "example.pptx",
  "status": "processing|completed|failed",
  "progress": 75,
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:32:30Z",
  "error_message": "错误信息（如果有）"
}
```

## ⚠️ 错误码

| 错误码 | 描述 | 说明 |
|--------|------|------|
| 400 | Bad Request | 请求参数错误 |
| 404 | Not Found | 资源不存在 |
| 413 | Payload Too Large | 文件过大 |
| 415 | Unsupported Media Type | 不支持的文件格式 |
| 500 | Internal Server Error | 服务器内部错误 |
| 503 | Service Unavailable | 服务不可用 |

### 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": 400,
    "message": "文件格式不支持",
    "details": "仅支持 .pptx 格式的文件"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 💡 示例代码

### JavaScript (前端)
```javascript
// 上传文件
async function uploadPPT(file) {
  const formData = new FormData();
  formData.append('file', file);
  
  try {
    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData
    });
    
    const result = await response.json();
    if (result.success) {
      console.log('上传成功:', result.data);
      return result.data.task_id;
    } else {
      throw new Error(result.error.message);
    }
  } catch (error) {
    console.error('上传失败:', error);
    throw error;
  }
}

// 查询处理状态
async function checkStatus(taskId) {
  try {
    const response = await fetch(`/api/status/${taskId}`);
    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('状态查询失败:', error);
    throw error;
  }
}
```

### Python (后端调用)
```python
import requests
import time

def upload_and_process_ppt(file_path):
    """上传PPT文件并等待处理完成"""
    
    # 上传文件
    with open(file_path, 'rb') as f:
        files = {'file': f}
        response = requests.post('http://localhost:8088/api/upload', files=files)
        
    if response.status_code == 200:
        result = response.json()
        task_id = result['data']['task_id']
        
        # 轮询状态
        while True:
            status_response = requests.get(f'http://localhost:8088/api/status/{task_id}')
            status_data = status_response.json()['data']
            
            if status_data['status'] == 'completed':
                return status_data['result']
            elif status_data['status'] == 'failed':
                raise Exception(f"处理失败: {status_data.get('error_message', '未知错误')}")
            
            time.sleep(2)  # 等待2秒后重试
    else:
        raise Exception(f"上传失败: {response.status_code}")

# 使用示例
try:
    result = upload_and_process_ppt('example.pptx')
    print(f"处理完成: {result}")
except Exception as e:
    print(f"错误: {e}")
```

### Go (客户端)
```go
package main

import (
    "bytes"
    "encoding/json"
    "fmt"
    "io"
    "mime/multipart"
    "net/http"
    "os"
    "time"
)

type APIResponse struct {
    Success bool        `json:"success"`
    Data    interface{} `json:"data"`
    Message string      `json:"message"`
    Error   *APIError   `json:"error,omitempty"`
}

type APIError struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Details string `json:"details"`
}

func uploadPPT(filePath string) (string, error) {
    file, err := os.Open(filePath)
    if err != nil {
        return "", err
    }
    defer file.Close()

    body := &bytes.Buffer{}
    writer := multipart.NewWriter(body)
    part, err := writer.CreateFormFile("file", filePath)
    if err != nil {
        return "", err
    }

    _, err = io.Copy(part, file)
    if err != nil {
        return "", err
    }

    err = writer.Close()
    if err != nil {
        return "", err
    }

    req, err := http.NewRequest("POST", "http://localhost:8088/api/upload", body)
    if err != nil {
        return "", err
    }
    req.Header.Set("Content-Type", writer.FormDataContentType())

    client := &http.Client{}
    resp, err := client.Do(req)
    if err != nil {
        return "", err
    }
    defer resp.Body.Close()

    var result APIResponse
    if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
        return "", err
    }

    if !result.Success {
        return "", fmt.Errorf("上传失败: %s", result.Error.Message)
    }

    data := result.Data.(map[string]interface{})
    return data["task_id"].(string), nil
}

func main() {
    taskId, err := uploadPPT("example.pptx")
    if err != nil {
        fmt.Printf("上传失败: %v\n", err)
        return
    }
    
    fmt.Printf("任务ID: %s\n", taskId)
}
```

## 🔧 配置说明

### 环境变量
- `PORT`: 服务器端口 (默认: 8088)
- `MAX_FILE_SIZE`: 最大文件大小 (默认: 100MB)
- `MAX_CONCURRENT_TASKS`: 最大并发任务数 (默认: 10)
- `TIMEOUT`: 请求超时时间 (默认: 300s)

### 限制说明
- 支持的文件格式：`.pptx`
- 最大文件大小：100MB
- 最大并发任务数：10个
- 单个任务超时时间：300秒

---

更多详细信息请参考 [项目文档](../README.md) 或联系技术支持。 