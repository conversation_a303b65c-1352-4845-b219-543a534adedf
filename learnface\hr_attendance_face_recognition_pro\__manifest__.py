# -*- coding: utf-8 -*-

{
    "name": "HR考勤人脸识别-专业版",
    "summary": """
人脸识别签入/签出是一种能够识别或验证一个人的技术吗
来自数字图像或来自视频源的视频帧
人脸检测人脸匹配人脸识别人脸匹配人脸比较
人脸识别人脸识别
考勤人脸考勤人脸考勤人识别人记录""",
    'author': "GejieSoft Inc",
    'website': "https://www.gejiesoft.com",
    "category": "人力资源",
    "version": "********",
    "license": "LGPL-3",
    "images": [
        "static/description/preview.gif",
        "static/description/face_control.png",
        "static/description/face_control.png",
        "static/description/face_control.png",
    ],
    # 此模块正常工作所需的前置模块
    "depends": [
        "base",
        "web",
        "hr_attendance_base",
        "web_image_webcam",
        "field_image_editor",
    ],
    # 始终加载
    "data": [
        "security/ir.model.access.csv",
        "views/views.xml",
        "views/res_users.xml",
        "views/hr_employee.xml",
        "views/res_config_settings_views.xml",
    ],
    "assets": {
        "web.assets_backend": [
            "hr_attendance_face_recognition_pro/static/src/css/toogle_button.css",
            "hr_attendance_face_recognition_pro/static/src/js/lib/webcam.js",
            "hr_attendance_face_recognition_pro/static/src/js/lib/human.js",
            "hr_attendance_face_recognition_pro/static/src/js/widget_image_recognition.js",
            "hr_attendance_face_recognition_pro/static/src/js/res_users_kanban_face_recognition.js",
            "hr_attendance_face_recognition_pro/static/src/js/my_attendances_face_recognition.js",
            "hr_attendance_face_recognition_pro/static/src/js/kiosk_mode_face_recognition.js",
            "hr_attendance_face_recognition_pro/static/src/xml/attendance.xml",
            # "hr_attendance_face_recognition_pro/static/src/xml/kiosk.xml",
        ],
    },
    "cloc_exclude": [
        "static/src/js/lib/**/*",  # 排除单个文件夹
    ],
}
