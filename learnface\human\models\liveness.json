{"format": "graph-model", "generatedBy": "https://github.com/leokwu/livenessnet", "convertedBy": "https://github.com/vladmandic", "signature": {"inputs": {"conv2d_1_input": {"name": "conv2d_1_input:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "32"}, {"size": "32"}, {"size": "3"}]}}}, "outputs": {"activation_6": {"name": "Identity:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "2"}]}}}}, "modelTopology": {"node": [{"name": "StatefulPartitionedCall/sequential_1/conv2d_1/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "3"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_1/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_2/Conv2D/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "16"}, {"size": "16"}]}}}}}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_2/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_3/Conv2D/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "16"}, {"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_3/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_4/Conv2D/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_4/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/flatten_1/Const", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_1/dense_1/MatMul/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "2048"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/dense_1/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_5/batchnorm/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_5/batchnorm/sub", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/sequential_1/dense_2/MatMul/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}, {"size": "2"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/dense_2/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "conv2d_1_input", "op": "Placeholder", "attr": {"shape": {"shape": {"dim": [{"size": "-1"}, {"size": "32"}, {"size": "32"}, {"size": "3"}]}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_2/FusedBatchNormV3/Scaled", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_2/FusedBatchNormV3/Offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_1/FusedBatchNormV3/Scaled", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_1/FusedBatchNormV3/Offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_4/FusedBatchNormV3/Scaled", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_4/FusedBatchNormV3/Offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_3/FusedBatchNormV3/Scaled", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_3/FusedBatchNormV3/Offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/sequential_1/activation_1/Relu", "op": "_FusedConv2D", "input": ["conv2d_1_input", "StatefulPartitionedCall/sequential_1/conv2d_1/Conv2D/ReadVariableOp", "StatefulPartitionedCall/sequential_1/conv2d_1/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_1/FusedBatchNormV3/Mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/sequential_1/activation_1/Relu", "StatefulPartitionedCall/sequential_1/batch_normalization_1/FusedBatchNormV3/Scaled"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_1/FusedBatchNormV3", "op": "Add", "input": ["StatefulPartitionedCall/sequential_1/batch_normalization_1/FusedBatchNormV3/Mul", "StatefulPartitionedCall/sequential_1/batch_normalization_1/FusedBatchNormV3/Offset"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/activation_2/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/sequential_1/batch_normalization_1/FusedBatchNormV3", "StatefulPartitionedCall/sequential_1/conv2d_2/Conv2D/ReadVariableOp", "StatefulPartitionedCall/sequential_1/conv2d_2/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_2/FusedBatchNormV3/Mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/sequential_1/activation_2/Relu", "StatefulPartitionedCall/sequential_1/batch_normalization_2/FusedBatchNormV3/Scaled"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_2/FusedBatchNormV3", "op": "Add", "input": ["StatefulPartitionedCall/sequential_1/batch_normalization_2/FusedBatchNormV3/Mul", "StatefulPartitionedCall/sequential_1/batch_normalization_2/FusedBatchNormV3/Offset"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/max_pooling2d_1/MaxPool", "op": "MaxPool", "input": ["StatefulPartitionedCall/sequential_1/batch_normalization_2/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}, "ksize": {"list": {"i": ["1", "2", "2", "1"]}}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}}}, {"name": "StatefulPartitionedCall/sequential_1/activation_3/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/sequential_1/max_pooling2d_1/MaxPool", "StatefulPartitionedCall/sequential_1/conv2d_3/Conv2D/ReadVariableOp", "StatefulPartitionedCall/sequential_1/conv2d_3/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}}}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_3/FusedBatchNormV3/Mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/sequential_1/activation_3/Relu", "StatefulPartitionedCall/sequential_1/batch_normalization_3/FusedBatchNormV3/Scaled"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_3/FusedBatchNormV3", "op": "Add", "input": ["StatefulPartitionedCall/sequential_1/batch_normalization_3/FusedBatchNormV3/Mul", "StatefulPartitionedCall/sequential_1/batch_normalization_3/FusedBatchNormV3/Offset"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/activation_4/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/sequential_1/batch_normalization_3/FusedBatchNormV3", "StatefulPartitionedCall/sequential_1/conv2d_4/Conv2D/ReadVariableOp", "StatefulPartitionedCall/sequential_1/conv2d_4/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_4/FusedBatchNormV3/Mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/sequential_1/activation_4/Relu", "StatefulPartitionedCall/sequential_1/batch_normalization_4/FusedBatchNormV3/Scaled"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_4/FusedBatchNormV3", "op": "Add", "input": ["StatefulPartitionedCall/sequential_1/batch_normalization_4/FusedBatchNormV3/Mul", "StatefulPartitionedCall/sequential_1/batch_normalization_4/FusedBatchNormV3/Offset"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/max_pooling2d_2/MaxPool", "op": "MaxPool", "input": ["StatefulPartitionedCall/sequential_1/batch_normalization_4/FusedBatchNormV3"], "attr": {"explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}, "ksize": {"list": {"i": ["1", "2", "2", "1"]}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}}}, {"name": "StatefulPartitionedCall/sequential_1/flatten_1/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/sequential_1/max_pooling2d_2/MaxPool", "StatefulPartitionedCall/sequential_1/flatten_1/Const"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_1/activation_5/Relu", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/sequential_1/flatten_1/Reshape", "StatefulPartitionedCall/sequential_1/dense_1/MatMul/ReadVariableOp", "StatefulPartitionedCall/sequential_1/dense_1/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0}, "transpose_b": {"b": false}, "num_args": {"i": "1"}, "transpose_a": {"b": false}}}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_5/batchnorm/mul_1", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/sequential_1/activation_5/Relu", "StatefulPartitionedCall/sequential_1/batch_normalization_5/batchnorm/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_5/batchnorm/add_1", "op": "AddV2", "input": ["StatefulPartitionedCall/sequential_1/batch_normalization_5/batchnorm/mul_1", "StatefulPartitionedCall/sequential_1/batch_normalization_5/batchnorm/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/dense_2/BiasAdd", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/sequential_1/batch_normalization_5/batchnorm/add_1", "StatefulPartitionedCall/sequential_1/dense_2/MatMul/ReadVariableOp", "StatefulPartitionedCall/sequential_1/dense_2/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "transpose_a": {"b": false}, "epsilon": {"f": 0}, "transpose_b": {"b": false}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/activation_6/Softmax", "op": "Softmax", "input": ["StatefulPartitionedCall/sequential_1/dense_2/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity", "op": "Identity", "input": ["StatefulPartitionedCall/sequential_1/activation_6/Softmax"], "attr": {"T": {"type": "DT_FLOAT"}}}], "library": {}, "versions": {"producer": 808}}, "weightsManifest": [{"paths": ["liveness.bin"], "weights": [{"name": "StatefulPartitionedCall/sequential_1/conv2d_1/Conv2D/ReadVariableOp", "shape": [3, 3, 3, 16], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_1/BiasAdd/ReadVariableOp", "shape": [16], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_2/Conv2D/ReadVariableOp", "shape": [3, 3, 16, 16], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_2/BiasAdd/ReadVariableOp", "shape": [16], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_3/Conv2D/ReadVariableOp", "shape": [3, 3, 16, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_3/BiasAdd/ReadVariableOp", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_4/Conv2D/ReadVariableOp", "shape": [3, 3, 32, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_4/BiasAdd/ReadVariableOp", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/flatten_1/Const", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_1/dense_1/MatMul/ReadVariableOp", "shape": [2048, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/dense_1/BiasAdd/ReadVariableOp", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_5/batchnorm/mul", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_5/batchnorm/sub", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/dense_2/MatMul/ReadVariableOp", "shape": [64, 2], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/dense_2/BiasAdd/ReadVariableOp", "shape": [2], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_2/FusedBatchNormV3/Scaled", "shape": [16], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_2/FusedBatchNormV3/Offset", "shape": [16], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_1/FusedBatchNormV3/Scaled", "shape": [16], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_1/FusedBatchNormV3/Offset", "shape": [16], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_4/FusedBatchNormV3/Scaled", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_4/FusedBatchNormV3/Offset", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_3/FusedBatchNormV3/Scaled", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/batch_normalization_3/FusedBatchNormV3/Offset", "shape": [32], "dtype": "float32"}]}]}