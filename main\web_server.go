package main

import (
	"archive/zip"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"runtime/debug"
	"strings"
	"sync"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/moipa-cn/pptx"
)

// WebResponse 统一的Web响应格式
type WebResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Debug   string      `json:"debug,omitempty"` // 调试信息
}

// ExtractRequest 提取请求结构
type ExtractRequest struct {
	Images []string `json:"images"`
}

// GeneratePPTRequest PPT生成请求结构
type GeneratePPTRequest struct {
	ResumeData    map[string]interface{} `json:"resume_data"`     // 简历数据
	InputJsonPath string                 `json:"input_json_path"` // 输入JSON文件的路径，用于确定输出目录
}

// 添加日志结构体
type LogMessage struct {
	Type    string `json:"type"`    // info, success, error, warning
	Message string `json:"message"` // 日志内容
	Time    string `json:"time"`    // 时间戳
}

// 日志管理器，为每个用户会话管理独立的日志流
type LogManager struct {
	clients map[string]chan LogMessage
	mutex   sync.RWMutex
}

var logManager = &LogManager{
	clients: make(map[string]chan LogMessage),
}

// 为客户端添加日志通道
func (lm *LogManager) addClient(clientID string) chan LogMessage {
	lm.mutex.Lock()
	defer lm.mutex.Unlock()

	ch := make(chan LogMessage, 100)
	lm.clients[clientID] = ch
	return ch
}

// 移除客户端日志通道
func (lm *LogManager) removeClient(clientID string) {
	lm.mutex.Lock()
	defer lm.mutex.Unlock()

	if ch, exists := lm.clients[clientID]; exists {
		close(ch)
		delete(lm.clients, clientID)
	}
}

// 广播日志消息到所有客户端
func (lm *LogManager) broadcast(msg LogMessage) {
	lm.mutex.RLock()
	defer lm.mutex.RUnlock()

	for clientID, ch := range lm.clients {
		select {
		case ch <- msg:
		default:
			// 通道满了，移除这个客户端
			go func(id string) {
				lm.removeClient(id)
			}(clientID)
		}
	}
}

// convertToDisplayPath 将绝对路径转换为相对路径，用于前端显示
func convertToDisplayPath(absolutePath string) string {
	projectRootPath, _ := os.Executable()
	if projectRootPath != "" {
		projectRootPath = filepath.Dir(projectRootPath)
		if filepath.Base(projectRootPath) == "main" {
			projectRootPath = filepath.Dir(projectRootPath)
		}
	} else {
		projectRootPath = "."
	}

	// 尝试转换为相对路径
	if relPath, err := filepath.Rel(projectRootPath, absolutePath); err == nil {
		return filepath.ToSlash(relPath) // 使用正斜杠，符合Web标准
	}

	// 如果转换失败，返回文件名
	return filepath.Base(absolutePath)
}

// 发送日志到前端
func sendLogToFrontend(logType, message string) {
	logMsg := LogMessage{
		Type:    logType,
		Message: message,
		Time:    time.Now().Format("15:04:05"),
	}

	// 同时打印到控制台
	fmt.Printf("[%s] %s: %s\n", logMsg.Time, strings.ToUpper(logType), message)

	// 广播到所有客户端
	logManager.broadcast(logMsg)
}

// 处理日志流
func handleLogStream(c *gin.Context) {
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")

	// 为这个客户端生成唯一ID
	clientID := fmt.Sprintf("client_%d_%d", time.Now().UnixNano(), c.Request.RemoteAddr)

	// 为客户端创建独立的日志通道
	clientLogChannel := logManager.addClient(clientID)
	defer logManager.removeClient(clientID)

	fmt.Printf("🔗 新的日志客户端连接: %s\n", clientID)

	// 发送初始连接消息
	c.SSEvent("log", gin.H{
		"type":    "info",
		"message": "日志流连接成功",
		"time":    time.Now().Format("15:04:05"),
	})
	c.Writer.Flush()

	// 监听客户端专用日志通道
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case logMsg := <-clientLogChannel:
			c.SSEvent("log", logMsg)
			c.Writer.Flush()
		case <-c.Request.Context().Done():
			fmt.Printf("🔌 日志客户端断开连接: %s\n", clientID)
			return
		case <-ticker.C:
			// 保持连接活跃
			c.SSEvent("ping", gin.H{"time": time.Now().Unix()})
			c.Writer.Flush()
		}
	}
}

// 启动Web服务器
func startWebServer() {
	// 检查是否为debug模式
	debugMode := os.Getenv("DEBUG") == "true"

	if debugMode {
		gin.SetMode(gin.DebugMode)
		fmt.Println("🐛 Debug模式已启用")
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	r := gin.Default()

	// 配置CORS
	config := cors.DefaultConfig()
	config.AllowAllOrigins = true
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	config.AllowHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization"}
	r.Use(cors.New(config))

	// 获取可执行文件所在目录
	exePath, err := os.Executable()
	if err != nil {
		fmt.Printf("❌ 获取可执行文件路径失败: %v\n", err)
		exePath = "."
	}
	exeDir := filepath.Dir(exePath)

	// 尝试确定项目根目录
	projectRoot := exeDir
	// 如果可执行文件位于 main 目录下（例如，通过 go run 运行或构建在 main 目录下），
	// 则项目根目录是其父目录
	if filepath.Base(exeDir) == "main" {
		projectRoot = filepath.Dir(exeDir)
	}

	// 构建前端文件路径，基于项目根目录
	frontendDir := filepath.Join(projectRoot, "frontend")

	// 检查前端目录是否存在
	if _, err := os.Stat(frontendDir); os.IsNotExist(err) {
		fmt.Printf("⚠️  前端目录不存在: %s\n", frontendDir)
		fmt.Println("请确保frontend目录在项目根目录下")
		return
	}

	// 静态文件服务
	r.Static("/webfonts", filepath.Join(frontendDir, "webfonts"))
	r.StaticFile("/", filepath.Join(frontendDir, "index.html"))
	r.StaticFile("/index.html", filepath.Join(frontendDir, "index.html"))
	r.StaticFile("/script.js", filepath.Join(frontendDir, "script.js"))
	r.StaticFile("/tailwind.min.css", filepath.Join(frontendDir, "tailwind.min.css"))
	r.StaticFile("/font-awesome.min.css", filepath.Join(frontendDir, "font-awesome.min.css"))

	// 处理favicon.ico请求
	r.GET("/favicon.ico", func(c *gin.Context) {
		c.Status(http.StatusNoContent)
	})

	// API路由
	api := r.Group("/api")
	{
		api.POST("/extract", handleExtractWithDebug(debugMode, projectRoot))
		api.POST("/download-images", handleDownloadImages)
		api.GET("/image/*filepath", handleImageRequest)
		api.GET("/log", handleLogStream)
		api.GET("/download-ppt/*filepath", handleDownloadPPT)
		api.POST("/generate-ppt", handleGeneratePPT(projectRoot))
	}

	// 获取端口配置
	port := os.Getenv("PORT")
	if port == "" {
		port = "8088" // 默认端口
	}

	fmt.Printf("🚀 Web服务器启动在 http://localhost:%s\n", port)
	if debugMode {
		fmt.Println("🔍 Debug模式: 详细错误信息将显示在响应中")
	}
	r.Run(":" + port)
}

// 带调试信息的提取处理函数
func handleExtractWithDebug(debugMode bool, projectRoot string) gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if r := recover(); r != nil {
				errorMsg := fmt.Sprintf("Panic: %v", r)
				stackTrace := string(debug.Stack())

				fmt.Printf("❌ Panic occurred: %s\n", errorMsg)
				fmt.Printf("📚 Stack trace:\n%s\n", stackTrace)

				response := WebResponse{
					Success: false,
					Message: "服务器内部错误",
				}

				if debugMode {
					response.Debug = fmt.Sprintf("Panic: %v\nStack: %s", r, stackTrace)
				}

				c.JSON(http.StatusInternalServerError, response)
			}
		}()

		handleExtract(c, debugMode, projectRoot)
	}
}

// 处理文件提取
func handleExtract(c *gin.Context, debugMode bool, projectRoot string) {
	var err error
	var errorMsg string
	var images []string
	var outputDirName string
	var outputDir string
	startTime := time.Now()
	sendLogToFrontend("info", "🚀 开始PPT信息提取流程")

	// 获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		errorMsg = fmt.Sprintf("获取上传文件失败: %v", err)
		sendLogToFrontend("error", "❌ "+errorMsg)
		fmt.Printf("❌ %s\n", errorMsg)

		response := WebResponse{
			Success: false,
			Message: errorMsg,
		}

		if debugMode {
			response.Debug = fmt.Sprintf("FormFile error: %v", err)
		}

		c.JSON(http.StatusBadRequest, response)
		return
	}

	sendLogToFrontend("info", fmt.Sprintf("📁 文件: %s (%d bytes)", file.Filename, file.Size))

	// 获取可执行文件所在目录 - 已由 projectRoot 提供
	// exePath, err := os.Executable()
	// if err != nil {
	// 	fmt.Printf("❌ 获取可执行文件路径失败: %v\n", err)
	// 	exePath = "."
	// }
	// exeDir := filepath.Dir(exePath)

	// 构建目录路径 - 使用 projectRoot 确保绝对路径
	tempDir := filepath.Join(projectRoot, "temp")

	// 确保目录存在
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		errorMsg = fmt.Sprintf("创建临时目录失败: %v", err)
		sendLogToFrontend("error", "❌ "+errorMsg)
		fmt.Printf("❌ %s\n", errorMsg)

		response := WebResponse{
			Success: false,
			Message: "创建临时目录失败",
		}
		if debugMode {
			response.Debug = fmt.Sprintf("MkdirAll temp error: %v", err)
		}
		c.JSON(http.StatusInternalServerError, response)
		return
	}

	// 创建临时工作目录
	tempWorkDir, err := ioutil.TempDir(tempDir, "extract_*")
	if err != nil {
		errorMsg := fmt.Sprintf("创建临时工作目录失败: %v", err)
		sendLogToFrontend("error", "❌ "+errorMsg)
		fmt.Printf("❌ %s\n", errorMsg)

		response := WebResponse{
			Success: false,
			Message: "创建临时工作目录失败",
		}
		if debugMode {
			response.Debug = fmt.Sprintf("TempDir error: %v", err)
		}
		c.JSON(http.StatusInternalServerError, response)
		return
	}
	defer func() {
		if err := os.RemoveAll(tempWorkDir); err != nil {
			fmt.Printf("⚠️  清理临时目录失败: %v\n", err)
		} else {
			fmt.Printf("✅ 临时目录已清理: %s\n", tempWorkDir)
		}
	}()

	fmt.Printf("📂 创建临时目录: %s\n", tempWorkDir)

	// 保存上传的文件
	pptPath := path.Join(tempWorkDir, file.Filename)
	if err := c.SaveUploadedFile(file, pptPath); err != nil {
		errorMsg = fmt.Sprintf("保存文件失败: %v", err)
		sendLogToFrontend("error", "❌ "+errorMsg)
		fmt.Printf("❌ %s\n", errorMsg)

		response := WebResponse{
			Success: false,
			Message: errorMsg,
		}

		if debugMode {
			response.Debug = fmt.Sprintf("SaveUploadedFile error: %v", err)
		}

		c.JSON(http.StatusInternalServerError, response)
		return
	}

	sendLogToFrontend("success", "💾 文件保存成功")

	if debugMode {
		fmt.Printf("💾 文件已保存到: %s\n", pptPath)
	}

	// 提取文本内容
	sendLogToFrontend("info", "📝 开始提取PPT文本内容...")
	allText, err := extractAllTextFromPPT(pptPath)
	if err != nil {
		errorMsg = fmt.Sprintf("PPT文本提取失败: %v", err)
		sendLogToFrontend("error", "❌ "+errorMsg)
		fmt.Printf("❌ %s\n", errorMsg)

		response := WebResponse{
			Success: false,
			Message: errorMsg,
		}

		if debugMode {
			response.Debug = fmt.Sprintf("extractAllTextFromPPT error: %v", err)
		}

		c.JSON(http.StatusInternalServerError, response)
		return
	}

	sendLogToFrontend("success", fmt.Sprintf("📝 文本提取完成，共 %d 字符", len(allText)))

	if debugMode {
		fmt.Printf("📝 提取到文本长度: %d 字符\n", len(allText))
		fmt.Printf("📝 文本预览: %s...\n", truncateString(allText, 200))
	}

	// 使用新的简历信息提取函数
	sendLogToFrontend("info", "🤖 开始调用AI进行详细简历信息提取...")

	// 调用专门的简历信息提取函数
	info, err := extractResumeInfo(allText)
	if err != nil {
		errorMsg = fmt.Sprintf("AI简历信息提取失败: %v", err)
		sendLogToFrontend("error", "❌ "+errorMsg)
		fmt.Printf("❌ %s\n", errorMsg)

		response := WebResponse{
			Success: false,
			Message: errorMsg,
		}

		if debugMode {
			response.Debug = fmt.Sprintf("extractResumeInfo error: %v", err)
		}

		c.JSON(http.StatusInternalServerError, response)
		return
	}

	sendLogToFrontend("success", "✅ AI详细简历信息提取完成")

	if debugMode {
		fmt.Printf("✅ AI简历信息提取成功\n")
		fmt.Printf("📋 提取的简历信息: %+v\n", info)
	}

	// 清理文本内容 (仅对字符串字段进行清理)
	// info.Description 现在是 []string 类型，不再直接进行 string 级别的清理
	// info.Description = cleanXMLContent(strings.TrimSpace(info.Description))
	// 对于 Position, AcademicTitle, AcademicOutput，它们现在也是 []string 类型，不再进行 string 级别的清理
	// info.Position = cleanXMLContent(strings.TrimSpace(info.Position))
	// info.AcademicTitle = cleanXMLContent(strings.TrimSpace(info.AcademicTitle))
	// info.AcademicOutput = cleanXMLContent(strings.TrimSpace(info.AcademicOutput))

	// 以下字段仍然是 string 类型，可以进行清理
	info.Specialty = cleanXMLContent(strings.TrimSpace(info.Specialty))
	info.Other = cleanXMLContent(strings.TrimSpace(info.Other))

	sendLogToFrontend("success", "📋 简历信息解析完成")

	// 提取图片
	sendLogToFrontend("info", "🖼️ 开始提取PPT中的图片...")
	outputDirName = fmt.Sprintf("VCOUTPUTDATA%s", time.Now().Format("20060102150405"))
	outputDir = filepath.Join(projectRoot, "output", outputDirName) // 使用 projectRoot 确保绝对路径
	err = os.MkdirAll(outputDir, 0755)
	if err != nil {
		errorMsg = fmt.Sprintf("创建输出目录失败: %v", err)
		sendLogToFrontend("error", "❌ "+errorMsg)
		fmt.Printf("❌ %s\n", errorMsg)

		response := WebResponse{
			Success: false,
			Message: errorMsg,
		}

		if debugMode {
			response.Debug = fmt.Sprintf("MkdirAll output error: %v", err)
		}

		c.JSON(http.StatusInternalServerError, response)
		return
	}

	if debugMode {
		fmt.Printf("📁 创建输出目录: %s\n", outputDir)
	}

	// 提取图片并获取图片列表
	images, err = extractImagesFromFileWeb(pptPath, outputDir, info)
	if err != nil {
		errorMsg = fmt.Sprintf("图片提取失败: %v", err)
		sendLogToFrontend("error", "❌ "+errorMsg)
		fmt.Printf("❌ %s\n", errorMsg)

		response := WebResponse{
			Success: false,
			Message: errorMsg,
		}

		if debugMode {
			response.Debug = fmt.Sprintf("extractImagesFromFileWeb error: %v", err)
		}

		c.JSON(http.StatusInternalServerError, response)
		return
	}

	sendLogToFrontend("success", fmt.Sprintf("🖼️ 图片提取完成，共 %d 张图片", len(images)))

	// 拼接带目录的图片路径
	var imagesWithDir []string
	for _, img := range images {
		// 这里仍然是相对路径，因为前端URL需要相对路径
		imagesWithDir = append(imagesWithDir, filepath.Join(outputDirName, img))
	}

	if debugMode {
		fmt.Printf("🖼️ 提取到 %d 张图片: %v\n", len(images), imagesWithDir)
	}

	// 图片智能处理（人像检测和背景去除）
	var processingStats *ProcessingStats
	if len(images) > 0 {
		sendLogToFrontend("info", "🔍 开始图片智能处理...")
		fmt.Println("🔍 开始图片智能处理")
		fmt.Printf("📸 准备处理 %d 张图片\n", len(images))

		// 加载配置
		config, err := LoadConfig("")
		if err != nil {
			sendLogToFrontend("warning", "⚠️ 加载配置失败，跳过图片处理")
			fmt.Printf("⚠️  加载配置失败: %v，跳过图片处理\n", err)
			processingStats = &ProcessingStats{TotalImages: len(images)}
		} else {
			// 创建图片处理器实例
			processor, err := NewImageProcessor(config)
			if err != nil {
				errorMsg = fmt.Sprintf("创建图片处理器失败: %v", err)
				sendLogToFrontend("error", "❌ "+errorMsg)
				c.JSON(http.StatusInternalServerError, WebResponse{Success: false, Message: errorMsg})
				return
			}

			// 调用实例的 ProcessImages 方法
			processingStats, err = processor.ProcessImages(images, outputDir)
			if err != nil {
				errorMsg = fmt.Sprintf("图片智能处理失败: %v", err)
				sendLogToFrontend("error", "❌ "+errorMsg)
				fmt.Printf("❌ %s\n", errorMsg)

				response := WebResponse{
					Success: false,
					Message: errorMsg,
				}

				if debugMode {
					response.Debug = fmt.Sprintf("ProcessImages error: %v", err)
				}
				c.JSON(http.StatusInternalServerError, response)
				return
			}
		}

		if processingStats != nil && processingStats.ProcessedImages > 0 {
			sendLogToFrontend("success", fmt.Sprintf("✅ 图片智能处理完成，处理了 %d 张图片", processingStats.ProcessedImages))
			fmt.Printf("✅ 图片智能处理完成，处理了 %d 张图片\n", processingStats.ProcessedImages)
		} else {
			sendLogToFrontend("info", "ℹ️ 未进行图片智能处理或没有图片需要处理")
			fmt.Println("ℹ️ 未进行图片智能处理或没有图片需要处理")
		}
	} else {
		sendLogToFrontend("info", "ℹ️ 未检测到图片，跳过图片智能处理")
		fmt.Println("ℹ️ 未检测到图片，跳过图片智能处理")
	}

	// 保存最终的JSON文件
	// 如果存在最佳头像，则更新ResumeInfo
	if processingStats != nil && processingStats.BestPortrait != "" {
		fmt.Printf("DEBUG: BestPortrait: %s\n", processingStats.BestPortrait)

		// 检查 BestPortrait 是否已经是相对路径
		if filepath.IsAbs(processingStats.BestPortrait) {
			// 如果是绝对路径，转换为相对路径
			outputRootPath := filepath.Join(projectRoot, "output")
			fmt.Printf("DEBUG: OutputRootPath for Rel: %s\n", outputRootPath)

			relPath, err := filepath.Rel(outputRootPath, processingStats.BestPortrait)
			if err != nil {
				sendLogToFrontend("error", fmt.Sprintf("❌ 获取头像相对路径失败: %v", err))
				fmt.Printf("DEBUG: Error getting relative path: %v\n", err)
				info.ProfilePicture = "" // 设置为空，避免错误路径
			} else {
				// 转换为正斜杠格式
				info.ProfilePicture = filepath.ToSlash(relPath)
				sendLogToFrontend("info", fmt.Sprintf("识别到最佳头像: %s", info.ProfilePicture))
				fmt.Printf("DEBUG: Final ProfilePicture (converted): %s\n", info.ProfilePicture)
			}
		} else {
			// 如果已经是相对路径，直接使用
			// 但需要确保移除开头的 "output\" 或 "output/"，因为前端期望的是相对于output目录的路径
			relPath := processingStats.BestPortrait
			if strings.HasPrefix(relPath, "output\\") || strings.HasPrefix(relPath, "output/") {
				relPath = relPath[7:] // 移除 "output\" 或 "output/"
			}
			info.ProfilePicture = filepath.ToSlash(relPath)
			sendLogToFrontend("info", fmt.Sprintf("识别到最佳头像: %s", info.ProfilePicture))
			fmt.Printf("DEBUG: Final ProfilePicture (relative): %s\n", info.ProfilePicture)
		}
	} else if info.ProfilePicture != "" {
		// 如果没有经过处理，但原始提取的info中有头像，则使用原始头像路径
		// 这里假设 info.ProfilePicture 已经是类似 VCOUTPUTDATAxxxx/imageX.jpeg 的相对路径或者只有文件名
		// 我们需要确保最终存储的是相对于 output 目录的正确相对路径
		// 从原始路径中提取文件名，并与 outputDirName 拼接
		baseFileName := filepath.Base(info.ProfilePicture)
		info.ProfilePicture = filepath.ToSlash(filepath.Join(outputDirName, baseFileName))
		sendLogToFrontend("info", fmt.Sprintf("使用原始提取的头像: %s", info.ProfilePicture))
		fmt.Printf("DEBUG: Final ProfilePicture (original): %s\n", info.ProfilePicture)
	} else {
		sendLogToFrontend("info", "ℹ️ 未识别到头像")
	}

	// 合并额外信息到 Description 字段
	// 如果合并后的内容不为空，则更新 info.Description
	// info.Description = combinedDescription.String() // 移除此行，使用AI返回的原始介绍

	jsonFileName := "vc_extracted.json"
	jsonFilePath := filepath.Join(outputDir, jsonFileName)

	jsonData, err := json.MarshalIndent(info, "", "  ")
	if err != nil {
		errorMsg = fmt.Sprintf("序列化JSON失败: %v", err)
		sendLogToFrontend("error", "❌ "+errorMsg)
		fmt.Printf("❌ %s\n", errorMsg)

		response := WebResponse{
			Success: false,
			Message: errorMsg,
		}

		if debugMode {
			response.Debug = fmt.Sprintf("json.MarshalIndent error: %v", err)
		}
		c.JSON(http.StatusInternalServerError, response)
		return
	}

	if err := os.WriteFile(jsonFilePath, jsonData, 0644); err != nil {
		errorMsg = fmt.Sprintf("保存JSON文件失败: %v", err)
		sendLogToFrontend("error", "❌ "+errorMsg)
		fmt.Printf("❌ %s\n", errorMsg)

		response := WebResponse{
			Success: false,
			Message: errorMsg,
		}

		if debugMode {
			response.Debug = fmt.Sprintf("os.WriteFile error: %v", err)
		}
		c.JSON(http.StatusInternalServerError, response)
		return
	}

	sendLogToFrontend("success", fmt.Sprintf("🎉 所有处理完成！"))

	// 封装响应数据
	responseData := gin.H{
		"resume_info":      info,
		"output_dir":       outputDirName,
		"images":           imagesWithDir,   // 添加提取到的图片路径列表
		"processing_stats": processingStats, // 添加图片处理统计信息
	}

	totalDuration := time.Since(startTime)
	sendLogToFrontend("success", fmt.Sprintf("🎉 PPT信息提取与处理完成！耗时: %s", totalDuration.Round(time.Millisecond)))

	response := WebResponse{
		Success: true,
		Message: "PPT信息提取与图片处理完成",
		Data:    responseData,
	}

	if debugMode {
		response.Debug = fmt.Sprintf("处理时间: %dms", time.Since(startTime).Milliseconds())
	}

	c.JSON(http.StatusOK, response)
}

// 处理图片请求
func handleImageRequest(c *gin.Context) {
	filePath := c.Param("filepath")
	if filePath == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "文件路径不能为空"})
		return
	}

	// 解码URL编码的文件路径
	decodedPath, err := url.QueryUnescape(filePath)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "文件路径格式错误"})
		return
	}

	// 去除开头的斜杠
	decodedPath = strings.TrimPrefix(decodedPath, "/")

	fmt.Printf("🔍 图片请求: %s (解码后: %s)\n", filePath, decodedPath)

	// 安全验证：防止路径遍历攻击
	if strings.Contains(decodedPath, "..") {
		fmt.Printf("❌ 路径包含非法字符: %s\n", decodedPath)
		c.JSON(http.StatusForbidden, gin.H{"error": "非法图片路径"})
		return
	}

	// 检查路径格式并构造完整路径
	var imagePath string

	// 获取可执行文件所在目录，从而确定项目根目录
	exePath, err := os.Executable()
	if err != nil {
		fmt.Printf("❌ 获取可执行文件路径失败: %v\n", err)
		exePath = "."
	}
	exeDir := filepath.Dir(exePath)
	projectRoot := exeDir
	if filepath.Base(exeDir) == "main" {
		projectRoot = filepath.Dir(exeDir)
	}

	// 根据decodedPath的格式判断是output目录下的图片还是templates-ppt下的图片
	if strings.HasPrefix(decodedPath, "output/") || strings.HasPrefix(decodedPath, "templates-ppt/") {
		// 如果是output或templates-ppt目录下的图片，则拼接项目根目录
		imagePath = filepath.Join(projectRoot, decodedPath)
	} else {
		// 否则，假定为output目录下的图片（兼容旧路径，或处理其他情况）
		imagePath = filepath.Join(projectRoot, "output", decodedPath)
	}

	fmt.Printf("🔍 完整图片路径: %s\n", imagePath)

	// 检查文件是否存在
	if _, err := os.Stat(imagePath); os.IsNotExist(err) {
		fmt.Printf("❌ 图片文件不存在: %s\n", imagePath)
		c.JSON(http.StatusNotFound, gin.H{"error": "图片文件不存在"})
		return
	}

	// 提供文件
	c.File(imagePath)
	fmt.Printf("✅ 图片访问成功: %s\n", imagePath)
}

// 处理图片下载
func handleDownloadImages(c *gin.Context) {
	// 添加调试信息
	fmt.Printf("🔍 收到下载图片请求\n")

	// 读取原始请求体用于调试
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		fmt.Printf("❌ 读取请求体失败: %v\n", err)
		c.JSON(http.StatusBadRequest, WebResponse{
			Success: false,
			Message: "读取请求数据失败",
		})
		return
	}

	fmt.Printf("📄 原始请求体: %s\n", string(body))

	// 重新设置请求体供后续解析使用
	c.Request.Body = ioutil.NopCloser(bytes.NewBuffer(body))

	var req ExtractRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		fmt.Printf("❌ JSON解析失败: %v\n", err)
		c.JSON(http.StatusBadRequest, WebResponse{
			Success: false,
			Message: "请求参数错误",
		})
		return
	}

	fmt.Printf("DEBUG: handleDownloadImages - 接收到请求体中的图片列表: %+v\n", req.Images) // 新增日志
	fmt.Printf("📋 解析后的请求数据: %+v\n", req)
	fmt.Printf("📸 图片数量: %d\n", len(req.Images))
	for i, img := range req.Images {
		fmt.Printf("  图片 %d: %s\n", i+1, img)
	}

	if len(req.Images) == 0 {
		fmt.Printf("DEBUG: handleDownloadImages - 没有找到任何图片文件，addedCount为0\n") // 新增日志
		c.JSON(http.StatusBadRequest, WebResponse{
			Success: false,
			Message: "没有指定要下载的图片",
		})
		return
	}

	// 创建临时目录
	tempDir, err := ioutil.TempDir("", "download_*")
	if err != nil {
		c.JSON(http.StatusInternalServerError, WebResponse{
			Success: false,
			Message: "创建临时目录失败",
		})
		return
	}
	defer os.RemoveAll(tempDir)

	// 创建ZIP文件
	zipPath := filepath.Join(tempDir, "images.zip")
	zipFile, err := os.Create(zipPath)
	if err != nil {
		c.JSON(http.StatusInternalServerError, WebResponse{
			Success: false,
			Message: "创建ZIP文件失败",
		})
		return
	}
	defer zipFile.Close()

	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()

	// 添加图片到ZIP
	addedCount := 0
	for _, imageName := range req.Images {
		// 统一路径分隔符，将所有反斜杠替换为正斜杠
		// 无论前端传来的是 '/' 还是 '\\', 都统一为 '/'
		cleanedImageName := strings.ReplaceAll(imageName, "\\", "/")

		// 构建完整的物理路径：output/<VCOUTPUTDATA_DIR>/<image_name>
		// filepath.Join 会正确处理操作系统对应的路径分隔符
		imagePath := filepath.Join("output", cleanedImageName)

		// 新增调试信息：打印原始图片名称和尝试查找的完整路径
		fmt.Printf("DEBUG_DOWNLOAD: 原始图片名: %s, 清理后: %s, 尝试查找文件: %s\n", imageName, cleanedImageName, imagePath) // 现有日志，确保显示

		// 验证文件是否存在
		if _, err := os.Stat(imagePath); err != nil {
			fmt.Printf("⚠️  图片文件未找到或无法访问: %s - %v\n", imagePath, err) // 现有日志，确保显示
			continue
		}

		// 读取图片文件
		imageData, err := ioutil.ReadFile(imagePath)
		if err != nil {
			fmt.Printf("⚠️  读取图片失败: %s - %v\n", imagePath, err)
			continue
		}

		// 获取 ZIP 内的文件名，确保只包含最终文件名，不带任务目录前缀
		// 例如，VCOUTPUTDATA20250703174230/image1.jpeg -> image1.jpeg
		zipFileName := filepath.Base(cleanedImageName)

		zipEntry, err := zipWriter.Create(zipFileName)
		if err != nil {
			fmt.Printf("⚠️  创建ZIP条目失败: %s - %v\n", zipFileName, err)
			continue
		}

		_, err = zipEntry.Write(imageData)
		if err != nil {
			fmt.Printf("⚠️  写入ZIP失败: %s - %v\n", zipFileName, err)
			continue
		}

		addedCount++
		fmt.Printf("✅ 已添加图片到ZIP: %s (原路径: %s)\n", zipFileName, imageName)
	}

	if addedCount == 0 {
		c.JSON(http.StatusNotFound, WebResponse{
			Success: false,
			Message: "没有找到任何图片文件",
		})
		return
	}

	// 关闭ZIP文件
	zipWriter.Close()

	// 读取ZIP文件内容
	zipData, err := ioutil.ReadFile(zipPath)
	if err != nil {
		c.JSON(http.StatusInternalServerError, WebResponse{
			Success: false,
			Message: "读取ZIP文件失败",
		})
		return
	}

	// 设置响应头
	c.Header("Content-Type", "application/zip")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=extracted_images_%d.zip", time.Now().Unix()))
	c.Header("Content-Length", fmt.Sprintf("%d", len(zipData)))

	// 发送ZIP文件
	c.Data(http.StatusOK, "application/zip", zipData)

	fmt.Printf("✅ 图片下载完成，共下载 %d 张图片\n", addedCount)
}

// 验证PPT文件
func isValidPPTFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	return ext == ".ppt" || ext == ".pptx"
}

// Web版本的图片提取函数，返回图片列表
func extractImagesFromFileWeb(pptFile, outputDir string, info *ResumeInfo) ([]string, error) {
	ppt, err := pptx.ReadPowerPoint(pptFile)
	if err != nil {
		return nil, err
	}

	var images []string
	imageCount := 0

	for _, file := range ppt.Files {
		if strings.HasPrefix(file.Name, "ppt/media/") {
			lowerName := strings.ToLower(file.Name)
			if strings.HasSuffix(lowerName, ".jpg") ||
				strings.HasSuffix(lowerName, ".jpeg") ||
				strings.HasSuffix(lowerName, ".png") ||
				strings.HasSuffix(lowerName, ".gif") ||
				strings.HasSuffix(lowerName, ".bmp") {

				imageCount++
				ext := filepath.Ext(file.Name)
				imageFilename := fmt.Sprintf("image%d%s", imageCount, ext)
				imagePath := path.Join(outputDir, imageFilename)

				// 打开文件并读取数据
				src, err := file.Open()
				if err != nil {
					continue
				}
				defer src.Close()

				imgData, err := io.ReadAll(src)
				if err != nil {
					continue
				}

				// 保存图片文件
				if err := os.WriteFile(imagePath, imgData, 0644); err != nil {
					sendLogToFrontend("error", fmt.Sprintf("❌ 保存图片文件失败: %s - %v", convertToDisplayPath(imagePath), err))
					continue
				}

				images = append(images, imageFilename)

				// 设置第一张图片为头像
				if imageCount == 1 {
					// 确保 ProfilePicture 包含任务目录名称
					outputDirName := filepath.Base(outputDir)
					info.ProfilePicture = filepath.ToSlash(filepath.Join(outputDirName, imageFilename))
				}
			}
		}
	}

	return images, nil
}

// 截断字符串用于调试显示
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}

// 提取PPT中的所有文本内容
func extractAllTextFromPPT(pptFile string) (string, error) {
	ppt, err := pptx.ReadPowerPoint(pptFile)
	if err != nil {
		return "", err
	}

	var allText strings.Builder

	for _, file := range ppt.Files {
		if strings.HasPrefix(file.Name, "ppt/slides/slide") && strings.HasSuffix(file.Name, ".xml") {
			// 读取幻灯片内容
			src, err := file.Open()
			if err != nil {
				continue
			}
			defer src.Close()

			content, err := ioutil.ReadAll(src)
			if err != nil {
				continue
			}

			// 提取文本内容（简化版，实际应该解析XML）
			text := string(content)
			// 移除XML标签，保留文本内容
			text = cleanXMLContent(text)
			allText.WriteString(text)
			allText.WriteString("\n")
		}
	}

	return allText.String(), nil
}

// 清理XML内容
func cleanXMLContent(content string) string {
	// 移除常见的XML标签
	content = strings.ReplaceAll(content, "<br>", "\n")
	content = strings.ReplaceAll(content, "<br/>", "\n")
	content = strings.ReplaceAll(content, "<br />", "\n")
	content = strings.ReplaceAll(content, "<p>", "")
	content = strings.ReplaceAll(content, "</p>", "\n")
	content = strings.ReplaceAll(content, "<div>", "")
	content = strings.ReplaceAll(content, "</div>", "\n")
	content = strings.ReplaceAll(content, "<span>", "")
	content = strings.ReplaceAll(content, "</span>", "")

	// 移除其他可能的HTML标签
	// 这里可以添加更多的标签清理逻辑

	// 清理多余的空白字符
	content = strings.TrimSpace(content)

	return content
}

// 处理PPT下载请求
func handleDownloadPPT(c *gin.Context) {
	sendLogToFrontend("debug", "DEBUG: Entering handleDownloadPPT function.")
	encodedFilePath := c.Param("filepath")
	if encodedFilePath == "" {
		c.JSON(http.StatusBadRequest, WebResponse{Success: false, Message: "文件路径不能为空"})
		return
	}

	filePath, err := url.QueryUnescape(encodedFilePath)
	if err != nil {
		sendLogToFrontend("error", fmt.Sprintf("❌ 解码PPT文件路径失败: %v", err))
		c.JSON(http.StatusBadRequest, WebResponse{Success: false, Message: "无效的文件路径"})
		return
	}
	sendLogToFrontend("debug", fmt.Sprintf("DEBUG: handleDownloadPPT - 原始 filePath (解码后): %s", filePath))

	// === 新增：处理Windows绝对路径可能带有的前导斜杠 ===
	// 如果是 Windows 路径且以 /X:/ 形式开头，移除前导斜杠
	if len(filePath) >= 3 && filePath[0] == '/' &&
		((filePath[1] >= 'a' && filePath[1] <= 'z') || (filePath[1] >= 'A' && filePath[1] <= 'Z')) &&
		filePath[2] == ':' {
		filePath = filePath[1:] // 移除开头的斜杠
		sendLogToFrontend("debug", fmt.Sprintf("DEBUG: 移除前导斜杠后 filePath: %s", filePath))
	}

	// 获取项目根目录
	exePath, err := os.Executable()
	if err != nil {
		fmt.Printf("❌ 获取可执行文件路径失败: %v\n", err)
		exePath = "."
	}
	exeDir := filepath.Dir(exePath)
	projectRoot := exeDir
	if filepath.Base(exeDir) == "main" {
		projectRoot = filepath.Dir(exeDir)
	}
	sendLogToFrontend("debug", fmt.Sprintf("DEBUG: projectRoot: %s", projectRoot))

	var fullPath string
	// 移除前导斜杠（如果存在），因为PPT文件路径都应该相对于output目录
	cleanPath := strings.TrimPrefix(filePath, "/")
	sendLogToFrontend("debug", fmt.Sprintf("DEBUG: 清理后的相对路径: %s", cleanPath))

	// 所有PPT文件都应该在output目录下，直接拼接
	fullPath = filepath.Join(projectRoot, "output", cleanPath)
	sendLogToFrontend("debug", fmt.Sprintf("DEBUG: 最终文件路径: %s", convertToDisplayPath(fullPath)))

	sendLogToFrontend("debug", fmt.Sprintf("DEBUG: 准备下载文件: %s", convertToDisplayPath(fullPath)))

	// 检查文件是否存在
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		sendLogToFrontend("error", fmt.Sprintf("❌ PPT文件不存在: %s (底层错误: %v)", convertToDisplayPath(fullPath), err)) // 增加底层错误日志
		c.JSON(http.StatusNotFound, WebResponse{Success: false, Message: "文件不存在"})
		return
	}

	// 获取自定义文件名参数（可选）
	customFilename := c.Query("filename")
	var downloadFilename string
	if customFilename != "" {
		// 使用自定义文件名，确保有.pptx扩展名
		if !strings.HasSuffix(strings.ToLower(customFilename), ".pptx") {
			downloadFilename = customFilename + ".pptx"
		} else {
			downloadFilename = customFilename
		}
		sendLogToFrontend("info", fmt.Sprintf("📄 使用自定义文件名: %s", downloadFilename))
	} else {
		// 使用原始文件名
		downloadFilename = filepath.Base(fullPath)
		sendLogToFrontend("info", fmt.Sprintf("📄 使用默认文件名: %s", downloadFilename))
	}

	// 设置Content-Disposition头部，提示浏览器下载文件
	c.Header("Content-Disposition", "attachment; filename=\""+downloadFilename+"\"")
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.presentationml.presentation")

	// 提供文件下载
	c.File(fullPath)
	sendLogToFrontend("success", fmt.Sprintf("🎉 已成功下载PPT文件: %s", filepath.Base(fullPath)))
}

// handleGeneratePPT 处理PPT生成请求
func handleGeneratePPT(projectRoot string) gin.HandlerFunc {
	return func(c *gin.Context) {
		sendLogToFrontend("info", "➡️ 接收到PPT生成请求...")

		var req GeneratePPTRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			errorMsg := fmt.Sprintf("请求数据解析失败: %v", err)
			sendLogToFrontend("error", "❌ "+errorMsg)
			c.JSON(http.StatusBadRequest, WebResponse{Success: false, Message: errorMsg})
			return
		}

		// 从InputJsonPath解析出outputDir
		var outputDir string
		if req.InputJsonPath != "" {
			// 修正：InputJsonPath是子目录名称，需要与 projectRoot/output 拼接
			outputDir = filepath.Join(projectRoot, "output", req.InputJsonPath) // 修正路径拼接
			sendLogToFrontend("info", fmt.Sprintf("检测到输入JSON路径: %s, 提取输出目录: %s", req.InputJsonPath, convertToDisplayPath(outputDir)))
		} else {
			// 如果没有提供InputJsonPath，则使用默认的output/generated_ppts
			outputDir = filepath.Join(projectRoot, "output", "generated_ppts") // 修正默认路径拼接
			sendLogToFrontend("warning", fmt.Sprintf("⚠️ 未提供输入JSON路径，将使用默认输出目录: %s", convertToDisplayPath(outputDir)))
		}

		// 确保输出目录存在
		if err := os.MkdirAll(outputDir, 0755); err != nil {
			errorMsg := fmt.Sprintf("创建输出目录失败: %v", err)
			sendLogToFrontend("error", "❌ "+errorMsg)
			c.JSON(http.StatusInternalServerError, WebResponse{Success: false, Message: errorMsg})
			return
		}

		// 模板路径 (当前是固定路径，未来可能需要根据配置或请求动态选择)
		templatePath := "templates-ppt/ppt-teample1.pptx" // 确保这里的路径与项目根目录相对

		// 检查模板文件是否存在
		if _, err := os.Stat(templatePath); os.IsNotExist(err) {
			errorMsg := fmt.Sprintf("PPT模板文件不存在: %s", templatePath)
			sendLogToFrontend("error", "❌ "+errorMsg)
			c.JSON(http.StatusInternalServerError, WebResponse{Success: false, Message: errorMsg})
			return
		}

		// === 新增：将简历数据写入临时JSON文件 ===
		tempJSONFile, err := ioutil.TempFile("", "resume_data_*.json")
		if err != nil {
			errorMsg := fmt.Sprintf("创建临时JSON文件失败: %v", err)
			sendLogToFrontend("error", "❌ "+errorMsg)
			c.JSON(http.StatusInternalServerError, WebResponse{Success: false, Message: errorMsg})
			return
		}
		defer os.Remove(tempJSONFile.Name()) // 确保临时文件在函数退出时被删除

		jsonData, err := json.Marshal(req.ResumeData)
		if err != nil {
			errorMsg := fmt.Sprintf("JSON数据序列化失败: %v", err)
			sendLogToFrontend("error", "❌ "+errorMsg)
			c.JSON(http.StatusInternalServerError, WebResponse{Success: false, Message: errorMsg})
			return
		}
		if _, err := tempJSONFile.Write(jsonData); err != nil {
			errorMsg := fmt.Sprintf("写入临时JSON文件失败: %v", err)
			sendLogToFrontend("error", "❌ "+errorMsg)
			c.JSON(http.StatusInternalServerError, WebResponse{Success: false, Message: errorMsg})
			return
		}
		tempJSONFile.Close() // 确保文件被关闭，以便Python脚本可以读取

		sendLogToFrontend("info", fmt.Sprintf("📝 简历数据已写入临时文件: %s", tempJSONFile.Name()))

		// === 新增：调用Python脚本生成PPT ===
		// 自动检测Python脚本路径（适配本地和Docker环境）
		var pythonScriptPath string
		dockerPath := filepath.Join("ppt_generator", "ppt_generator.py")        // Docker环境路径
		localPath := filepath.Join("main", "ppt_generator", "ppt_generator.py") // 本地开发环境路径

		if _, err := os.Stat(dockerPath); err == nil {
			pythonScriptPath = dockerPath
			sendLogToFrontend("debug", fmt.Sprintf("检测到Docker环境，使用Python脚本路径: %s", pythonScriptPath))
		} else if _, err := os.Stat(localPath); err == nil {
			pythonScriptPath = localPath
			sendLogToFrontend("debug", fmt.Sprintf("检测到本地环境，使用Python脚本路径: %s", pythonScriptPath))
		} else {
			errorMsg := fmt.Sprintf("无法找到Python脚本文件，尝试的路径: %s, %s", dockerPath, localPath)
			sendLogToFrontend("error", "❌ "+errorMsg)
			c.JSON(http.StatusInternalServerError, WebResponse{Success: false, Message: errorMsg})
			return
		}

		// 构建Python命令
		cmd := exec.Command("python", pythonScriptPath,
			"--input_json", tempJSONFile.Name(),
			"--template_path", filepath.Join(projectRoot, templatePath), // 修正模板路径
			"--output_dir", outputDir)

		// 设置PYTHONIOENCODING环境变量，确保Python的I/O使用UTF-8编码
		cmd.Env = os.Environ()
		cmd.Env = append(cmd.Env, "PYTHONIOENCODING=utf-8")

		sendLogToFrontend("info", fmt.Sprintf("▶️ 正在执行Python脚本: %s %s %s %s %s %s",
			cmd.Path, cmd.Args[1], cmd.Args[2], cmd.Args[3], cmd.Args[4], cmd.Args[5]))

		// 捕获标准输出和标准错误
		var stdout, stderr bytes.Buffer
		cmd.Stdout = &stdout
		cmd.Stderr = &stderr

		// 获取项目根目录 (这是为了在执行Python脚本时，确保其工作目录正确)
		// 这个逻辑应该在startWebServer中处理一次并作为参数传递进来，或者直接在config中获取
		// 但为了快速集成，这里临时获取
		// projectRoot, err := os.Executable()
		// if err == nil {
		// 	projectRoot = filepath.Dir(projectRoot)
		// 	if filepath.Base(projectRoot) == "main" {
		// 		projectRoot = filepath.Dir(projectRoot)
		// 	}
		// } else {
		// 	projectRoot = "." // Fallback to current directory
		// }
		cmd.Dir = projectRoot // 设置Python脚本的工作目录为项目根目录

		err = cmd.Run() // 执行命令

		// 打印Python脚本的输出，无论成功与否
		if stdout.Len() > 0 {
			sendLogToFrontend("info", "Python stdout: "+stdout.String())
		}
		if stderr.Len() > 0 {
			sendLogToFrontend("error", "Python stderr: "+stderr.String())
		}

		if err != nil {
			errorMsg := fmt.Sprintf("Python脚本执行失败: %v", err)
			if stderr.Len() > 0 {
				errorMsg += "\n" + stderr.String()
			}
			sendLogToFrontend("error", "❌ "+errorMsg)
			c.JSON(http.StatusInternalServerError, WebResponse{Success: false, Message: errorMsg})
			return
		}

		// 假设Python脚本会将生成的PPT路径打印到标准输出的第一行
		generatedPPTPath := strings.TrimSpace(stdout.String())
		if generatedPPTPath == "" {
			errorMsg := "Python脚本未返回生成的PPT路径"
			sendLogToFrontend("error", "❌ "+errorMsg)
			c.JSON(http.StatusInternalServerError, WebResponse{Success: false, Message: errorMsg})
			return
		}

		// 将绝对路径转换为相对于 output 目录的相对路径
		outputBaseDir := filepath.Join(projectRoot, "output")
		relativePath, err := filepath.Rel(outputBaseDir, generatedPPTPath)
		if err != nil {
			errorMsg := fmt.Sprintf("计算相对路径失败: %v", err)
			sendLogToFrontend("error", "❌ "+errorMsg)
			c.JSON(http.StatusInternalServerError, WebResponse{Success: false, Message: errorMsg})
			return
		}

		// 确保使用正斜杠，因为前端URL通常使用正斜杠
		finalDownloadPath := filepath.ToSlash(relativePath)

		sendLogToFrontend("success", fmt.Sprintf("🎉 PPT文件生成成功: %s", finalDownloadPath))
		c.JSON(http.StatusOK, WebResponse{Success: true, Message: "PPT文件生成成功", Data: gin.H{"output_path": finalDownloadPath}})
	}
}
