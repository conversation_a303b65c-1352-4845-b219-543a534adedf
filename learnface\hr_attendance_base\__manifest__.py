# -*- coding: utf-8 -*-

{
    'name': "HR考勤人脸识别-基础模块 ",

    'summary': """
        模块提供快速有效的交互和继承
    对于所有依赖它的模块，形成一个生态系统""",

    'author': "GejieSoft Inc",
    'website': "https://www.gejiesoft.com",

    # 类别可用于筛选模块列表中的模块
    # 对于完整列表
    'category': '人力资源',
    'version': '********',
    "license": "LGPL-3",
    'images':[
        'static/description/Attendance_base.png',
    ],

    # any module necessary for this one to work correctly
    'depends': ['base','web','hr_attendance'],

    # always loaded
    'data': [
        'views/views.xml',
        'security/hr_attendance_security.xml',
    ],

    # 'qweb': [
    #     "static/src/xml/base.xml",
    # ],
    'assets': {
        'web.assets_backend': [
            'hr_attendance_base/static/src/css/steps.css',
            'hr_attendance_base/static/src/css/sweetalert2.css',
            'hr_attendance_base/static/src/js/lib/sweetalert2.js',
            'hr_attendance_base/static/src/js/attendances_base.js',
            'hr_attendance_base/static/src/js/kiosk_mode_base.js',
            'hr_attendance_base/static/src/**/*.xml',
        ],
        # 'web.assets_qweb': [
            
        # ],
    },
}
