#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试 rembg 是否正常工作
"""

import sys
import os

try:
    print("正在导入 rembg...")
    from rembg import remove, new_session
    print("✅ rembg 导入成功")
    
    # 测试创建会话
    print("正在创建 u2net_human_seg 会话...")
    session = new_session('u2net_human_seg')
    print("✅ 会话创建成功")
    
    print("rembg 测试完成，一切正常！")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ 其他错误: {e}")
    sys.exit(1)
