@echo off
chcp 65001
echo 🚀 启动头像抠图处理系统本地服务器 (Node.js版本)
echo.
echo 正在检查Node.js环境...

node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js环境正常
echo.
echo 🌐 启动HTTP服务器...
echo 服务器地址: http://localhost:8000
echo.
echo 📖 使用说明:
echo 1. 保持此窗口打开
echo 2. 在浏览器中访问: http://localhost:8000/start.html
echo 3. 按 Ctrl+C 停止服务器
echo.

npx http-server -p 8000 -c-1

pause
