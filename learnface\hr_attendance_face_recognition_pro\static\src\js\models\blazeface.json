{"format": "graph-model", "generatedBy": "https://github.com/google/mediapipe", "convertedBy": "https://github.com/vladmandic", "userDefinedMetadata": {"signature": {"inputs": {"input:0": {"name": "input:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "256"}, {"size": "256"}, {"size": "3"}]}}}, "outputs": {"Identity_3:0": {"name": "Identity_3:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "384"}, {"size": "16"}]}}, "Identity:0": {"name": "Identity:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "512"}, {"size": "1"}]}}, "Identity_1:0": {"name": "Identity_1:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "384"}, {"size": "1"}]}}, "Identity_2:0": {"name": "Identity_2:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "512"}, {"size": "16"}]}}}}}, "modelTopology": {"node": [{"name": "unknown_135", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "2"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_136", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "StatefulPartitionedCall/functional_1/tf_op_layer_classificators_1/classificators_1/shape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "unknown_133", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "6"}]}}}}}, {"name": "unknown_134", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "6"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/tf_op_layer_classificators_2/classificators_2/shape", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}}}, {"name": "unknown_131", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_132", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/tf_op_layer_regressors_1/regressors_1/shape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "unknown_93", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "48"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_95", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "48"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_96", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_61", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}}}, {"name": "unknown_63", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "48"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_64", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_57", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_59", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_60", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_53", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_55", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_56", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_49", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}}}, {"name": "unknown_51", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_52", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "unknown_29", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_31", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_32", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "unknown", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "3"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_0", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_3", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}}}, {"name": "unknown_4", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_5", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_7", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_8", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "unknown_9", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_11", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_12", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_13", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_15", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}}}, {"name": "unknown_16", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_17", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}}}, {"name": "unknown_19", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_20", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "unknown_21", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_23", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_24", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "unknown_25", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_27", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_28", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_33", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_35", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}}}, {"name": "unknown_36", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_37", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}}}, {"name": "unknown_39", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}}}, {"name": "unknown_40", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_41", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_43", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_44", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_45", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_47", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_48", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/tf_op_layer_Pad/Pad/paddings", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "unknown_65", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "48"}, {"size": "1"}]}}}}}, {"name": "unknown_67", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "48"}, {"size": "48"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_68", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_69", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "48"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_71", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "48"}, {"size": "48"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_72", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}]}}}}}, {"name": "unknown_73", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "48"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_75", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "48"}, {"size": "48"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_76", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}]}}}}}, {"name": "unknown_77", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "48"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_79", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "48"}, {"size": "48"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_80", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_81", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "48"}, {"size": "1"}]}}}}}, {"name": "unknown_83", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "48"}, {"size": "48"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_84", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}]}}}}}, {"name": "unknown_85", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "48"}, {"size": "1"}]}}}}}, {"name": "unknown_87", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "48"}, {"size": "48"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_88", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}]}}}}}, {"name": "unknown_89", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "48"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_91", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "48"}, {"size": "48"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_92", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/tf_op_layer_Pad_1/Pad_1/paddings", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "unknown_97", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "96"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_99", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "96"}]}}}}}, {"name": "unknown_100", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_101", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "96"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_103", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "96"}]}}}}}, {"name": "unknown_104", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_105", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "96"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_107", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_108", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_109", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "96"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_111", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_112", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_113", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "96"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_115", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_116", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_117", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "96"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_119", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_120", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "unknown_121", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "96"}, {"size": "1"}]}}}}}, {"name": "unknown_123", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_124", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_125", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "96"}, {"size": "1"}]}}}}}, {"name": "unknown_127", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "96"}]}}}}}, {"name": "unknown_128", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_129", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "96"}]}}}}}, {"name": "unknown_130", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/tf_op_layer_regressors_2/regressors_2/shape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "input", "op": "Placeholder", "attr": {"dtype": {"type": "DT_FLOAT"}, "shape": {"shape": {"dim": [{"size": "1"}, {"size": "256"}, {"size": "256"}, {"size": "3"}]}}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d/Relu", "op": "_FusedConv2D", "input": ["input", "unknown", "unknown_0"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "U0FNRQ=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/conv2d/Relu", "unknown_1"], "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_1/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d/depthwise", "unknown_3", "unknown_4"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/functional_1/add/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/conv2d/Relu", "StatefulPartitionedCall/functional_1/conv2d_1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_1/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu/Relu", "unknown_5"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_2/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_1/depthwise", "unknown_7", "unknown_8"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/add_1/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu/Relu", "StatefulPartitionedCall/functional_1/conv2d_2/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_1/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_1/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_2/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_1/Relu", "unknown_9"], "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_3/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_2/depthwise", "unknown_11", "unknown_12"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/functional_1/add_2/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_1/Relu", "StatefulPartitionedCall/functional_1/conv2d_3/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_2/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_2/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_3/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_2/Relu", "unknown_13"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_4/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_3/depthwise", "unknown_15", "unknown_16"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/functional_1/add_3/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_2/Relu", "StatefulPartitionedCall/functional_1/conv2d_4/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_3/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_3/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_4/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_3/Relu", "unknown_17"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_5/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_4/depthwise", "unknown_19", "unknown_20"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}}}, {"name": "StatefulPartitionedCall/functional_1/add_4/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_3/Relu", "StatefulPartitionedCall/functional_1/conv2d_5/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_4/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_4/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_5/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_4/Relu", "unknown_21"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_6/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_5/depthwise", "unknown_23", "unknown_24"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/functional_1/add_5/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_4/Relu", "StatefulPartitionedCall/functional_1/conv2d_6/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_5/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_5/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_6/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_5/Relu", "unknown_25"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_7/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_6/depthwise", "unknown_27", "unknown_28"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/functional_1/add_6/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_5/Relu", "StatefulPartitionedCall/functional_1/conv2d_7/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_6/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_6/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_7/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_6/Relu", "unknown_29"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/functional_1/max_pooling2d/MaxPool", "op": "MaxPool", "input": ["StatefulPartitionedCall/functional_1/re_lu_6/Relu"], "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "ksize": {"list": {"i": ["1", "2", "2", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_8/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_7/depthwise", "unknown_31", "unknown_32"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/functional_1/add_7/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/conv2d_8/BiasAdd", "StatefulPartitionedCall/functional_1/max_pooling2d/MaxPool"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_7/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_7/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_8/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_7/Relu", "unknown_33"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_9/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_8/depthwise", "unknown_35", "unknown_36"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/functional_1/add_8/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_7/Relu", "StatefulPartitionedCall/functional_1/conv2d_9/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_8/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_8/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_9/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_8/Relu", "unknown_37"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_10/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_9/depthwise", "unknown_39", "unknown_40"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/add_9/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_8/Relu", "StatefulPartitionedCall/functional_1/conv2d_10/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_9/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_9/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_10/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_9/Relu", "unknown_41"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_11/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_10/depthwise", "unknown_43", "unknown_44"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/functional_1/add_10/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_9/Relu", "StatefulPartitionedCall/functional_1/conv2d_11/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_10/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_10/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_11/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_10/Relu", "unknown_45"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_12/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_11/depthwise", "unknown_47", "unknown_48"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/functional_1/add_11/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_10/Relu", "StatefulPartitionedCall/functional_1/conv2d_12/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_11/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_11/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_12/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_11/Relu", "unknown_49"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_13/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_12/depthwise", "unknown_51", "unknown_52"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/functional_1/add_12/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/conv2d_13/BiasAdd", "StatefulPartitionedCall/functional_1/re_lu_11/Relu"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_12/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_12/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_13/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_12/Relu", "unknown_53"], "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_14/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_13/depthwise", "unknown_55", "unknown_56"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/functional_1/add_13/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/conv2d_14/BiasAdd", "StatefulPartitionedCall/functional_1/re_lu_12/Relu"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_13/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_13/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_14/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_13/Relu", "unknown_57"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_15/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_14/depthwise", "unknown_59", "unknown_60"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/functional_1/add_14/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/conv2d_15/BiasAdd", "StatefulPartitionedCall/functional_1/re_lu_13/Relu"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_14/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_14/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_15/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_14/Relu", "unknown_61"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/functional_1/max_pooling2d_1/MaxPool", "op": "MaxPool", "input": ["StatefulPartitionedCall/functional_1/re_lu_14/Relu"], "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "ksize": {"list": {"i": ["1", "2", "2", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_16/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_15/depthwise", "unknown_63", "unknown_64"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/functional_1/tf_op_layer_Pad/Pad", "op": "Pad", "input": ["StatefulPartitionedCall/functional_1/max_pooling2d_1/MaxPool", "StatefulPartitionedCall/functional_1/tf_op_layer_Pad/Pad/paddings"], "attr": {"T": {"type": "DT_FLOAT"}, "Tpaddings": {"type": "DT_INT32"}, "_cloned": {"b": true}}}, {"name": "StatefulPartitionedCall/functional_1/add_15/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/conv2d_16/BiasAdd", "StatefulPartitionedCall/functional_1/tf_op_layer_Pad/Pad"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_15/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_15/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_16/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_15/Relu", "unknown_65"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_17/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_16/depthwise", "unknown_67", "unknown_68"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/functional_1/add_16/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_15/Relu", "StatefulPartitionedCall/functional_1/conv2d_17/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_16/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_16/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_17/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_16/Relu", "unknown_69"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_18/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_17/depthwise", "unknown_71", "unknown_72"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}}}, {"name": "StatefulPartitionedCall/functional_1/add_17/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_16/Relu", "StatefulPartitionedCall/functional_1/conv2d_18/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_17/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_17/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_18/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_17/Relu", "unknown_73"], "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_19/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_18/depthwise", "unknown_75", "unknown_76"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/functional_1/add_18/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_17/Relu", "StatefulPartitionedCall/functional_1/conv2d_19/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_18/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_18/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_19/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_18/Relu", "unknown_77"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_20/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_19/depthwise", "unknown_79", "unknown_80"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/functional_1/add_19/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_18/Relu", "StatefulPartitionedCall/functional_1/conv2d_20/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_19/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_19/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_20/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_19/Relu", "unknown_81"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_21/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_20/depthwise", "unknown_83", "unknown_84"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/functional_1/add_20/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_19/Relu", "StatefulPartitionedCall/functional_1/conv2d_21/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_20/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_20/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_21/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_20/Relu", "unknown_85"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_22/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_21/depthwise", "unknown_87", "unknown_88"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/functional_1/add_21/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_20/Relu", "StatefulPartitionedCall/functional_1/conv2d_22/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_21/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_21/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_22/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_21/Relu", "unknown_89"], "attr": {"T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_23/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_22/depthwise", "unknown_91", "unknown_92"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/functional_1/add_22/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_21/Relu", "StatefulPartitionedCall/functional_1/conv2d_23/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_22/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_22/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_23/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_22/Relu", "unknown_93"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/functional_1/max_pooling2d_2/MaxPool", "op": "MaxPool", "input": ["StatefulPartitionedCall/functional_1/re_lu_22/Relu"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "ksize": {"list": {"i": ["1", "2", "2", "1"]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_24/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_23/depthwise", "unknown_95", "unknown_96"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/functional_1/tf_op_layer_Pad_1/Pad_1", "op": "Pad", "input": ["StatefulPartitionedCall/functional_1/max_pooling2d_2/MaxPool", "StatefulPartitionedCall/functional_1/tf_op_layer_Pad_1/Pad_1/paddings"], "attr": {"Tpaddings": {"type": "DT_INT32"}, "_cloned": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/add_23/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/conv2d_24/BiasAdd", "StatefulPartitionedCall/functional_1/tf_op_layer_Pad_1/Pad_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_23/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_23/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_24/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_23/Relu", "unknown_97"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_25/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_24/depthwise", "unknown_99", "unknown_100"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/functional_1/add_24/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_23/Relu", "StatefulPartitionedCall/functional_1/conv2d_25/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_24/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_24/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_25/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_24/Relu", "unknown_101"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_26/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_25/depthwise", "unknown_103", "unknown_104"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/functional_1/add_25/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_24/Relu", "StatefulPartitionedCall/functional_1/conv2d_26/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_25/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_25/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_26/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_25/Relu", "unknown_105"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_27/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_26/depthwise", "unknown_107", "unknown_108"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/functional_1/add_26/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_25/Relu", "StatefulPartitionedCall/functional_1/conv2d_27/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_26/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_26/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_27/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_26/Relu", "unknown_109"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_28/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_27/depthwise", "unknown_111", "unknown_112"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/functional_1/add_27/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_26/Relu", "StatefulPartitionedCall/functional_1/conv2d_28/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_27/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_27/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_28/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_27/Relu", "unknown_113"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_29/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_28/depthwise", "unknown_115", "unknown_116"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}}}, {"name": "StatefulPartitionedCall/functional_1/add_28/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_27/Relu", "StatefulPartitionedCall/functional_1/conv2d_29/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_28/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_28/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_29/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_28/Relu", "unknown_117"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_30/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_29/depthwise", "unknown_119", "unknown_120"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}}}, {"name": "StatefulPartitionedCall/functional_1/add_29/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_28/Relu", "StatefulPartitionedCall/functional_1/conv2d_30/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_29/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_29/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_30/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_29/Relu", "unknown_121"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_31/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_30/depthwise", "unknown_123", "unknown_124"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}}}, {"name": "StatefulPartitionedCall/functional_1/add_30/add", "op": "AddV2", "input": ["StatefulPartitionedCall/functional_1/re_lu_29/Relu", "StatefulPartitionedCall/functional_1/conv2d_31/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_30/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/functional_1/add_30/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_33/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/re_lu_30/Relu", "unknown_135", "unknown_136"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "U0FNRQ=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_35/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/re_lu_30/Relu", "unknown_131", "unknown_132"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "padding": {"s": "U0FNRQ=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/functional_1/depthwise_conv2d_31/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/functional_1/re_lu_30/Relu", "unknown_125"], "attr": {"padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/functional_1/tf_op_layer_classificators_1/classificators_1", "op": "Reshape", "input": ["StatefulPartitionedCall/functional_1/conv2d_33/BiasAdd", "StatefulPartitionedCall/functional_1/tf_op_layer_classificators_1/classificators_1/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}, "_cloned": {"b": true}}}, {"name": "StatefulPartitionedCall/functional_1/tf_op_layer_regressors_1/regressors_1", "op": "Reshape", "input": ["StatefulPartitionedCall/functional_1/conv2d_35/BiasAdd", "StatefulPartitionedCall/functional_1/tf_op_layer_regressors_1/regressors_1/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}, "_cloned": {"b": true}}}, {"name": "StatefulPartitionedCall/functional_1/re_lu_31/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/depthwise_conv2d_31/depthwise", "unknown_127", "unknown_128"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}}}, {"name": "Identity", "op": "Identity", "input": ["StatefulPartitionedCall/functional_1/tf_op_layer_classificators_1/classificators_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity_2", "op": "Identity", "input": ["StatefulPartitionedCall/functional_1/tf_op_layer_regressors_1/regressors_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_36/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/re_lu_31/Relu", "unknown_129", "unknown_130"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/functional_1/conv2d_34/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/functional_1/re_lu_31/Relu", "unknown_133", "unknown_134"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "epsilon": {"f": 0}, "padding": {"s": "U0FNRQ=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/functional_1/tf_op_layer_regressors_2/regressors_2", "op": "Reshape", "input": ["StatefulPartitionedCall/functional_1/conv2d_36/BiasAdd", "StatefulPartitionedCall/functional_1/tf_op_layer_regressors_2/regressors_2/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}, "_cloned": {"b": true}}}, {"name": "StatefulPartitionedCall/functional_1/tf_op_layer_classificators_2/classificators_2", "op": "Reshape", "input": ["StatefulPartitionedCall/functional_1/conv2d_34/BiasAdd", "StatefulPartitionedCall/functional_1/tf_op_layer_classificators_2/classificators_2/shape"], "attr": {"_cloned": {"b": true}, "T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "Identity_3", "op": "Identity", "input": ["StatefulPartitionedCall/functional_1/tf_op_layer_regressors_2/regressors_2"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity_1", "op": "Identity", "input": ["StatefulPartitionedCall/functional_1/tf_op_layer_classificators_2/classificators_2"], "attr": {"T": {"type": "DT_FLOAT"}}}], "library": {}, "versions": {}}, "weightsManifest": [{"paths": ["blazeface.bin"], "weights": [{"name": "unknown_135", "shape": [1, 1, 96, 2], "dtype": "float32"}, {"name": "unknown_136", "shape": [2], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/tf_op_layer_classificators_1/classificators_1/shape", "shape": [3], "dtype": "int32"}, {"name": "unknown_133", "shape": [1, 1, 96, 6], "dtype": "float32"}, {"name": "unknown_134", "shape": [6], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/tf_op_layer_classificators_2/classificators_2/shape", "shape": [3], "dtype": "int32"}, {"name": "unknown_131", "shape": [1, 1, 96, 32], "dtype": "float32"}, {"name": "unknown_132", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/tf_op_layer_regressors_1/regressors_1/shape", "shape": [3], "dtype": "int32"}, {"name": "unknown_93", "shape": [3, 3, 48, 1], "dtype": "float32"}, {"name": "unknown_95", "shape": [1, 1, 48, 96], "dtype": "float32"}, {"name": "unknown_96", "shape": [96], "dtype": "float32"}, {"name": "unknown_61", "shape": [3, 3, 24, 1], "dtype": "float32"}, {"name": "unknown_63", "shape": [1, 1, 24, 48], "dtype": "float32"}, {"name": "unknown_64", "shape": [48], "dtype": "float32"}, {"name": "unknown_57", "shape": [3, 3, 24, 1], "dtype": "float32"}, {"name": "unknown_59", "shape": [1, 1, 24, 24], "dtype": "float32"}, {"name": "unknown_60", "shape": [24], "dtype": "float32"}, {"name": "unknown_53", "shape": [3, 3, 24, 1], "dtype": "float32"}, {"name": "unknown_55", "shape": [1, 1, 24, 24], "dtype": "float32"}, {"name": "unknown_56", "shape": [24], "dtype": "float32"}, {"name": "unknown_49", "shape": [3, 3, 24, 1], "dtype": "float32"}, {"name": "unknown_51", "shape": [1, 1, 24, 24], "dtype": "float32"}, {"name": "unknown_52", "shape": [24], "dtype": "float32"}, {"name": "unknown_29", "shape": [3, 3, 24, 1], "dtype": "float32"}, {"name": "unknown_31", "shape": [1, 1, 24, 24], "dtype": "float32"}, {"name": "unknown_32", "shape": [24], "dtype": "float32"}, {"name": "unknown", "shape": [5, 5, 3, 24], "dtype": "float32"}, {"name": "unknown_0", "shape": [24], "dtype": "float32"}, {"name": "unknown_1", "shape": [3, 3, 24, 1], "dtype": "float32"}, {"name": "unknown_3", "shape": [1, 1, 24, 24], "dtype": "float32"}, {"name": "unknown_4", "shape": [24], "dtype": "float32"}, {"name": "unknown_5", "shape": [3, 3, 24, 1], "dtype": "float32"}, {"name": "unknown_7", "shape": [1, 1, 24, 24], "dtype": "float32"}, {"name": "unknown_8", "shape": [24], "dtype": "float32"}, {"name": "unknown_9", "shape": [3, 3, 24, 1], "dtype": "float32"}, {"name": "unknown_11", "shape": [1, 1, 24, 24], "dtype": "float32"}, {"name": "unknown_12", "shape": [24], "dtype": "float32"}, {"name": "unknown_13", "shape": [3, 3, 24, 1], "dtype": "float32"}, {"name": "unknown_15", "shape": [1, 1, 24, 24], "dtype": "float32"}, {"name": "unknown_16", "shape": [24], "dtype": "float32"}, {"name": "unknown_17", "shape": [3, 3, 24, 1], "dtype": "float32"}, {"name": "unknown_19", "shape": [1, 1, 24, 24], "dtype": "float32"}, {"name": "unknown_20", "shape": [24], "dtype": "float32"}, {"name": "unknown_21", "shape": [3, 3, 24, 1], "dtype": "float32"}, {"name": "unknown_23", "shape": [1, 1, 24, 24], "dtype": "float32"}, {"name": "unknown_24", "shape": [24], "dtype": "float32"}, {"name": "unknown_25", "shape": [3, 3, 24, 1], "dtype": "float32"}, {"name": "unknown_27", "shape": [1, 1, 24, 24], "dtype": "float32"}, {"name": "unknown_28", "shape": [24], "dtype": "float32"}, {"name": "unknown_33", "shape": [3, 3, 24, 1], "dtype": "float32"}, {"name": "unknown_35", "shape": [1, 1, 24, 24], "dtype": "float32"}, {"name": "unknown_36", "shape": [24], "dtype": "float32"}, {"name": "unknown_37", "shape": [3, 3, 24, 1], "dtype": "float32"}, {"name": "unknown_39", "shape": [1, 1, 24, 24], "dtype": "float32"}, {"name": "unknown_40", "shape": [24], "dtype": "float32"}, {"name": "unknown_41", "shape": [3, 3, 24, 1], "dtype": "float32"}, {"name": "unknown_43", "shape": [1, 1, 24, 24], "dtype": "float32"}, {"name": "unknown_44", "shape": [24], "dtype": "float32"}, {"name": "unknown_45", "shape": [3, 3, 24, 1], "dtype": "float32"}, {"name": "unknown_47", "shape": [1, 1, 24, 24], "dtype": "float32"}, {"name": "unknown_48", "shape": [24], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/tf_op_layer_Pad/Pad/paddings", "shape": [4, 2], "dtype": "int32"}, {"name": "unknown_65", "shape": [3, 3, 48, 1], "dtype": "float32"}, {"name": "unknown_67", "shape": [1, 1, 48, 48], "dtype": "float32"}, {"name": "unknown_68", "shape": [48], "dtype": "float32"}, {"name": "unknown_69", "shape": [3, 3, 48, 1], "dtype": "float32"}, {"name": "unknown_71", "shape": [1, 1, 48, 48], "dtype": "float32"}, {"name": "unknown_72", "shape": [48], "dtype": "float32"}, {"name": "unknown_73", "shape": [3, 3, 48, 1], "dtype": "float32"}, {"name": "unknown_75", "shape": [1, 1, 48, 48], "dtype": "float32"}, {"name": "unknown_76", "shape": [48], "dtype": "float32"}, {"name": "unknown_77", "shape": [3, 3, 48, 1], "dtype": "float32"}, {"name": "unknown_79", "shape": [1, 1, 48, 48], "dtype": "float32"}, {"name": "unknown_80", "shape": [48], "dtype": "float32"}, {"name": "unknown_81", "shape": [3, 3, 48, 1], "dtype": "float32"}, {"name": "unknown_83", "shape": [1, 1, 48, 48], "dtype": "float32"}, {"name": "unknown_84", "shape": [48], "dtype": "float32"}, {"name": "unknown_85", "shape": [3, 3, 48, 1], "dtype": "float32"}, {"name": "unknown_87", "shape": [1, 1, 48, 48], "dtype": "float32"}, {"name": "unknown_88", "shape": [48], "dtype": "float32"}, {"name": "unknown_89", "shape": [3, 3, 48, 1], "dtype": "float32"}, {"name": "unknown_91", "shape": [1, 1, 48, 48], "dtype": "float32"}, {"name": "unknown_92", "shape": [48], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/tf_op_layer_Pad_1/Pad_1/paddings", "shape": [4, 2], "dtype": "int32"}, {"name": "unknown_97", "shape": [3, 3, 96, 1], "dtype": "float32"}, {"name": "unknown_99", "shape": [1, 1, 96, 96], "dtype": "float32"}, {"name": "unknown_100", "shape": [96], "dtype": "float32"}, {"name": "unknown_101", "shape": [3, 3, 96, 1], "dtype": "float32"}, {"name": "unknown_103", "shape": [1, 1, 96, 96], "dtype": "float32"}, {"name": "unknown_104", "shape": [96], "dtype": "float32"}, {"name": "unknown_105", "shape": [3, 3, 96, 1], "dtype": "float32"}, {"name": "unknown_107", "shape": [1, 1, 96, 96], "dtype": "float32"}, {"name": "unknown_108", "shape": [96], "dtype": "float32"}, {"name": "unknown_109", "shape": [3, 3, 96, 1], "dtype": "float32"}, {"name": "unknown_111", "shape": [1, 1, 96, 96], "dtype": "float32"}, {"name": "unknown_112", "shape": [96], "dtype": "float32"}, {"name": "unknown_113", "shape": [3, 3, 96, 1], "dtype": "float32"}, {"name": "unknown_115", "shape": [1, 1, 96, 96], "dtype": "float32"}, {"name": "unknown_116", "shape": [96], "dtype": "float32"}, {"name": "unknown_117", "shape": [3, 3, 96, 1], "dtype": "float32"}, {"name": "unknown_119", "shape": [1, 1, 96, 96], "dtype": "float32"}, {"name": "unknown_120", "shape": [96], "dtype": "float32"}, {"name": "unknown_121", "shape": [3, 3, 96, 1], "dtype": "float32"}, {"name": "unknown_123", "shape": [1, 1, 96, 96], "dtype": "float32"}, {"name": "unknown_124", "shape": [96], "dtype": "float32"}, {"name": "unknown_125", "shape": [3, 3, 96, 1], "dtype": "float32"}, {"name": "unknown_127", "shape": [1, 1, 96, 96], "dtype": "float32"}, {"name": "unknown_128", "shape": [96], "dtype": "float32"}, {"name": "unknown_129", "shape": [1, 1, 96, 96], "dtype": "float32"}, {"name": "unknown_130", "shape": [96], "dtype": "float32"}, {"name": "StatefulPartitionedCall/functional_1/tf_op_layer_regressors_2/regressors_2/shape", "shape": [3], "dtype": "int32"}]}]}