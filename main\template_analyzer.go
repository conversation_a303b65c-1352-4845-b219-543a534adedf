package main

import (
	"archive/zip"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/moipa-cn/pptx"
)

// TemplateAnalyzer 模板分析器
type TemplateAnalyzer struct {
	TemplatePath string
	OutputDir    string
}

// NewTemplateAnalyzer 创建新的模板分析器
func NewTemplateAnalyzer(templatePath, outputDir string) *TemplateAnalyzer {
	return &TemplateAnalyzer{
		TemplatePath: templatePath,
		OutputDir:    outputDir,
	}
}

// AnalyzeTemplate 分析PPT模板
func (ta *TemplateAnalyzer) AnalyzeTemplate() (*TemplateAnalysisResult, error) {
	fmt.Printf("🔍 开始分析PPT模板: %s\n", filepath.Base(ta.TemplatePath))

	// 读取PPT文件
	ppt, err := pptx.ReadPowerPoint(ta.TemplatePath)
	if err != nil {
		return nil, fmt.Errorf("读取PPT文件失败: %v", err)
	}

	result := &TemplateAnalysisResult{
		TemplatePath:      ta.TemplatePath,
		Slides:            make([]SlideAnalysis, 0),
		TextPlaceholders:  make([]string, 0),
		ImagePlaceholders: make([]string, 0),
		Placeholders:      make([]PlaceholderElement, 0),
		Styles:            make(map[string]Style),
	}

	// 分析幻灯片
	for _, file := range ppt.Files {
		if strings.HasPrefix(file.Name, "ppt/slides/slide") && strings.HasSuffix(file.Name, ".xml") {
			slideAnalysis, err := ta.analyzeSlide(file)
			if err != nil {
				fmt.Printf("⚠️ 分析幻灯片失败 %s: %v\n", file.Name, err)
				continue
			}
			result.Slides = append(result.Slides, *slideAnalysis)
		}
	}

	// 生成映射配置
	if err := ta.generateMappingConfig(result); err != nil {
		return nil, fmt.Errorf("生成映射配置失败: %v", err)
	}

	fmt.Printf("✅ 模板分析完成，发现 %d 张幻灯片\n", len(result.Slides))
	fmt.Printf("📝 文本占位符: %d 个\n", len(result.TextPlaceholders))
	fmt.Printf("🖼️ 图片占位符: %d 个\n", len(result.ImagePlaceholders))

	return result, nil
}

// analyzeSlide 分析单张幻灯片
func (ta *TemplateAnalyzer) analyzeSlide(file *zip.File) (*SlideAnalysis, error) {
	// 打开文件
	src, err := file.Open()
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %v", err)
	}
	defer src.Close()

	// 读取内容
	content, err := io.ReadAll(src)
	if err != nil {
		return nil, fmt.Errorf("读取文件内容失败: %v", err)
	}

	// 解析XML
	var slide SlideXML
	if err := xml.Unmarshal(content, &slide); err != nil {
		return nil, fmt.Errorf("解析XML失败: %v", err)
	}

	analysis := &SlideAnalysis{
		SlideNumber:  extractSlideNumber(file.Name),
		Texts:        make([]TextElement, 0),
		Images:       make([]ImageElement, 0),
		Shapes:       make([]ShapeElement, 0),
		Placeholders: make([]PlaceholderElement, 0),
	}

	// 分析文本元素
	ta.analyzeTextElements(&slide, analysis)

	// 分析图片元素
	ta.analyzeImageElements(&slide, analysis)

	// 分析形状元素
	ta.analyzeShapeElements(&slide, analysis)

	// 分析占位符
	ta.analyzePlaceholders(&slide, analysis)

	return analysis, nil
}

// analyzeTextElements 分析文本元素
func (ta *TemplateAnalyzer) analyzeTextElements(slide *SlideXML, analysis *SlideAnalysis) {
	// 首先尝试查找完整的占位符模式 {{占位符}}
	placeholderPattern := regexp.MustCompile(`\{\{([^}]+)\}\}`)
	placeholderMatches := placeholderPattern.FindAllStringSubmatch(slide.Content, -1)

	fmt.Printf("🔍 在XML中找到 %d 个完整占位符\n", len(placeholderMatches))

	// 处理完整占位符
	for i, match := range placeholderMatches {
		if len(match) > 1 {
			placeholderName := match[1]
			analysis.Placeholders = append(analysis.Placeholders, PlaceholderElement{
				ID:          fmt.Sprintf("text_placeholder_%d", i),
				Type:        "text",
				Placeholder: placeholderName,
				X:           0, // TODO: 从XML中提取位置信息
				Y:           0,
				Width:       100,
				Height:      50,
			})
			fmt.Printf("  📝 发现占位符: %s\n", placeholderName)
		}
	}

	// 如果没找到完整占位符，尝试查找分割的占位符
	if len(placeholderMatches) == 0 {
		// 查找所有文本元素
		textPattern := regexp.MustCompile(`<a:t[^>]*>([^<]+)</a:t>`)
		matches := textPattern.FindAllStringSubmatch(slide.Content, -1)

		fmt.Printf("🔍 找到 %d 个文本元素\n", len(matches))

		// 尝试重建占位符
		var currentPlaceholder strings.Builder
		var inPlaceholder bool

		for i, match := range matches {
			if len(match) > 1 {
				text := match[1]
				fmt.Printf("  📄 文本元素 %d: '%s'\n", i, text)

				if text == "{{" {
					inPlaceholder = true
					currentPlaceholder.Reset()
				} else if text == "}}" && inPlaceholder {
					placeholderName := strings.TrimSpace(currentPlaceholder.String())
					if placeholderName != "" {
						analysis.Placeholders = append(analysis.Placeholders, PlaceholderElement{
							ID:          fmt.Sprintf("text_placeholder_%d", len(analysis.Placeholders)),
							Type:        "text",
							Placeholder: placeholderName,
							X:           0,
							Y:           0,
							Width:       100,
							Height:      50,
						})
						fmt.Printf("  📝 重建占位符: %s\n", placeholderName)
					}
					inPlaceholder = false
				} else if inPlaceholder {
					currentPlaceholder.WriteString(text)
				} else {
					// 检查是否为占位符
					if ta.isPlaceholder(text) {
						analysis.Placeholders = append(analysis.Placeholders, PlaceholderElement{
							ID:          fmt.Sprintf("text_placeholder_%d", len(analysis.Placeholders)),
							Type:        "text",
							Placeholder: text,
							X:           0, // TODO: 从XML中提取位置信息
							Y:           0,
							Width:       100,
							Height:      50,
						})
						fmt.Printf("  📝 发现占位符: %s\n", text)
					} else {
						analysis.Texts = append(analysis.Texts, TextElement{
							ID:       fmt.Sprintf("text_%d", len(analysis.Texts)),
							Content:  text,
							X:        0, // TODO: 从XML中提取位置信息
							Y:        0,
							Width:    100,
							Height:   50,
							FontSize: 12,
						})
					}
				}
			}
		}
	}
}

// analyzeImageElements 分析图片元素
func (ta *TemplateAnalyzer) analyzeImageElements(slide *SlideXML, analysis *SlideAnalysis) {
	// 查找图片元素
	imagePattern := regexp.MustCompile(`<a:pic[^>]*>.*?<a:blip[^>]*r:embed="([^"]+)"[^>]*>.*?</a:pic>`)
	matches := imagePattern.FindAllStringSubmatch(slide.Content, -1)

	for i, match := range matches {
		if len(match) > 1 {
			imageRef := match[1]
			analysis.Images = append(analysis.Images, ImageElement{
				ID:         fmt.Sprintf("image_%d", i),
				Path:       imageRef,
				X:          0, // TODO: 从XML中提取位置信息
				Y:          0,
				Width:      200,
				Height:     150,
				KeepAspect: true,
			})
		}
	}
}

// analyzeShapeElements 分析形状元素
func (ta *TemplateAnalyzer) analyzeShapeElements(slide *SlideXML, analysis *SlideAnalysis) {
	// 查找形状元素
	shapePattern := regexp.MustCompile(`<a:sp[^>]*>.*?<a:prstGeom[^>]*prst="([^"]+)"[^>]*>.*?</a:sp>`)
	matches := shapePattern.FindAllStringSubmatch(slide.Content, -1)

	for i, match := range matches {
		if len(match) > 1 {
			shapeType := match[1]
			analysis.Shapes = append(analysis.Shapes, ShapeElement{
				ID:     fmt.Sprintf("shape_%d", i),
				Type:   shapeType,
				X:      0, // TODO: 从XML中提取位置信息
				Y:      0,
				Width:  100,
				Height: 100,
			})
		}
	}
}

// analyzePlaceholders 分析占位符
func (ta *TemplateAnalyzer) analyzePlaceholders(slide *SlideXML, analysis *SlideAnalysis) {
	// 查找占位符元素
	placeholderPattern := regexp.MustCompile(`<p:ph[^>]*type="([^"]+)"[^>]*>`)
	matches := placeholderPattern.FindAllStringSubmatch(slide.Content, -1)

	for i, match := range matches {
		if len(match) > 1 {
			placeholderType := match[1]
			analysis.Placeholders = append(analysis.Placeholders, PlaceholderElement{
				ID:          fmt.Sprintf("placeholder_%d", i),
				Type:        placeholderType,
				Placeholder: fmt.Sprintf("{{%s}}", placeholderType),
				X:           0, // TODO: 从XML中提取位置信息
				Y:           0,
				Width:       200,
				Height:      150,
			})
		}
	}
}

// isPlaceholder 检查文本是否为占位符
func (ta *TemplateAnalyzer) isPlaceholder(text string) bool {
	// 检查常见的占位符模式
	patterns := []string{
		`\{\{[^}]+\}\}`,
		`\[[^\]]+\]`,
		`\([^)]+\)`,
		`<[^>]+>`,
	}

	for _, pattern := range patterns {
		matched, _ := regexp.MatchString(pattern, text)
		if matched {
			return true
		}
	}

	// 检查常见的占位符关键词
	keywords := []string{
		"姓名", "名字", "name",
		"医院", "hospital",
		"科室", "department",
		"职称", "title",
		"介绍", "description",
		"头像", "photo", "picture",
		"标题", "title",
		"内容", "content",
		"日期", "date",
		"时间", "time",
	}

	lowerText := strings.ToLower(text)
	for _, keyword := range keywords {
		if strings.Contains(lowerText, strings.ToLower(keyword)) {
			return true
		}
	}

	return false
}

// extractSlideNumber 从文件名提取幻灯片编号
func extractSlideNumber(filename string) int {
	// 从 "ppt/slides/slide1.xml" 提取 "1"
	pattern := regexp.MustCompile(`slide(\d+)\.xml`)
	match := pattern.FindStringSubmatch(filename)
	if len(match) > 1 {
		// 这里简化处理，实际应该解析数字
		return 1
	}
	return 1
}

// generateMappingConfig 生成映射配置文件
func (ta *TemplateAnalyzer) generateMappingConfig(result *TemplateAnalysisResult) error {
	// 创建templates目录
	templatesDir := "templates"
	if err := os.MkdirAll(templatesDir, 0755); err != nil {
		return fmt.Errorf("创建templates目录失败: %v", err)
	}

	// 生成映射配置
	mapping := &TemplateMapping{
		TextPlaceholders:  make(map[string]string),
		ImagePlaceholders: make(map[string]string),
		Styles:            make(map[string]Style),
	}

	// 添加文本占位符映射
	for _, slide := range result.Slides {
		for _, placeholder := range slide.Placeholders {
			if placeholder.Type == "text" {
				// 使用占位符名称作为键，空字符串作为默认值
				mapping.TextPlaceholders[placeholder.Placeholder] = ""
				fmt.Printf("📝 添加文本占位符映射: %s\n", placeholder.Placeholder)
			}
		}
	}

	// 添加图片占位符映射
	for _, slide := range result.Slides {
		for _, placeholder := range slide.Placeholders {
			if placeholder.Type == "image" || strings.Contains(placeholder.Placeholder, "头像") {
				mapping.ImagePlaceholders[placeholder.Placeholder] = ""
				fmt.Printf("🖼️ 添加图片占位符映射: %s\n", placeholder.Placeholder)
			}
		}
	}

	// 添加默认样式
	mapping.Styles["default"] = Style{
		FontSize:   12,
		FontFamily: "微软雅黑",
		Color:      "#000000",
		Bold:       false,
		Italic:     false,
	}

	// 保存映射配置
	mappingPath := filepath.Join(templatesDir, "template_mapping.json")
	mappingData, err := json.MarshalIndent(mapping, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化映射配置失败: %v", err)
	}

	if err := os.WriteFile(mappingPath, mappingData, 0644); err != nil {
		return fmt.Errorf("保存映射配置失败: %v", err)
	}

	fmt.Printf("📝 映射配置已保存到: %s\n", mappingPath)
	fmt.Printf("📊 映射配置统计: %d 个文本占位符, %d 个图片占位符\n",
		len(mapping.TextPlaceholders), len(mapping.ImagePlaceholders))
	return nil
}

// TemplateAnalysisResult 模板分析结果
type TemplateAnalysisResult struct {
	TemplatePath      string               `json:"template_path"`
	Slides            []SlideAnalysis      `json:"slides"`
	TextPlaceholders  []string             `json:"text_placeholders"`
	ImagePlaceholders []string             `json:"image_placeholders"`
	Placeholders      []PlaceholderElement `json:"placeholders"`
	Styles            map[string]Style     `json:"styles"`
}

// SlideAnalysis 幻灯片分析结果
type SlideAnalysis struct {
	SlideNumber  int                  `json:"slide_number"`
	Texts        []TextElement        `json:"texts"`
	Images       []ImageElement       `json:"images"`
	Shapes       []ShapeElement       `json:"shapes"`
	Placeholders []PlaceholderElement `json:"placeholders"`
}

// SlideXML PPT幻灯片XML结构（简化版）
type SlideXML struct {
	Content string `xml:",innerxml"`
}
