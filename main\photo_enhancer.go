package main

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"image"
	"image/color"
	"image/draw"
	"image/jpeg"
	"image/png"
	"io/ioutil"
	"math"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	iai "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/iai/v20200303"
	"github.com/tencentyun/cos-go-sdk-v5"
)

// PhotoEnhancer 照片矫正处理器
type PhotoEnhancer struct {
	config    *Config
	cosClient *cos.Client
}

// NewPhotoEnhancer 创建照片矫正处理器
func NewPhotoEnhancer(config *Config, cosClient *cos.Client) *PhotoEnhancer {
	return &PhotoEnhancer{
		config:    config,
		cosClient: cosClient,
	}
}

// EnhanceImage 增强单张图片 (可选择标准化处理)
func (e *PhotoEnhancer) EnhanceImage(inputPath, outputDir string) (*ImageEnhancementResult, error) {
	startTime := time.Now()

	fmt.Printf("🔧 开始处理图片: %s\n", filepath.Base(inputPath))

	// 检查是否启用智能标准化
	if e.config.PhotoEnhancement.EnablePortraitStandardization {
		fmt.Printf("🎯 启用智能头像标准化模式\n")
		return e.StandardizePortrait(inputPath, outputDir)
	}

	// 检查是否为AI超分处理后的图片（避免二次增强）
	isAISuperRes := strings.Contains(filepath.Base(inputPath), "_ai_superres")
	if isAISuperRes {
		fmt.Printf("🤖 检测到AI超分图片，跳过传统增强步骤\n")
		return e.processAISuperResImage(inputPath, outputDir)
	}

	// 原有的处理流程（保持向后兼容）
	// 0. 读取原图，判断分辨率/DPI是否过低，必要时增强
	imageData, err := os.ReadFile(inputPath)
	if err != nil {
		return nil, fmt.Errorf("读取图片失败: %v", err)
	}
	img, _, err := image.Decode(bytes.NewReader(imageData))
	if err != nil {
		return nil, fmt.Errorf("图片解码失败: %v", err)
	}
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()
	originalSizeMB := float64(len(imageData)) / (1024 * 1024)
	estimatedDPI := e.estimateDPI(width, height, originalSizeMB)
	minDPI := e.config.PhotoEnhancement.MinDPI

	needEnhance := false
	if estimatedDPI < minDPI {
		fmt.Printf("⚠️  图片DPI过低（%d < %d），将使用腾讯云AI图像超分API进行专业增强\n", estimatedDPI, minDPI)
		needEnhance = true
	}
	if width < 400 || height < 500 {
		fmt.Printf("⚠️  图片分辨率过低（%dx%d），将使用腾讯云AI图像超分API进行专业增强\n", width, height)
		needEnhance = true
	}

	if needEnhance {
		// 使用腾讯云AI图像超分API进行专业增强
		imageData, err = e.enhanceImageWithTencentAI(imageData)
		if err != nil {
			return nil, fmt.Errorf("AI图像超分失败: %v", err)
		}
		// 保存AI超分后图片到输出目录，便于人工检查
		enhancedDetectPath := filepath.Join(outputDir, strings.TrimSuffix(filepath.Base(inputPath), filepath.Ext(inputPath))+"_ai_superres.png")
		_ = os.WriteFile(enhancedDetectPath, imageData, 0644)
		// 重新解码增强后的图片，更新width/height/estimatedDPI
		img, _, err = image.Decode(bytes.NewReader(imageData))
		if err != nil {
			return nil, fmt.Errorf("AI超分后图片解码失败: %v", err)
		}
		bounds = img.Bounds()
		width = bounds.Dx()
		height = bounds.Dy()
		originalSizeMB = float64(len(imageData)) / (1024 * 1024)
		estimatedDPI = e.estimateDPI(width, height, originalSizeMB)
		fmt.Printf("✅ AI超分后图片尺寸: %dx%d, 大小: %.2fMB, DPI: %d\n", width, height, originalSizeMB, estimatedDPI)
		// 临时保存AI超分图片到临时文件，供后续压缩/上传
		tmpEnhancedPath := filepath.Join(outputDir, "_tmp_ai_superres_"+filepath.Base(inputPath))
		err = os.WriteFile(tmpEnhancedPath, imageData, 0644)
		if err == nil {
			inputPath = tmpEnhancedPath
		}
	}

	// 1. 读取并优化图片（与人像检测使用相同的压缩逻辑）
	optimizedImageData, compressionInfo, err := e.compressImageForAPI(inputPath)
	if err != nil {
		return nil, fmt.Errorf("图片优化失败: %v", err)
	}

	// 显示压缩信息
	if compressionInfo.WasCompressed {
		fmt.Printf("📦 抠图前图片优化: %.2fMB -> %.2fMB\n",
			compressionInfo.OriginalSizeMB, compressionInfo.CompressedSizeMB)
	}

	_, _, err = image.Decode(bytes.NewReader(optimizedImageData))
	if err != nil {
		return nil, fmt.Errorf("解码优化后图片失败: %v", err)
	}

	originalSize := Size{
		Width:  compressionInfo.ImageDimensions.Width,
		Height: compressionInfo.ImageDimensions.Height,
	}

	// Upload optimized image to COS for CI processing
	cosFileName := fmt.Sprintf("enhance_temp_%d%s", time.Now().UnixNano(), filepath.Ext(inputPath))
	_, err = e.cosClient.Object.Put(context.Background(), cosFileName, bytes.NewReader(optimizedImageData), nil)
	if err != nil {
		return nil, fmt.Errorf("上传优化图片到COS失败: %v", err)
	}
	fmt.Printf("✅ 优化图片已上传到COS进行增强处理: %s\n", cosFileName)

	// 2. 人像分割（背景去除）
	cutoutPath := filepath.Join(outputDir,
		strings.TrimSuffix(filepath.Base(inputPath), filepath.Ext(inputPath))+"_cutout.png")

	err = e.removeBackground(cosFileName, cutoutPath)
	if err != nil {
		fmt.Printf("⚠️  人像分割失败: %v\n", err)
		cutoutPath = ""
	} else {
		fmt.Printf("✂️  人像分割完成: %s\n", filepath.Base(cutoutPath))
	}

	processingTime := time.Since(startTime).Milliseconds()

	result := &ImageEnhancementResult{
		OriginalPath:   inputPath,
		EnhancedPath:   inputPath, // 保持原始路径
		OriginalSize:   originalSize,
		EnhancedSize:   originalSize, // 保持原始尺寸
		RotationAngle:  0.0,
		SkewCorrection: false,
		FaceCentered:   false,
		QualityScore:   0.0,
		ProcessingTime: processingTime,
		CutoutPath:     cutoutPath,
	}

	fmt.Printf("✅ 图片处理完成: %s (耗时: %dms)\n", filepath.Base(inputPath), processingTime)

	return result, nil
}

// processAISuperResImage 专门处理AI超分图片（跳过传统增强）
func (e *PhotoEnhancer) processAISuperResImage(inputPath, outputDir string) (*ImageEnhancementResult, error) {
	startTime := time.Now()

	fmt.Printf("🚀 直接处理AI超分图片: %s\n", filepath.Base(inputPath))

	// 读取AI超分图片
	imageData, err := os.ReadFile(inputPath)
	if err != nil {
		return nil, fmt.Errorf("读取AI超分图片失败: %v", err)
	}

	img, _, err := image.Decode(bytes.NewReader(imageData))
	if err != nil {
		return nil, fmt.Errorf("AI超分图片解码失败: %v", err)
	}

	bounds := img.Bounds()
	originalSize := Size{
		Width:  bounds.Dx(),
		Height: bounds.Dy(),
	}

	fmt.Printf("📊 AI超分图片信息: %dx%d, %.2fMB\n",
		originalSize.Width, originalSize.Height, float64(len(imageData))/(1024*1024))

	// 直接进行图片压缩优化（如果需要）
	optimizedImageData, compressionInfo, err := e.compressImageForAPI(inputPath)
	if err != nil {
		return nil, fmt.Errorf("AI超分图片优化失败: %v", err)
	}

	// 显示压缩信息
	if compressionInfo.WasCompressed {
		fmt.Printf("📦 AI超分图片优化: %.2fMB -> %.2fMB\n",
			compressionInfo.OriginalSizeMB, compressionInfo.CompressedSizeMB)
	} else {
		fmt.Printf("✅ AI超分图片无需压缩，直接使用\n")
	}

	// 上传到COS进行抠图处理
	cosFileName := fmt.Sprintf("ai_superres_temp_%d%s", time.Now().UnixNano(), filepath.Ext(inputPath))
	_, err = e.cosClient.Object.Put(context.Background(), cosFileName, bytes.NewReader(optimizedImageData), nil)
	if err != nil {
		return nil, fmt.Errorf("上传AI超分图片到COS失败: %v", err)
	}
	fmt.Printf("✅ AI超分图片已上传到COS进行抠图: %s\n", cosFileName)

	// 进行人像分割（背景去除）
	cutoutPath := filepath.Join(outputDir,
		strings.TrimSuffix(filepath.Base(inputPath), filepath.Ext(inputPath))+"_cutout.png")

	err = e.removeBackground(cosFileName, cutoutPath)
	if err != nil {
		fmt.Printf("⚠️  AI超分图片抠图失败: %v\n", err)
		cutoutPath = ""
	} else {
		fmt.Printf("✂️  AI超分图片抠图完成: %s\n", filepath.Base(cutoutPath))
	}

	processingTime := time.Since(startTime).Milliseconds()

	result := &ImageEnhancementResult{
		OriginalPath:   inputPath,
		EnhancedPath:   inputPath, // AI超分图片本身就是增强版本
		OriginalSize:   originalSize,
		EnhancedSize:   originalSize,
		RotationAngle:  0.0,
		SkewCorrection: false,
		FaceCentered:   false,
		QualityScore:   0.0,
		ProcessingTime: processingTime,
		CutoutPath:     cutoutPath,
	}

	fmt.Printf("✅ AI超分图片处理完成: %s (耗时: %dms)\n", filepath.Base(inputPath), processingTime)

	return result, nil
}

// EnhanceImages 批量增强图片
func (e *PhotoEnhancer) EnhanceImages(inputPaths []string, outputDir string) ([]*ImageEnhancementResult, error) {
	var results []*ImageEnhancementResult

	fmt.Printf("🚀 开始批量增强 %d 张图片\n", len(inputPaths))

	for i, inputPath := range inputPaths {
		fmt.Printf("\n📸 处理图片 %d/%d: %s\n", i+1, len(inputPaths), filepath.Base(inputPath))

		result, err := e.EnhanceImage(inputPath, outputDir)
		if err != nil {
			fmt.Printf("❌ 图片增强失败: %v\n", err)
			continue
		}

		results = append(results, result)
	}

	fmt.Printf("\n🎉 批量增强完成，成功处理 %d/%d 张图片\n", len(results), len(inputPaths))

	return results, nil
}

// 检测人脸位置 - 使用腾讯云人脸识别API（SDK方式）
func (e *PhotoEnhancer) detectFace(imagePath string) (*FaceInfo, error) {
	imageData, err := ioutil.ReadFile(imagePath)
	if err != nil {
		return nil, err
	}
	credential := common.NewCredential(e.config.TencentSecretID, e.config.TencentSecretKey)
	cpf := profile.NewClientProfile()
	client, err := iai.NewClient(credential, e.config.TencentRegion, cpf)
	if err != nil {
		return nil, fmt.Errorf("创建腾讯云人脸识别客户端失败: %v", err)
	}
	request := iai.NewDetectFaceRequest()
	request.Image = common.StringPtr(base64.StdEncoding.EncodeToString(imageData))
	request.MaxFaceNum = common.Uint64Ptr(1)
	request.NeedFaceAttributes = common.Uint64Ptr(0)
	response, err := client.DetectFace(request)
	if err != nil {
		return nil, fmt.Errorf("人脸检测API调用失败: %v", err)
	}
	if response.Response.FaceInfos != nil && len(response.Response.FaceInfos) > 0 {
		faceInfo := response.Response.FaceInfos[0]
		return &FaceInfo{
			X:          int(*faceInfo.X),
			Y:          int(*faceInfo.Y),
			Width:      int(*faceInfo.Width),
			Height:     int(*faceInfo.Height),
			Confidence: 0.9, // SDK未返回置信度时可设默认
		}, nil
	}
	return nil, fmt.Errorf("未检测到人脸")
}

// 加载图片
func (e *PhotoEnhancer) loadImage(path string) (image.Image, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	img, _, err := image.Decode(file)
	if err != nil {
		return nil, err
	}

	return img, nil
}

// 保存图片
func (e *PhotoEnhancer) saveImage(img image.Image, path string) error {
	file, err := os.Create(path)
	if err != nil {
		return err
	}
	defer file.Close()

	ext := strings.ToLower(filepath.Ext(path))
	switch ext {
	case ".jpg", ".jpeg":
		return jpeg.Encode(file, img, &jpeg.Options{Quality: e.config.PhotoEnhancement.Quality})
	case ".png":
		return png.Encode(file, img)
	default:
		return png.Encode(file, img)
	}
}

// 检测图片歪斜角度
func (e *PhotoEnhancer) detectSkew(img image.Image) (float64, error) {
	// 简化的歪斜检测算法
	bounds := img.Bounds()
	width, height := bounds.Dx(), bounds.Dy()

	// 转换为灰度图
	gray := image.NewGray(bounds)
	draw.Draw(gray, bounds, img, bounds.Min, draw.Src)

	// 检测水平线
	var angles []float64
	for y := 0; y < height; y += 10 {
		var linePoints []int
		for x := 0; x < width; x++ {
			if gray.GrayAt(x, y).Y > 128 {
				linePoints = append(linePoints, x)
			}
		}

		if len(linePoints) > width/4 {
			// 计算这条线的角度
			if len(linePoints) > 1 {
				angle := math.Atan2(float64(linePoints[len(linePoints)-1]-linePoints[0]), float64(width))
				angles = append(angles, angle*180/math.Pi)
			}
		}
	}

	if len(angles) == 0 {
		return 0, fmt.Errorf("无法检测到明显的水平线")
	}

	// 计算平均角度
	var sum float64
	for _, angle := range angles {
		sum += angle
	}
	avgAngle := sum / float64(len(angles))

	return avgAngle, nil
}

// 旋转图片
func (e *PhotoEnhancer) rotateImage(img image.Image, angle float64) image.Image {
	bounds := img.Bounds()
	width, height := bounds.Dx(), bounds.Dy()

	// 计算旋转后的尺寸
	rad := angle * math.Pi / 180
	cos := math.Cos(rad)
	sin := math.Sin(rad)

	newWidth := int(math.Abs(float64(width)*cos) + math.Abs(float64(height)*sin))
	newHeight := int(math.Abs(float64(width)*sin) + math.Abs(float64(height)*cos))

	rotated := image.NewRGBA(image.Rect(0, 0, newWidth, newHeight))

	// 像素复制
	for y := 0; y < newHeight; y++ {
		for x := 0; x < newWidth; x++ {
			// 计算原图坐标
			oldX := int(float64(x-newWidth/2)*cos+float64(y-newHeight/2)*sin) + width/2
			oldY := int(float64(y-newHeight/2)*cos-float64(x-newWidth/2)*sin) + height/2

			if oldX >= 0 && oldX < width && oldY >= 0 && oldY < height {
				rotated.Set(x, y, img.At(oldX, oldY))
			}
		}
	}

	return rotated
}

// repositionImage 将人脸图片居中
func (e *PhotoEnhancer) repositionImage(img image.Image, faceInfo *FaceInfo) image.Image {
	// Implement repositioning logic if needed
	return img
}

// resizeImage 调整图片大小
func (e *PhotoEnhancer) resizeImage(img image.Image) image.Image {
	// Implement resize logic if needed
	return img
}

// resizeForPPT 调整图片大小以适应PPT
func (e *PhotoEnhancer) resizeForPPT(img image.Image) image.Image {
	// Implement resize logic if needed
	return img
}

// calculateQualityScore 计算图片质量得分
func (e *PhotoEnhancer) calculateQualityScore(img image.Image) float64 {
	// 简化的质量评分：基于图片亮度、对比度、清晰度
	bounds := img.Bounds()
	width, height := bounds.Dx(), bounds.Dy()

	if width == 0 || height == 0 {
		return 0.0
	}

	// 转换为灰度图
	gray := image.NewGray(bounds)
	draw.Draw(gray, bounds, img, bounds.Min, draw.Src)

	// 计算平均亮度
	var sumBrightness float64
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			sumBrightness += float64(gray.GrayAt(x, y).Y) / 255.0
		}
	}
	avgBrightness := sumBrightness / float64(width*height)

	// 计算对比度（标准差）
	var sumSquaredDiff float64
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			diff := float64(gray.GrayAt(x, y).Y)/255.0 - avgBrightness
			sumSquaredDiff += diff * diff
		}
	}
	contrast := math.Sqrt(sumSquaredDiff / float64(width*height))

	// 综合评分 (0-100)
	clarityScore := math.Min(100, float64(width*height)/10000) // 分辨率评分
	brightnessScore := (1 - math.Abs(avgBrightness-0.5)) * 100 // 亮度评分
	contrastScore := math.Min(100, contrast*100)               // 对比度评分

	qualityScore := (clarityScore + brightnessScore + contrastScore) / 3

	return math.Max(0, math.Min(100, qualityScore))
}

// 人像抠图（背景去除）
func (e *PhotoEnhancer) removeBackground(cosFileName, outputPath string) error {
	fmt.Printf("🔍 开始智能多策略抠图: %s\n", filepath.Base(cosFileName))

	// 策略1：直接抠图（标准方式）
	fmt.Printf("🎯 策略1: 标准人像抠图\n")
	err := e.performCutout(cosFileName, outputPath)
	if err == nil {
		// 检查抠图质量
		quality := e.evaluateCutoutQuality(outputPath)
		fmt.Printf("📊 抠图质量评分: %.2f\n", quality)

		if quality >= 0.8 { // 质量足够好
			fmt.Printf("✅ 策略1成功，抠图质量良好\n")
			return nil
		} else {
			fmt.Printf("⚠️  策略1质量不佳(%.2f)，尝试其他策略\n", quality)
		}
	} else {
		fmt.Printf("❌ 策略1失败: %v\n", err)
	}

	// 策略2：对比度增强后抠图（针对白色衣服问题）
	fmt.Printf("🎯 策略2: 对比度增强抠图（白衣服优化）\n")
	err = e.contrastEnhancedCutout(cosFileName, outputPath)
	if err == nil {
		quality := e.evaluateCutoutQuality(outputPath)
		fmt.Printf("📊 增强后抠图质量评分: %.2f\n", quality)

		if quality >= 0.7 {
			fmt.Printf("✅ 策略2成功，对比度增强有效\n")
			return nil
		} else {
			fmt.Printf("⚠️  策略2质量仍不理想(%.2f)\n", quality)
		}
	} else {
		fmt.Printf("❌ 策略2失败: %v\n", err)
	}

	// 策略3：边缘增强后抠图
	fmt.Printf("🎯 策略3: 边缘增强抠图\n")
	err = e.edgeEnhancedCutout(cosFileName, outputPath)
	if err == nil {
		quality := e.evaluateCutoutQuality(outputPath)
		fmt.Printf("📊 边缘增强抠图质量评分: %.2f\n", quality)

		if quality >= 0.6 {
			fmt.Printf("✅ 策略3成功，边缘增强有效\n")
			return nil
		}
	} else {
		fmt.Printf("❌ 策略3失败: %v\n", err)
	}

	// 策略4：多参数尝试
	fmt.Printf("🎯 策略4: 多参数组合尝试\n")
	err = e.multiParameterCutout(cosFileName, outputPath)
	if err == nil {
		fmt.Printf("✅ 策略4成功，多参数优化有效\n")
		return nil
	}

	// 如果所有策略都失败，返回最后一个错误
	return fmt.Errorf("所有抠图策略均失败，最后错误: %v", err)
}

// performCutout 执行实际的抠图操作
func (e *PhotoEnhancer) performCutout(cosFileName, outputPath string) error {
	// 使用COS SDK调用AIPortraitMatting接口
	opt := &cos.AIPortraitMattingOptions{} // 创建一个空的选项结构体，不设置CenterLayout和PaddingLayout

	resp, err := e.cosClient.CI.AIPortraitMatting(context.Background(), cosFileName, opt)
	if err != nil {
		return fmt.Errorf("调用人像抠图API失败: %v", err)
	}

	// 解析API响应
	if resp != nil && resp.Body != nil {
		imgDataBytes, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			return fmt.Errorf("读取抠图结果失败: %v", err)
		}
		defer resp.Body.Close() // 确保关闭响应体

		if len(imgDataBytes) == 0 {
			return fmt.Errorf("人像抠图API返回结果为空")
		}

		// 1. 解码抠图后的图片（默认为PNG格式）
		img, err := png.Decode(bytes.NewReader(imgDataBytes))
		if err != nil {
			return fmt.Errorf("解码抠图图片失败: %v", err)
		}
		fmt.Printf("DEBUG: API返回原始图片尺寸: %dx%d\n", img.Bounds().Dx(), img.Bounds().Dy())

		// 将image.Image转换为*image.RGBA，确保可以进行像素操作
		rgbaImg := image.NewRGBA(img.Bounds())
		draw.Draw(rgbaImg, rgbaImg.Bounds(), img, img.Bounds().Min, draw.Src)

		// 2. 查找人像的非透明区域边界
		bounds := rgbaImg.Bounds()
		minX, minY := bounds.Max.X, bounds.Max.Y
		maxX, maxY := bounds.Min.X, bounds.Min.Y

		for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
			for x := bounds.Min.X; x < bounds.Max.X; x++ {
				_, _, _, a := rgbaImg.At(x, y).RGBA()
				if a > 0 { // 如果不是完全透明
					if x < minX {
						minX = x
					}
					if y < minY {
						minY = y
					}
					if x > maxX {
						maxX = x
					}
					if y > maxY {
						maxY = y
					}
				}
			}
		}

		if minX > maxX || minY > maxY { // 未检测到有效人像像素
			return fmt.Errorf("抠图结果中未检测到有效人像像素，无法进行处理")
		}

		personRect := image.Rect(minX, minY, maxX+1, maxY+1) // +1因为maxX, maxY是包含坐标
		fmt.Printf("DEBUG: 检测到人像区域尺寸: %dx%d (MinY:%d, MaxY:%d)\n", personRect.Dx(), personRect.Dy(), personRect.Min.Y, personRect.Max.Y)

		// 3. 尝试检测人脸位置以进行智能头肩裁剪
		faceRect, err := e.detectFaceInImage(rgbaImg, personRect)
		var croppedImg image.Image
		var adjustedFaceRect *image.Rectangle // 调整后的人脸坐标（相对于裁剪图像）

		if err != nil || faceRect == nil {
			fmt.Printf("💡 未检测到人脸，使用完整人像: %v\n", err)
			// 如果人脸检测失败，使用完整人像
			croppedImg = rgbaImg.SubImage(personRect)
			adjustedFaceRect = nil
		} else {
			fmt.Printf("✅ 检测到人脸位置: %dx%d at (%d,%d)\n", faceRect.Dx(), faceRect.Dy(), faceRect.Min.X, faceRect.Min.Y)
			// 基于人脸位置计算智能头肩裁剪区域
			headShoulderRect := e.calculateHeadShoulderRegion(faceRect, personRect, rgbaImg.Bounds())
			fmt.Printf("📐 智能头肩裁剪区域: %dx%d at (%d,%d)\n", headShoulderRect.Dx(), headShoulderRect.Dy(), headShoulderRect.Min.X, headShoulderRect.Min.Y)
			croppedImg = rgbaImg.SubImage(headShoulderRect)

			// 重要：计算人脸在裁剪后图像中的新坐标
			adjustedFaceX := faceRect.Min.X - headShoulderRect.Min.X
			adjustedFaceY := faceRect.Min.Y - headShoulderRect.Min.Y
			adjustedFaceRect = &image.Rectangle{
				Min: image.Point{X: adjustedFaceX, Y: adjustedFaceY},
				Max: image.Point{X: adjustedFaceX + faceRect.Dx(), Y: adjustedFaceY + faceRect.Dy()},
			}
			fmt.Printf("🔄 调整后人脸坐标: %dx%d at (%d,%d) (相对于裁剪图像)\n",
				adjustedFaceRect.Dx(), adjustedFaceRect.Dy(), adjustedFaceRect.Min.X, adjustedFaceRect.Min.Y)
		}

		fmt.Printf("DEBUG: 裁剪后图片尺寸: %dx%d\n", croppedImg.Bounds().Dx(), croppedImg.Bounds().Dy())

		// 4. 调整为正方形并添加指定留白，重点是让鼻尖居中
		//    左右各5%，上下留白根据人脸位置智能调整
		croppedWidth := croppedImg.Bounds().Dx()
		croppedHeight := croppedImg.Bounds().Dy()

		paddingRatio := 0.05

		// 计算最终正方形画布的最小尺寸 (maxDim)
		// 人像宽度需要占据 maxDim * (1 - 2*paddingRatio)
		requiredDimByWidth := float64(croppedWidth) / (1.0 - 2*paddingRatio)

		// 高度计算改为适应居中模式，上下预留适当空间
		heightPaddingRatio := 0.1 // 上下各预留10%空间
		requiredDimByHeight := float64(croppedHeight) / (1.0 - 2*heightPaddingRatio)

		maxDim := int(math.Ceil(math.Max(requiredDimByWidth, requiredDimByHeight)))

		// 确保maxDim不小于人像的实际尺寸，并设置一个合理的最小尺寸
		maxDim = int(math.Max(float64(maxDim), math.Max(float64(croppedWidth), float64(croppedHeight))))
		if maxDim < 100 {
			maxDim = 100
		} // 设置最小输出尺寸为100x100，避免极小图片出现问题

		squareImg := image.NewRGBA(image.Rect(0, 0, maxDim, maxDim))
		// 填充完全透明背景
		draw.Draw(squareImg, squareImg.Bounds(), image.Transparent, image.Point{}, draw.Src)

		// 计算最终缩放比例，使人像按比例适应留白区域
		scaleX := (float64(maxDim) * (1.0 - 2*paddingRatio)) / float64(croppedWidth)
		scaleY := (float64(maxDim) * (1.0 - 2*heightPaddingRatio)) / float64(croppedHeight)
		finalScale := math.Min(scaleX, scaleY) // 使用较小的比例确保人像完全可见且不变形

		// 计算缩放后人像的尺寸
		scaledPersonWidth := int(float64(croppedWidth) * finalScale)
		scaledPersonHeight := int(float64(croppedHeight) * finalScale)

		// 关键修复：智能定位算法，让鼻尖（脸部中心）居中
		var offsetX, offsetY int

		if adjustedFaceRect != nil {
			// 如果检测到人脸，以脸部中心为基准进行居中定位
			fmt.Printf("🎯 使用脸部中心定位算法\n")

			// 计算人脸在裁剪图像中的相对位置（使用调整后的坐标）
			faceRelativeX := adjustedFaceRect.Min.X - croppedImg.Bounds().Min.X
			faceRelativeY := adjustedFaceRect.Min.Y - croppedImg.Bounds().Min.Y
			faceRelativeWidth := adjustedFaceRect.Dx()
			faceRelativeHeight := adjustedFaceRect.Dy()

			// 计算脸部中心在裁剪图像中的位置
			faceCenterX := faceRelativeX + faceRelativeWidth/2
			faceCenterY := faceRelativeY + faceRelativeHeight/2

			// 缩放后脸部中心的位置
			scaledFaceCenterX := int(float64(faceCenterX) * finalScale)
			scaledFaceCenterY := int(float64(faceCenterY) * finalScale)

			// 计算偏移量，使脸部中心位于正方形画布的中央
			offsetX = (maxDim / 2) - scaledFaceCenterX
			offsetY = (maxDim / 2) - scaledFaceCenterY

			fmt.Printf("📐 脸部中心: (%d,%d) -> 缩放后: (%d,%d) -> 画布中心偏移: (%d,%d)\n",
				faceCenterX, faceCenterY, scaledFaceCenterX, scaledFaceCenterY, offsetX, offsetY)
		} else {
			// 如果未检测到人脸，使用传统的居中算法
			fmt.Printf("💡 未检测到人脸，使用整体居中算法\n")
			offsetX = (maxDim - scaledPersonWidth) / 2
			offsetY = (maxDim - scaledPersonHeight) / 2
		}

		// 边界检查，确保人像不会超出画布边界
		if offsetX < 0 {
			offsetX = 0
		}
		if offsetY < 0 {
			offsetY = 0
		}
		if offsetX+scaledPersonWidth > maxDim {
			offsetX = maxDim - scaledPersonWidth
		}
		if offsetY+scaledPersonHeight > maxDim {
			offsetY = maxDim - scaledPersonHeight
		}

		// 绘制缩放后的人像到正方形画布
		targetRectOnSquare := image.Rect(offsetX, offsetY, offsetX+scaledPersonWidth, offsetY+scaledPersonHeight)
		draw.Draw(squareImg, targetRectOnSquare, croppedImg, croppedImg.Bounds().Min, draw.Src) // draw.Src会进行最近邻缩放

		fmt.Printf("DEBUG: 最终人像位置: (%d,%d) 尺寸: %dx%d，画布: %dx%d\n",
			offsetX, offsetY, scaledPersonWidth, scaledPersonHeight, maxDim, maxDim)

		fmt.Printf("DEBUG: 最终保存图片尺寸: %dx%d\n", squareImg.Bounds().Dx(), squareImg.Bounds().Dy())

		// 5. 保存最终处理后的正方形图片
		var buf bytes.Buffer
		if err := png.Encode(&buf, squareImg); err != nil {
			return fmt.Errorf("编码正方形抠图图片失败: %v", err)
		}

		fmt.Printf("DEBUG: 准备保存抠图图片到: %s\n", outputPath)

		if err := os.WriteFile(outputPath, buf.Bytes(), 0644); err != nil {
			return fmt.Errorf("保存正方形抠图结果失败: %v", err)
		}
		fmt.Printf("✅ 抠图并处理为正方形头像完成: %s\n", filepath.Base(outputPath))
		return nil
	}

	return fmt.Errorf("人像抠图API返回结果为空或无效")
}

// detectFaceInImage 在抠图后的图片中检测人脸位置
func (e *PhotoEnhancer) detectFaceInImage(rgbaImg *image.RGBA, personRect image.Rectangle) (*image.Rectangle, error) {
	// 调用COS人脸检测API检测人脸位置
	// 首先将裁剪后的人像区域保存为临时文件
	tempImg := rgbaImg.SubImage(personRect)

	// 将图片编码为字节数组
	var buf bytes.Buffer
	if err := png.Encode(&buf, tempImg); err != nil {
		return nil, fmt.Errorf("编码临时图片失败: %v", err)
	}

	// 检查图片大小和尺寸，如果太大需要压缩
	imageData := buf.Bytes()
	imageSizeMB := float64(len(imageData)) / (1024 * 1024)

	// 解码图片获取尺寸信息
	img, _, err := image.Decode(bytes.NewReader(imageData))
	if err != nil {
		return nil, fmt.Errorf("解码图片失败: %v", err)
	}

	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	fmt.Printf("📏 抠图人脸检测图片尺寸: %dx%d, 大小: %.2fMB\n", width, height, imageSizeMB)

	// 检查是否需要压缩（文件大小或尺寸）
	needsCompression := false
	var compressionReason string

	if imageSizeMB > 2.0 {
		needsCompression = true
		compressionReason = fmt.Sprintf("文件大小%.2fMB > 2MB", imageSizeMB)
	}

	// 腾讯云API限制：宽度和高度都不能超过4096
	if width > 4096 || height > 4096 {
		needsCompression = true
		if compressionReason != "" {
			compressionReason += "，"
		}
		compressionReason += fmt.Sprintf("尺寸%dx%d > 4096x4096", width, height)
	}

	if needsCompression {
		fmt.Printf("📦 抠图人脸检测前压缩: %s\n", compressionReason)

		// 压缩图片（限制最大尺寸为2048x2048，确保安全）
		compressedData, err := e.compressImageWithDimensions(img, "png", 2.0, 2048)
		if err != nil {
			return nil, fmt.Errorf("压缩图片失败: %v", err)
		}

		imageData = compressedData
		compressedSizeMB := float64(len(imageData)) / (1024 * 1024)

		// 重新解码获取压缩后的尺寸
		compressedImg, _, decodeErr := image.Decode(bytes.NewReader(compressedData))
		if decodeErr == nil {
			compressedBounds := compressedImg.Bounds()
			fmt.Printf("✅ 压缩完成: %dx%d -> %dx%d, %.2fMB -> %.2fMB\n",
				width, height, compressedBounds.Dx(), compressedBounds.Dy(), imageSizeMB, compressedSizeMB)
		}
	}

	// 上传临时图片到COS进行人脸检测
	tempFileName := fmt.Sprintf("face_detect_temp_%d.png", time.Now().Unix())

	// 使用现有的cosClient上传图片
	_, err = e.cosClient.Object.Put(context.Background(), tempFileName, bytes.NewReader(imageData), nil)
	if err != nil {
		return nil, fmt.Errorf("上传临时图片失败: %v", err)
	}

	// 确保清理临时文件
	defer func() {
		e.cosClient.Object.Delete(context.Background(), tempFileName)
	}()

	// 调用人脸检测API
	opt := &cos.DetectFaceOptions{}
	var resp *cos.DetectFaceResult
	var detectErr error
	resp, _, detectErr = e.cosClient.CI.DetectFace(context.Background(), tempFileName, opt)
	if detectErr != nil {
		return nil, fmt.Errorf("调用人脸检测API失败: %v", detectErr)
	}

	if resp == nil || len(resp.FaceInfos) == 0 {
		return nil, fmt.Errorf("未检测到人脸")
	}

	// 获取第一个检测到的人脸信息
	faceInfo := resp.FaceInfos[0]

	// 将相对于personRect的坐标转换为相对于原图的绝对坐标
	faceX := personRect.Min.X + int(faceInfo.X)
	faceY := personRect.Min.Y + int(faceInfo.Y)
	faceWidth := int(faceInfo.Width)
	faceHeight := int(faceInfo.Height)

	faceRect := image.Rect(faceX, faceY, faceX+faceWidth, faceY+faceHeight)
	return &faceRect, nil
}

// calculateHeadShoulderRegion 基于人脸位置计算头肩裁剪区域
func (e *PhotoEnhancer) calculateHeadShoulderRegion(faceRect *image.Rectangle, personRect image.Rectangle, imgBounds image.Rectangle) image.Rectangle {
	// 人脸中心点
	faceCenterX := faceRect.Min.X + faceRect.Dx()/2

	// 基于人脸大小计算头肩区域
	faceWidth := faceRect.Dx()
	faceHeight := faceRect.Dy()

	// 计算头肩区域的宽度和高度
	// 宽度：人脸宽度的2.5-3倍，确保包含肩部
	headShoulderWidth := int(float64(faceWidth) * 2.8)

	// 高度：根据照片类型智能调整
	personHeight := personRect.Dy()
	faceToTopRatio := float64(faceRect.Min.Y-personRect.Min.Y) / float64(personHeight)

	var headShoulderHeight int
	if faceToTopRatio < 0.1 {
		// 大头照：人脸靠近顶部，主要扩展向下
		headShoulderHeight = int(float64(faceHeight) * 3.5)
	} else if faceToTopRatio < 0.3 {
		// 半身照：人脸在上部，包含头肩区域
		headShoulderHeight = int(float64(faceHeight) * 4.0)
	} else {
		// 全身照：人脸在中上部，扩展更多向下包含肩部
		headShoulderHeight = int(float64(faceHeight) * 4.5)
	}

	// 确保头肩区域为正方形（取较大的尺寸）
	headShoulderSize := int(math.Max(float64(headShoulderWidth), float64(headShoulderHeight)))

	// 计算裁剪区域的左上角坐标
	// 水平居中于人脸
	cropX := faceCenterX - headShoulderSize/2

	// 垂直方向：人脸顶部向上延伸人脸高度的0.3倍作为头顶留白
	headTopMargin := int(float64(faceHeight) * 0.3)
	cropY := faceRect.Min.Y - headTopMargin

	// 边界检查，确保不超出人像区域和图片边界
	cropX = int(math.Max(float64(cropX), float64(personRect.Min.X)))
	cropY = int(math.Max(float64(cropY), float64(personRect.Min.Y)))

	cropRight := cropX + headShoulderSize
	cropBottom := cropY + headShoulderSize

	// 如果右边或底部超出边界，向左或向上调整
	if cropRight > personRect.Max.X {
		cropX = personRect.Max.X - headShoulderSize
	}
	if cropBottom > personRect.Max.Y {
		cropY = personRect.Max.Y - headShoulderSize
	}

	// 最终边界检查
	cropX = int(math.Max(float64(cropX), float64(imgBounds.Min.X)))
	cropY = int(math.Max(float64(cropY), float64(imgBounds.Min.Y)))
	cropRight = int(math.Min(float64(cropX+headShoulderSize), float64(imgBounds.Max.X)))
	cropBottom = int(math.Min(float64(cropY+headShoulderSize), float64(imgBounds.Max.Y)))

	return image.Rect(cropX, cropY, cropRight, cropBottom)
}

// 生成标准证件照
func (e *PhotoEnhancer) generateStandardPhoto(inputPath, outputPath string) error {
	fmt.Printf("📸 生成标准证件照: %s\n", filepath.Base(inputPath))

	// 读取抠图后的图片
	img, err := e.loadImage(inputPath)
	if err != nil {
		return fmt.Errorf("读取抠图图片失败: %v", err)
	}

	// 标准一寸证件照尺寸（25mm×35mm，300DPI）
	// 25mm = 295像素，35mm = 413像素
	standardWidth := 295
	standardHeight := 413

	// 调整图片尺寸为标准证件照尺寸
	standardImg := e.resizeToStandard(img, standardWidth, standardHeight)

	// 保存标准证件照
	err = e.saveImage(standardImg, outputPath)
	if err != nil {
		return fmt.Errorf("保存标准证件照失败: %v", err)
	}

	fmt.Printf("✅ 标准证件照已生成: %s\n", filepath.Base(outputPath))
	return nil
}

// 调整图片为标准证件照尺寸
func (e *PhotoEnhancer) resizeToStandard(img image.Image, targetWidth, targetHeight int) image.Image {
	bounds := img.Bounds()
	width, height := bounds.Dx(), bounds.Dy()

	// 计算缩放比例，保持宽高比
	scaleX := float64(targetWidth) / float64(width)
	scaleY := float64(targetHeight) / float64(height)
	scale := scaleX
	if scaleY < scaleX {
		scale = scaleY
	}

	// 计算缩放后的尺寸
	newWidth := int(float64(width) * scale)
	newHeight := int(float64(height) * scale)

	// 创建缩放后的图片
	resized := image.NewRGBA(image.Rect(0, 0, newWidth, newHeight))

	// 执行缩放
	for y := 0; y < newHeight; y++ {
		for x := 0; x < newWidth; x++ {
			srcX := int(float64(x) / scale)
			srcY := int(float64(y) / scale)

			if srcX < width && srcY < height {
				resized.Set(x, y, img.At(srcX, srcY))
			}
		}
	}

	// 如果缩放后的图片尺寸不等于目标尺寸，需要居中放置
	if newWidth != targetWidth || newHeight != targetHeight {
		// 创建目标尺寸的图片（透明背景）
		standard := image.NewRGBA(image.Rect(0, 0, targetWidth, targetHeight))

		// 计算居中位置
		offsetX := (targetWidth - newWidth) / 2
		offsetY := (targetHeight - newHeight) / 2

		// 将缩放后的图片居中放置
		draw.Draw(standard, image.Rect(offsetX, offsetY, offsetX+newWidth, offsetY+newHeight), resized, image.Point{0, 0}, draw.Src)

		return standard
	}

	return resized
}

// compressImageForAPI 为API调用压缩图片（与ImageProcessor中的逻辑相同）
func (e *PhotoEnhancer) compressImageForAPI(imagePath string) ([]byte, *ImageCompressionInfo, error) {
	// 读取原始图片数据
	imageData, err := os.ReadFile(imagePath)
	if err != nil {
		return nil, nil, fmt.Errorf("读取图片失败: %v", err)
	}

	// 计算原始大小（MB）
	originalSizeMB := float64(len(imageData)) / (1024 * 1024)

	// 获取图片尺寸信息
	img, format, err := image.Decode(bytes.NewReader(imageData))
	if err != nil {
		return nil, nil, fmt.Errorf("图片解码失败: %v", err)
	}

	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// 估算DPI
	estimatedDPI := e.estimateDPI(width, height, originalSizeMB)

	compressionInfo := &ImageCompressionInfo{
		OriginalSizeMB:  originalSizeMB,
		EstimatedDPI:    estimatedDPI,
		ImageDimensions: Size{Width: width, Height: height},
		WasCompressed:   false,
	}

	// 检查是否需要压缩（文件大小和尺寸）
	maxSizeMB := e.config.PhotoEnhancement.MaxImageSizeForAPI
	maxDimension := 4096 // 腾讯云API的最大尺寸限制
	smartResizeThreshold := e.config.PhotoEnhancement.SmartResizeThresholdWidth

	needsCompression := false
	var compressionReason string

	if e.config.PhotoEnhancement.EnableSizeCheck && originalSizeMB > maxSizeMB {
		needsCompression = true
		compressionReason += fmt.Sprintf("文件大小%.2fMB > %.2fMB", originalSizeMB, maxSizeMB)
	}

	if width > maxDimension || height > maxDimension {
		needsCompression = true
		if compressionReason != "" {
			compressionReason += "，"
		}
		compressionReason += fmt.Sprintf("图片尺寸%dx%d > %dx%d", width, height, maxDimension, maxDimension)
	}

	if width > smartResizeThreshold {
		needsCompression = true
		if compressionReason != "" {
			compressionReason += "，"
		}
		compressionReason += fmt.Sprintf("宽度%dpx > %dpx", width, smartResizeThreshold)
	}

	if !needsCompression {
		fmt.Printf("📏 图片检查通过: 大小%.2fMB <= %.2fMB，尺寸%dx%d\n",
			originalSizeMB, maxSizeMB, width, height)
		compressionInfo.CompressedSizeMB = originalSizeMB
		compressionInfo.CompressionRatio = 1.0
		return imageData, compressionInfo, nil
	}

	fmt.Printf("📦 图片需要压缩: %s\n", compressionReason)

	// 进行智能压缩
	compressedData, err := e.compressImageWithDimensions(img, format, maxSizeMB, maxDimension)
	if err != nil {
		return nil, nil, fmt.Errorf("图片压缩失败: %v", err)
	}

	compressedSizeMB := float64(len(compressedData)) / (1024 * 1024)
	compressionRatio := compressedSizeMB / originalSizeMB

	compressionInfo.CompressedSizeMB = compressedSizeMB
	compressionInfo.CompressionRatio = compressionRatio
	compressionInfo.WasCompressed = true

	fmt.Printf("✅ 图片压缩完成: %.2fMB -> %.2fMB (压缩比: %.1f%%)\n",
		originalSizeMB, compressedSizeMB, compressionRatio*100)

	return compressedData, compressionInfo, nil
}

// compressImageWithDimensions 智能压缩图片（PhotoEnhancer版本）
func (e *PhotoEnhancer) compressImageWithDimensions(img image.Image, format string, targetSizeMB float64, maxDimension int) ([]byte, error) {
	bounds := img.Bounds()
	originalWidth := bounds.Dx()
	originalHeight := bounds.Dy()

	fmt.Printf("🎯 开始智能压缩 (质量优先模式)\n")
	fmt.Printf("   原始尺寸: %dx%d\n", originalWidth, originalHeight)

	// 第一步：智能尺寸缩放
	smartResizeThreshold := e.config.PhotoEnhancement.SmartResizeThresholdWidth
	smartResizeTarget := e.config.PhotoEnhancement.SmartResizeTargetWidth

	processedImg := img
	var compressionSteps []string

	if originalWidth > smartResizeThreshold {
		// 计算缩放比例（基于宽度）
		scale := float64(smartResizeTarget) / float64(originalWidth)
		newWidth := smartResizeTarget
		newHeight := int(float64(originalHeight) * scale)

		fmt.Printf("🔧 智能缩放: %dx%d -> %dx%d (目标宽度: %dpx)\n",
			originalWidth, originalHeight, newWidth, newHeight, smartResizeTarget)

		processedImg = e.resizeImageForCompression(processedImg, scale)
		compressionSteps = append(compressionSteps, fmt.Sprintf("尺寸缩放 %dx%d->%dx%d", originalWidth, originalHeight, newWidth, newHeight))
	}

	// 第二步：生成当前图片数据，检查文件大小
	currentData, err := e.encodeImage(processedImg, format, e.config.PhotoEnhancement.CompressionQuality)
	if err != nil {
		return nil, fmt.Errorf("编码图片失败: %v", err)
	}

	currentSizeMB := float64(len(currentData)) / (1024 * 1024)
	fmt.Printf("📊 缩放后大小: %.2fMB\n", currentSizeMB)

	// 第三步：如果文件大小仍然超标，进行质量压缩
	if currentSizeMB > targetSizeMB {
		fmt.Printf("📦 需要进一步质量压缩: %.2fMB > %.2fMB\n", currentSizeMB, targetSizeMB)

		qualityCompressedData, err := e.compressImageByQuality(processedImg, format, targetSizeMB)
		if err != nil {
			return nil, fmt.Errorf("质量压缩失败: %v", err)
		}

		qualityCompressedSizeMB := float64(len(qualityCompressedData)) / (1024 * 1024)
		compressionSteps = append(compressionSteps, fmt.Sprintf("质量压缩 %.2fMB->%.2fMB", currentSizeMB, qualityCompressedSizeMB))
		currentData = qualityCompressedData
		currentSizeMB = qualityCompressedSizeMB
	}

	// 第四步：最终安全检查（API尺寸限制）
	bounds = processedImg.Bounds()
	finalWidth := bounds.Dx()
	finalHeight := bounds.Dy()

	if finalWidth > maxDimension || finalHeight > maxDimension {
		fmt.Printf("⚠️  最终安全检查: 图片尺寸 %dx%d 超过API限制 %d，进行最终缩放\n", finalWidth, finalHeight, maxDimension)

		scaleX := float64(maxDimension) / float64(finalWidth)
		scaleY := float64(maxDimension) / float64(finalHeight)
		scale := scaleX
		if scaleY < scaleX {
			scale = scaleY
		}

		processedImg = e.resizeImageForCompression(processedImg, scale)
		currentData, err = e.encodeImage(processedImg, format, e.config.PhotoEnhancement.CompressionQuality)
		if err != nil {
			return nil, fmt.Errorf("最终编码失败: %v", err)
		}

		currentSizeMB = float64(len(currentData)) / (1024 * 1024)
		compressionSteps = append(compressionSteps, "API安全缩放")
	}

	fmt.Printf("✅ 智能压缩完成: %.2fMB\n", currentSizeMB)
	if len(compressionSteps) > 0 {
		fmt.Printf("   压缩步骤: %s\n", strings.Join(compressionSteps, " → "))
	}

	return currentData, nil
}

// encodeImage 将图片编码为指定格式的字节数组
func (e *PhotoEnhancer) encodeImage(img image.Image, format string, quality int) ([]byte, error) {
	var buf bytes.Buffer

	switch strings.ToLower(format) {
	case "jpeg", "jpg":
		err := jpeg.Encode(&buf, img, &jpeg.Options{Quality: quality})
		if err != nil {
			return nil, err
		}
	case "png":
		err := png.Encode(&buf, img)
		if err != nil {
			return nil, err
		}
	default:
		// 默认使用JPEG编码
		err := jpeg.Encode(&buf, img, &jpeg.Options{Quality: quality})
		if err != nil {
			return nil, err
		}
	}

	return buf.Bytes(), nil
}

// compressImageByQuality 通过调整质量参数压缩图片
func (e *PhotoEnhancer) compressImageByQuality(img image.Image, format string, targetSizeMB float64) ([]byte, error) {
	baseQuality := e.config.PhotoEnhancement.CompressionQuality
	if e.config.PhotoEnhancement.QualityPriority {
		baseQuality = 90 // 质量优先模式使用更高的起始质量
	}

	maxAttempts := 6
	quality := baseQuality
	minQuality := 40 // 质量优先模式的最低质量更高

	if e.config.PhotoEnhancement.QualityPriority {
		minQuality = 60 // 质量优先时最低质量为60
	}

	for attempt := 0; attempt < maxAttempts; attempt++ {
		data, err := e.encodeImage(img, format, quality)
		if err != nil {
			return nil, err
		}

		sizeMB := float64(len(data)) / (1024 * 1024)

		fmt.Printf("   🔄 质量压缩尝试 %d/%d: 质量=%d, 大小=%.2fMB\n", attempt+1, maxAttempts, quality, sizeMB)

		if sizeMB <= targetSizeMB {
			fmt.Printf("   ✅ 质量压缩成功: 质量=%d, 大小=%.2fMB\n", quality, sizeMB)
			return data, nil
		}

		// 如果质量已经很低了，停止尝试
		if quality <= minQuality {
			fmt.Printf("   ⚠️  已达到最低质量限制(%d)，停止压缩\n", minQuality)
			return data, nil
		}

		// 降低质量，质量优先模式降幅更小
		if e.config.PhotoEnhancement.QualityPriority {
			quality = int(float64(quality) * 0.85) // 质量优先：每次降低15%
		} else {
			quality = int(float64(quality) * 0.75) // 标准模式：每次降低25%
		}

		if quality < minQuality {
			quality = minQuality
		}
	}

	// 如果最后还是太大，返回最低质量的版本
	data, err := e.encodeImage(img, format, minQuality)
	if err != nil {
		return nil, err
	}

	sizeMB := float64(len(data)) / (1024 * 1024)
	fmt.Printf("   ⚠️  最终压缩结果: 质量=%d, 大小=%.2fMB (可能仍超过目标大小)\n", minQuality, sizeMB)

	return data, nil
}

// resizeImageForCompression 为压缩目的调整图片尺寸
func (e *PhotoEnhancer) resizeImageForCompression(img image.Image, scale float64) image.Image {
	bounds := img.Bounds()
	width := int(float64(bounds.Dx()) * scale)
	height := int(float64(bounds.Dy()) * scale)

	resized := image.NewRGBA(image.Rect(0, 0, width, height))

	// 简单的最近邻缩放
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			srcX := int(float64(x) / scale)
			srcY := int(float64(y) / scale)

			if srcX < bounds.Dx() && srcY < bounds.Dy() {
				resized.Set(x, y, img.At(srcX, srcY))
			}
		}
	}

	return resized
}

// enhanceImageForDetection 本地图片增强方法（已废弃，现使用AI超分API）
func (e *PhotoEnhancer) enhanceImageForDetection(imageData []byte) ([]byte, error) {
	fmt.Printf("🔧 开始图片增强处理...\n")

	// 解码图片
	img, format, err := image.Decode(bytes.NewReader(imageData))
	if err != nil {
		return nil, fmt.Errorf("图片解码失败: %v", err)
	}

	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// 1. 适度放大（如果图片较小）
	var processedImg image.Image = img
	if width < 400 || height < 500 {
		scale := 1.5 // 放大1.5倍
		newWidth := int(float64(width) * scale)
		newHeight := int(float64(height) * scale)
		fmt.Printf("📏 图片放大: %dx%d -> %dx%d\n", width, height, newWidth, newHeight)
		processedImg = e.resizeImageForCompression(processedImg, scale)
	}

	// 2. 锐化处理（提高清晰度）
	processedImg = e.sharpenImage(processedImg)

	// 3. 对比度增强
	processedImg = e.enhanceContrast(processedImg)

	// 4. 重新编码
	enhancedData, err := e.encodeImage(processedImg, format, 95) // 使用高质量编码
	if err != nil {
		return nil, fmt.Errorf("增强图片编码失败: %v", err)
	}

	fmt.Printf("✅ 图片增强完成: %d bytes -> %d bytes\n", len(imageData), len(enhancedData))
	return enhancedData, nil
}

// estimateDPI 估算图片的DPI
func (e *PhotoEnhancer) estimateDPI(width, height int, sizeMB float64) int {
	// 基于图片尺寸和文件大小的简单DPI估算
	totalPixels := width * height

	// 假设标准的打印尺寸比例
	var estimatedDPI int

	if totalPixels > 4000000 { // 超过4MP
		estimatedDPI = 300
	} else if totalPixels > 2000000 { // 2-4MP
		estimatedDPI = 200
	} else if totalPixels > 1000000 { // 1-2MP
		estimatedDPI = 150
	} else if totalPixels > 500000 { // 0.5-1MP
		estimatedDPI = 100
	} else {
		estimatedDPI = 72 // 低分辨率
	}

	// 根据文件大小调整估算
	if sizeMB > 5 {
		estimatedDPI += 50
	} else if sizeMB < 0.5 {
		estimatedDPI -= 50
	}

	if estimatedDPI < 72 {
		estimatedDPI = 72
	}

	return estimatedDPI
}

// 图片锐化处理
func (e *PhotoEnhancer) sharpenImage(img image.Image) image.Image {
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// 创建新图片
	sharpened := image.NewRGBA(bounds)

	// 简单的锐化卷积核
	kernel := [][]float64{
		{0, -1, 0},
		{-1, 5, -1},
		{0, -1, 0},
	}

	// 应用锐化
	for y := 1; y < height-1; y++ {
		for x := 1; x < width-1; x++ {
			var r, g, b, a uint32

			// 应用卷积核
			for ky := -1; ky <= 1; ky++ {
				for kx := -1; kx <= 1; kx++ {
					px := x + kx
					py := y + ky
					weight := kernel[ky+1][kx+1]

					pr, pg, pb, pa := img.At(px, py).RGBA()
					r += uint32(float64(pr) * weight)
					g += uint32(float64(pg) * weight)
					b += uint32(float64(pb) * weight)
					a += uint32(float64(pa) * weight)
				}
			}

			// 限制值范围
			r = uint32(math.Min(65535, math.Max(0, float64(r))))
			g = uint32(math.Min(65535, math.Max(0, float64(g))))
			b = uint32(math.Min(65535, math.Max(0, float64(b))))
			a = uint32(math.Min(65535, math.Max(0, float64(a))))

			sharpened.Set(x, y, color.RGBA64{
				R: uint16(r),
				G: uint16(g),
				B: uint16(b),
				A: uint16(a),
			})
		}
	}

	return sharpened
}

// 对比度增强
func (e *PhotoEnhancer) enhanceContrast(img image.Image) image.Image {
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	enhanced := image.NewRGBA(bounds)
	contrast := 1.2 // 对比度增强因子

	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			r, g, b, a := img.At(x, y).RGBA()

			// 应用对比度增强
			r = uint32(math.Min(65535, math.Max(0, float64(r)*contrast)))
			g = uint32(math.Min(65535, math.Max(0, float64(g)*contrast)))
			b = uint32(math.Min(65535, math.Max(0, float64(b)*contrast)))

			enhanced.Set(x, y, color.RGBA64{
				R: uint16(r),
				G: uint16(g),
				B: uint16(b),
				A: uint16(a),
			})
		}
	}

	return enhanced
}

// contrastEnhancedCutout 对比度增强抠图（专门针对白色衣服问题）
func (e *PhotoEnhancer) contrastEnhancedCutout(cosFileName, outputPath string) error {
	fmt.Printf("🔧 执行对比度增强预处理...\n")

	// 1. 下载原图
	resp, err := e.cosClient.Object.Get(context.Background(), cosFileName, nil)
	if err != nil {
		return fmt.Errorf("下载原图失败: %v", err)
	}
	defer resp.Body.Close()

	originalData, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取原图数据失败: %v", err)
	}

	// 2. 专门针对白色衣服的对比度增强
	enhancedData, err := e.enhanceContrastForWhiteClothes(originalData)
	if err != nil {
		return fmt.Errorf("对比度增强失败: %v", err)
	}

	// 3. 上传增强后的图片
	enhancedFileName := fmt.Sprintf("contrast_enhanced_%s", cosFileName)
	_, err = e.cosClient.Object.Put(context.Background(), enhancedFileName, bytes.NewReader(enhancedData), nil)
	if err != nil {
		return fmt.Errorf("上传增强图片失败: %v", err)
	}
	fmt.Printf("📤 对比度增强图片已上传: %s\n", enhancedFileName)

	// 4. 使用增强后的图片进行抠图
	err = e.performCutout(enhancedFileName, outputPath)
	if err != nil {
		return fmt.Errorf("增强后抠图失败: %v", err)
	}

	// 5. 清理临时文件
	go func() {
		time.Sleep(30 * time.Second)
		e.cosClient.Object.Delete(context.Background(), enhancedFileName, nil)
	}()

	fmt.Printf("✅ 对比度增强抠图完成\n")
	return nil
}

// edgeEnhancedCutout 边缘增强抠图
func (e *PhotoEnhancer) edgeEnhancedCutout(cosFileName, outputPath string) error {
	fmt.Printf("🔧 执行边缘增强预处理...\n")

	// 下载原图并进行边缘增强
	resp, err := e.cosClient.Object.Get(context.Background(), cosFileName, nil)
	if err != nil {
		return fmt.Errorf("下载原图失败: %v", err)
	}
	defer resp.Body.Close()

	originalData, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取原图数据失败: %v", err)
	}

	// 边缘增强处理
	enhancedData, err := e.enhanceEdgesForCutout(originalData)
	if err != nil {
		return fmt.Errorf("边缘增强失败: %v", err)
	}

	// 上传并抠图
	enhancedFileName := fmt.Sprintf("edge_enhanced_%s", cosFileName)
	_, err = e.cosClient.Object.Put(context.Background(), enhancedFileName, bytes.NewReader(enhancedData), nil)
	if err != nil {
		return fmt.Errorf("上传边缘增强图片失败: %v", err)
	}

	err = e.performCutout(enhancedFileName, outputPath)

	// 清理临时文件
	go func() {
		time.Sleep(30 * time.Second)
		e.cosClient.Object.Delete(context.Background(), enhancedFileName, nil)
	}()

	return err
}

// multiParameterCutout 多参数组合尝试
func (e *PhotoEnhancer) multiParameterCutout(cosFileName, outputPath string) error {
	fmt.Printf("🔧 尝试多种抠图参数组合...\n")

	// 这里可以尝试腾讯云的不同抠图参数或其他抠图API
	// 例如调整抠图的敏感度、使用不同的算法模式等

	// 参数组合1：更严格的人像识别
	opt1 := &cos.AIPortraitMattingOptions{}
	err := e.performCutoutWithOptions(cosFileName, outputPath+"_attempt1.png", opt1)
	if err == nil {
		// 重命名为最终输出
		os.Rename(outputPath+"_attempt1.png", outputPath)
		return nil
	}

	// 可以添加更多参数组合...

	return fmt.Errorf("多参数尝试均失败")
}

// enhanceContrastForWhiteClothes 专门针对白色衣服的对比度增强
func (e *PhotoEnhancer) enhanceContrastForWhiteClothes(imageData []byte) ([]byte, error) {
	fmt.Printf("🎨 开始白色衣服专用对比度增强...\n")

	// 解码图片
	img, format, err := image.Decode(bytes.NewReader(imageData))
	if err != nil {
		return nil, fmt.Errorf("图片解码失败: %v", err)
	}

	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()
	enhanced := image.NewRGBA(bounds)

	// 专门针对白色区域的增强算法
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			r, g, b, a := img.At(x, y).RGBA()

			// 转换为0-255范围
			r8 := uint8(r >> 8)
			g8 := uint8(g >> 8)
			b8 := uint8(b >> 8)
			a8 := uint8(a >> 8)

			// 检测是否为白色或接近白色
			isWhitish := (r8 > 200 && g8 > 200 && b8 > 200)

			if isWhitish {
				// 对白色区域进行特殊处理
				// 略微降低亮度，增加色彩分离度
				r8 = uint8(math.Max(0, float64(r8)*0.85))
				g8 = uint8(math.Max(0, float64(g8)*0.85))
				b8 = uint8(math.Max(0, float64(b8)*0.85))

				// 增加轻微的色彩倾向，帮助区分前景和背景
				if x < width/2 { // 左半部分（通常是人）
					r8 = uint8(math.Min(255, float64(r8)*1.05))
				} else { // 右半部分（通常是背景）
					b8 = uint8(math.Min(255, float64(b8)*1.05))
				}
			} else {
				// 非白色区域增强对比度
				r8 = uint8(math.Min(255, float64(r8)*1.2))
				g8 = uint8(math.Min(255, float64(g8)*1.2))
				b8 = uint8(math.Min(255, float64(b8)*1.2))
			}

			enhanced.Set(x, y, color.RGBA{r8, g8, b8, a8})
		}
	}

	// 重新编码
	var buf bytes.Buffer
	switch format {
	case "jpeg", "jpg":
		err = jpeg.Encode(&buf, enhanced, &jpeg.Options{Quality: 90})
	case "png":
		err = png.Encode(&buf, enhanced)
	default:
		err = png.Encode(&buf, enhanced)
	}

	if err != nil {
		return nil, fmt.Errorf("图片编码失败: %v", err)
	}

	fmt.Printf("✅ 白色衣服对比度增强完成\n")
	return buf.Bytes(), nil
}

// enhanceEdgesForCutout 边缘增强处理
func (e *PhotoEnhancer) enhanceEdgesForCutout(imageData []byte) ([]byte, error) {
	fmt.Printf("✂️  开始边缘增强处理...\n")

	// 解码图片
	img, format, err := image.Decode(bytes.NewReader(imageData))
	if err != nil {
		return nil, fmt.Errorf("图片解码失败: %v", err)
	}

	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()
	enhanced := image.NewRGBA(bounds)

	// 边缘检测卷积核（拉普拉斯算子）
	kernel := [][]float64{
		{0, -1, 0},
		{-1, 4, -1},
		{0, -1, 0},
	}

	// 应用边缘增强
	for y := 1; y < height-1; y++ {
		for x := 1; x < width-1; x++ {
			var edgeR, edgeG, edgeB float64

			// 应用卷积核
			for ky := -1; ky <= 1; ky++ {
				for kx := -1; kx <= 1; kx++ {
					px := x + kx
					py := y + ky
					weight := kernel[ky+1][kx+1]

					r, g, b, _ := img.At(px, py).RGBA()
					edgeR += float64(r>>8) * weight
					edgeG += float64(g>>8) * weight
					edgeB += float64(b>>8) * weight
				}
			}

			// 获取原像素
			origR, origG, origB, origA := img.At(x, y).RGBA()
			r8 := uint8(origR >> 8)
			g8 := uint8(origG >> 8)
			b8 := uint8(origB >> 8)
			a8 := uint8(origA >> 8)

			// 混合原图和边缘信息
			edgeStrength := 0.3 // 边缘增强强度
			r8 = uint8(math.Min(255, math.Max(0, float64(r8)+edgeR*edgeStrength)))
			g8 = uint8(math.Min(255, math.Max(0, float64(g8)+edgeG*edgeStrength)))
			b8 = uint8(math.Min(255, math.Max(0, float64(b8)+edgeB*edgeStrength)))

			enhanced.Set(x, y, color.RGBA{r8, g8, b8, a8})
		}
	}

	// 重新编码
	var buf bytes.Buffer
	switch format {
	case "jpeg", "jpg":
		err = jpeg.Encode(&buf, enhanced, &jpeg.Options{Quality: 90})
	case "png":
		err = png.Encode(&buf, enhanced)
	default:
		err = png.Encode(&buf, enhanced)
	}

	if err != nil {
		return nil, fmt.Errorf("图片编码失败: %v", err)
	}

	fmt.Printf("✅ 边缘增强处理完成\n")
	return buf.Bytes(), nil
}

// evaluateCutoutQuality 评估抠图质量
func (e *PhotoEnhancer) evaluateCutoutQuality(outputPath string) float64 {
	// 读取抠图结果
	file, err := os.Open(outputPath)
	if err != nil {
		return 0.0
	}
	defer file.Close()

	img, err := png.Decode(file)
	if err != nil {
		return 0.0
	}

	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	var transparentPixels, solidPixels, totalPixels int

	// 分析透明度分布
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			_, _, _, a := img.At(x, y).RGBA()
			alpha := uint8(a >> 8)

			totalPixels++

			if alpha == 0 {
				transparentPixels++
			} else if alpha == 255 {
				solidPixels++
			}
		}
	}

	// 质量评分基于：
	// 1. 透明/不透明像素的比例是否合理
	// 2. 是否有足够的实体像素（人像部分）
	// 3. 边缘的清晰度（透明度过渡）

	transparentRatio := float64(transparentPixels) / float64(totalPixels)
	solidRatio := float64(solidPixels) / float64(totalPixels)

	// 理想的抠图应该有适当的透明背景和实体前景
	var quality float64

	if solidRatio < 0.1 { // 实体部分太少，可能过度抠图
		quality = 0.2
	} else if transparentRatio < 0.3 { // 透明部分太少，可能抠图不够
		quality = 0.4
	} else if solidRatio > 0.7 { // 实体部分太多，背景可能没抠干净
		quality = 0.3
	} else {
		// 合理的比例
		quality = 0.8 + (solidRatio-0.1)*0.2/0.6 // 0.8-1.0范围
	}

	return math.Min(1.0, quality)
}

// performCutoutWithOptions 使用指定参数进行抠图
func (e *PhotoEnhancer) performCutoutWithOptions(cosFileName, outputPath string, opt *cos.AIPortraitMattingOptions) error {
	resp, err := e.cosClient.CI.AIPortraitMatting(context.Background(), cosFileName, opt)
	if err != nil {
		return fmt.Errorf("调用人像抠图API失败: %v", err)
	}

	if resp != nil && resp.Body != nil {
		imgDataBytes, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			return fmt.Errorf("读取抠图结果失败: %v", err)
		}
		defer resp.Body.Close()

		if len(imgDataBytes) == 0 {
			return fmt.Errorf("人像抠图API返回结果为空")
		}

		// 直接保存结果（简化版本）
		err = os.WriteFile(outputPath, imgDataBytes, 0644)
		if err != nil {
			return fmt.Errorf("保存抠图结果失败: %v", err)
		}
	}

	return nil
}

// StandardPortraitTemplate 标准头像模板配置
type StandardPortraitTemplate struct {
	CanvasWidth  int     // 画布宽度
	CanvasHeight int     // 画布高度
	FaceRatio    float64 // 人脸在画布中的占比
	FaceCenterX  float64 // 人脸中心X位置比例
	FaceCenterY  float64 // 人脸中心Y位置比例
	TopPadding   float64 // 头顶留白比例
	BottomRatio  float64 // 底部裁剪比例（肩膀位置）
}

// getStandardTemplate 获取标准头像模板
func (e *PhotoEnhancer) getStandardTemplate() *StandardPortraitTemplate {
	return &StandardPortraitTemplate{
		CanvasWidth:  600,  // 标准宽度
		CanvasHeight: 800,  // 标准高度，4:3比例适合头肩
		FaceRatio:    0.25, // 人脸占画布25%
		FaceCenterX:  0.5,  // 人脸在水平中央
		FaceCenterY:  0.35, // 人脸中心在画布上方35%位置
		TopPadding:   0.15, // 头顶留白15%
		BottomRatio:  0.75, // 显示到肩膀位置
	}
}

// StandardizePortrait 智能标准化人像（头肩占位符方法）
func (e *PhotoEnhancer) StandardizePortrait(inputPath, outputDir string) (*ImageEnhancementResult, error) {
	startTime := time.Now()
	fmt.Printf("🎯 开始智能头像标准化: %s\n", filepath.Base(inputPath))

	// 1. 读取并优化原图
	imageData, err := os.ReadFile(inputPath)
	if err != nil {
		return nil, fmt.Errorf("读取图片失败: %v", err)
	}

	img, _, err := image.Decode(bytes.NewReader(imageData))
	if err != nil {
		return nil, fmt.Errorf("图片解码失败: %v", err)
	}

	// 2. 人脸检测，获取人物位置信息
	faceInfo, err := e.detectDetailedFaceInfo(imageData)
	if err != nil {
		return nil, fmt.Errorf("人脸检测失败: %v", err)
	}

	fmt.Printf("📍 检测到人脸: %dx%d at (%d,%d)\n",
		faceInfo.Width, faceInfo.Height, faceInfo.X, faceInfo.Y)

	// 3. 智能计算头肩区域
	headShoulderRegion := e.calculateIntelligentHeadShoulderRegion(img, faceInfo)
	fmt.Printf("👤 计算头肩区域: %dx%d at (%d,%d)\n",
		headShoulderRegion.Dx(), headShoulderRegion.Dy(),
		headShoulderRegion.Min.X, headShoulderRegion.Min.Y)

	// 4. 提取头肩图像
	headShoulderImg := img.(interface {
		SubImage(image.Rectangle) image.Image
	}).SubImage(headShoulderRegion)

	// 5. 应用标准化模板，生成标准头像
	template := e.getStandardTemplate()
	standardizedImg := e.applyStandardTemplate(headShoulderImg, faceInfo, template)

	// 6. 保存标准化结果
	standardizedPath := filepath.Join(outputDir,
		strings.TrimSuffix(filepath.Base(inputPath), filepath.Ext(inputPath))+"_standardized.png")

	err = e.saveImageAsPNG(standardizedImg, standardizedPath)
	if err != nil {
		return nil, fmt.Errorf("保存标准化图片失败: %v", err)
	}

	// 7. 可选：进行抠图处理
	var cutoutPath string
	if e.config.EnableImageProcessing {
		cutoutPath = filepath.Join(outputDir,
			strings.TrimSuffix(filepath.Base(inputPath), filepath.Ext(inputPath))+"_standard_cutout.png")

		err = e.generateStandardCutout(standardizedImg, cutoutPath)
		if err != nil {
			fmt.Printf("⚠️  标准化抠图失败: %v\n", err)
			cutoutPath = ""
		} else {
			fmt.Printf("✂️  标准化抠图完成: %s\n", filepath.Base(cutoutPath))
		}
	}

	processingTime := time.Since(startTime).Milliseconds()

	result := &ImageEnhancementResult{
		OriginalPath:   inputPath,
		EnhancedPath:   standardizedPath,
		CutoutPath:     cutoutPath,
		OriginalSize:   Size{Width: img.Bounds().Dx(), Height: img.Bounds().Dy()},
		EnhancedSize:   Size{Width: template.CanvasWidth, Height: template.CanvasHeight},
		ProcessingTime: processingTime,
	}

	fmt.Printf("✅ 智能标准化完成: %s (耗时: %dms)\n", filepath.Base(standardizedPath), processingTime)
	return result, nil
}

// DetectedFaceInfo 详细人脸信息
type DetectedFaceInfo struct {
	X, Y, Width, Height int
	Confidence          float64
	TopY, BottomY       int // 估算的头顶和下巴位置
	LeftX, RightX       int // 估算的脸部左右边界
}

// detectDetailedFaceInfo 检测详细人脸信息
func (e *PhotoEnhancer) detectDetailedFaceInfo(imageData []byte) (*DetectedFaceInfo, error) {
	fmt.Printf("🔍 开始详细人脸分析...\n")

	// 使用腾讯云人脸检测API获取详细信息
	credential := common.NewCredential(e.config.TencentSecretID, e.config.TencentSecretKey)
	cpf := profile.NewClientProfile()
	client, err := iai.NewClient(credential, e.config.TencentRegion, cpf)
	if err != nil {
		return nil, fmt.Errorf("创建人脸识别客户端失败: %v", err)
	}

	request := iai.NewDetectFaceRequest()
	request.Image = common.StringPtr(base64.StdEncoding.EncodeToString(imageData))
	request.MaxFaceNum = common.Uint64Ptr(1)
	request.NeedFaceAttributes = common.Uint64Ptr(1) // 获取详细属性

	response, err := client.DetectFace(request)
	if err != nil {
		return nil, fmt.Errorf("人脸检测API调用失败: %v", err)
	}

	if response.Response.FaceInfos == nil || len(response.Response.FaceInfos) == 0 {
		return nil, fmt.Errorf("未检测到人脸")
	}

	faceInfo := response.Response.FaceInfos[0]

	// 计算详细位置信息
	faceX := int(*faceInfo.X)
	faceY := int(*faceInfo.Y)
	faceWidth := int(*faceInfo.Width)
	faceHeight := int(*faceInfo.Height)

	// 估算头顶和肩膀位置
	topY := faceY - int(float64(faceHeight)*0.5)              // 头顶大约在人脸上方0.5个人脸高度
	bottomY := faceY + int(float64(faceHeight)*2.5)           // 肩膀大约在人脸下方2.5个人脸高度
	leftX := faceX - int(float64(faceWidth)*0.8)              // 左边界扩展
	rightX := faceX + faceWidth + int(float64(faceWidth)*0.8) // 右边界扩展

	detailedInfo := &DetectedFaceInfo{
		X:          faceX,
		Y:          faceY,
		Width:      faceWidth,
		Height:     faceHeight,
		Confidence: 0.9, // SDK通常不返回置信度，设为默认值
		TopY:       topY,
		BottomY:    bottomY,
		LeftX:      leftX,
		RightX:     rightX,
	}

	fmt.Printf("📊 人脸详情: 脸部=%dx%d, 头肩范围=(%d,%d) to (%d,%d)\n",
		faceWidth, faceHeight, leftX, topY, rightX, bottomY)

	return detailedInfo, nil
}

// calculateIntelligentHeadShoulderRegion 智能计算头肩区域
func (e *PhotoEnhancer) calculateIntelligentHeadShoulderRegion(img image.Image, faceInfo *DetectedFaceInfo) image.Rectangle {
	bounds := img.Bounds()
	imgWidth := bounds.Dx()
	imgHeight := bounds.Dy()

	// 基于人脸位置智能计算头肩区域
	// 确保区域在图片范围内
	topY := int(math.Max(0, float64(faceInfo.TopY)))
	bottomY := int(math.Min(float64(imgHeight), float64(faceInfo.BottomY)))
	leftX := int(math.Max(0, float64(faceInfo.LeftX)))
	rightX := int(math.Min(float64(imgWidth), float64(faceInfo.RightX)))

	// 确保最小尺寸
	minWidth := faceInfo.Width * 3
	minHeight := faceInfo.Height * 4

	currentWidth := rightX - leftX
	currentHeight := bottomY - topY

	// 如果当前区域太小，扩展它
	if currentWidth < minWidth {
		expand := (minWidth - currentWidth) / 2
		leftX = int(math.Max(0, float64(leftX-expand)))
		rightX = int(math.Min(float64(imgWidth), float64(rightX+expand)))
	}

	if currentHeight < minHeight {
		expand := (minHeight - currentHeight) / 2
		topY = int(math.Max(0, float64(topY-expand)))
		bottomY = int(math.Min(float64(imgHeight), float64(bottomY+expand)))
	}

	return image.Rect(leftX, topY, rightX, bottomY)
}

// applyStandardTemplate 应用标准模板，生成标准头像
func (e *PhotoEnhancer) applyStandardTemplate(headShoulderImg image.Image, faceInfo *DetectedFaceInfo, template *StandardPortraitTemplate) image.Image {
	fmt.Printf("🎨 应用标准头像模板...\n")

	// 创建标准画布
	canvas := image.NewRGBA(image.Rect(0, 0, template.CanvasWidth, template.CanvasHeight))

	// 设置背景色（浅灰色，适合证件照）
	bgColor := color.RGBA{245, 245, 245, 255}
	for y := 0; y < template.CanvasHeight; y++ {
		for x := 0; x < template.CanvasWidth; x++ {
			canvas.Set(x, y, bgColor)
		}
	}

	// 计算目标人脸大小（基于模板比例）
	targetFaceSize := int(float64(template.CanvasWidth) * template.FaceRatio)

	// 计算缩放比例
	sourceFaceSize := faceInfo.Width
	scale := float64(targetFaceSize) / float64(sourceFaceSize)

	// 缩放头肩图像
	scaledImg := e.resizeImageWithScale(headShoulderImg, scale)

	// 计算人脸在缩放后图像中的新位置
	scaledFaceX := int(float64(faceInfo.X-headShoulderImg.Bounds().Min.X) * scale)
	scaledFaceY := int(float64(faceInfo.Y-headShoulderImg.Bounds().Min.Y) * scale)

	// 计算图像在画布上的位置，使人脸中心位于模板指定位置
	targetFaceCenterX := int(float64(template.CanvasWidth) * template.FaceCenterX)
	targetFaceCenterY := int(float64(template.CanvasHeight) * template.FaceCenterY)

	imageX := targetFaceCenterX - scaledFaceX - int(float64(targetFaceSize)/2)
	imageY := targetFaceCenterY - scaledFaceY - int(float64(targetFaceSize)/2)

	// 将缩放后的图像绘制到画布上
	e.drawImageOnCanvas(canvas, scaledImg, imageX, imageY)

	fmt.Printf("📐 模板应用完成: 画布=%dx%d, 人脸目标大小=%d, 缩放比例=%.2f\n",
		template.CanvasWidth, template.CanvasHeight, targetFaceSize, scale)

	return canvas
}

// resizeImageWithScale 按比例缩放图像
func (e *PhotoEnhancer) resizeImageWithScale(img image.Image, scale float64) image.Image {
	bounds := img.Bounds()
	newWidth := int(float64(bounds.Dx()) * scale)
	newHeight := int(float64(bounds.Dy()) * scale)

	resized := image.NewRGBA(image.Rect(0, 0, newWidth, newHeight))

	// 双线性插值缩放
	for y := 0; y < newHeight; y++ {
		for x := 0; x < newWidth; x++ {
			srcX := float64(x)/scale + float64(bounds.Min.X)
			srcY := float64(y)/scale + float64(bounds.Min.Y)

			// 简化处理：最近邻
			nearX := int(srcX + 0.5)
			nearY := int(srcY + 0.5)

			if nearX >= bounds.Min.X && nearX < bounds.Max.X &&
				nearY >= bounds.Min.Y && nearY < bounds.Max.Y {
				resized.Set(x, y, img.At(nearX, nearY))
			}
		}
	}

	return resized
}

// drawImageOnCanvas 将图像绘制到画布上
func (e *PhotoEnhancer) drawImageOnCanvas(canvas *image.RGBA, img image.Image, offsetX, offsetY int) {
	imgBounds := img.Bounds()
	canvasBounds := canvas.Bounds()

	for y := imgBounds.Min.Y; y < imgBounds.Max.Y; y++ {
		for x := imgBounds.Min.X; x < imgBounds.Max.X; x++ {
			canvasX := x - imgBounds.Min.X + offsetX
			canvasY := y - imgBounds.Min.Y + offsetY

			// 检查是否在画布范围内
			if canvasX >= canvasBounds.Min.X && canvasX < canvasBounds.Max.X &&
				canvasY >= canvasBounds.Min.Y && canvasY < canvasBounds.Max.Y {
				canvas.Set(canvasX, canvasY, img.At(x, y))
			}
		}
	}
}

// saveImageAsPNG 保存图像为PNG格式
func (e *PhotoEnhancer) saveImageAsPNG(img image.Image, filePath string) error {
	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	return png.Encode(file, img)
}

// generateStandardCutout 生成标准化的抠图
func (e *PhotoEnhancer) generateStandardCutout(standardizedImg image.Image, outputPath string) error {
	// 将标准化图像保存为临时文件，然后上传到COS进行抠图
	tempPath := outputPath + "_temp.png"
	err := e.saveImageAsPNG(standardizedImg, tempPath)
	if err != nil {
		return fmt.Errorf("保存临时文件失败: %v", err)
	}
	defer os.Remove(tempPath)

	// 上传到COS
	tempData, err := os.ReadFile(tempPath)
	if err != nil {
		return fmt.Errorf("读取临时文件失败: %v", err)
	}

	cosFileName := fmt.Sprintf("standard_temp_%d.png", time.Now().UnixNano())
	_, err = e.cosClient.Object.Put(context.Background(), cosFileName, bytes.NewReader(tempData), nil)
	if err != nil {
		return fmt.Errorf("上传到COS失败: %v", err)
	}

	// 进行抠图
	err = e.performCutout(cosFileName, outputPath)

	// 清理COS临时文件
	go func() {
		time.Sleep(30 * time.Second)
		e.cosClient.Object.Delete(context.Background(), cosFileName, nil)
	}()

	return err
}

// enhanceImageWithTencentAI 使用腾讯云AI图像超分进行专业增强
func (e *PhotoEnhancer) enhanceImageWithTencentAI(imageData []byte) ([]byte, error) {
	fmt.Printf("🤖 开始腾讯云AI图像超分处理...\n")

	// 检测原图格式，保持格式一致性避免转换损失
	format := "jpg" // 默认格式
	if len(imageData) > 8 {
		// 检测PNG格式（PNG文件头：89 50 4E 47）
		if imageData[0] == 0x89 && imageData[1] == 0x50 && imageData[2] == 0x4E && imageData[3] == 0x47 {
			format = "png"
		}
	}
	fmt.Printf("📋 检测到原图格式: %s\n", format)

	// 1. 上传原图到COS，保持原始格式
	tempFileName := fmt.Sprintf("superres_input_%d.%s", time.Now().UnixNano(), format)
	_, err := e.uploadToCOS(imageData, tempFileName)
	if err != nil {
		return nil, fmt.Errorf("上传原图失败: %v", err)
	}
	fmt.Printf("📤 原图已上传: %s (保持%s格式)\n", tempFileName, format)

	// 2. 构建超分处理的输出文件名（统一输出PNG格式）
	outputFileName := fmt.Sprintf("superres_output_%d.png", time.Now().UnixNano())

	// 3. 调用腾讯云图像超分API
	err = e.callTencentSuperResolutionAPI(tempFileName, outputFileName)
	if err != nil {
		fmt.Printf("❌ 腾讯云AI超分失败: %v\n", err)
		// 清理上传的临时文件
		e.cosClient.Object.Delete(context.Background(), tempFileName, nil)
		return nil, fmt.Errorf("AI超分处理失败: %v", err)
	}
	fmt.Printf("🎯 AI超分处理完成: %s\n", outputFileName)

	// 4. 下载处理后的图片
	enhancedData, err := e.downloadFromCOS(outputFileName)
	if err != nil {
		// 清理临时文件
		e.cosClient.Object.Delete(context.Background(), tempFileName, nil)
		e.cosClient.Object.Delete(context.Background(), outputFileName, nil)
		return nil, fmt.Errorf("下载超分图片失败: %v", err)
	}

	// 5. 延迟清理COS临时文件
	go func() {
		time.Sleep(30 * time.Second) // 延长到30秒，确保处理完成
		fmt.Printf("🧹 清理COS临时文件: %s, %s\n", tempFileName, outputFileName)
		e.cosClient.Object.Delete(context.Background(), tempFileName, nil)
		e.cosClient.Object.Delete(context.Background(), outputFileName, nil)
	}()

	fmt.Printf("✅ AI超分完成: %d bytes -> %d bytes (变化%.1f%%)\n",
		len(imageData), len(enhancedData),
		(float64(len(enhancedData))-float64(len(imageData)))/float64(len(imageData))*100)
	return enhancedData, nil
}

// callTencentSuperResolutionAPI 调用腾讯云图像超分API
func (e *PhotoEnhancer) callTencentSuperResolutionAPI(inputFile, outputFile string) error {
	fmt.Printf("🔥 调用腾讯云图像超分API...\n")

	// 使用COS SDK的CI接口进行图像超分处理
	opt := &cos.ImageProcessOptions{
		IsPicInfo: 1,
		Rules: []cos.PicOperationsRules{
			{
				FileId: outputFile,
				Rule:   "imageMogr2/format/png/quality/95/super-resolution", // PNG格式 + 最高质量
			},
		},
	}

	// 调用图像处理接口
	picProcessResult, _, err := e.cosClient.CI.ImageProcess(context.Background(), inputFile, opt)
	if err != nil {
		return fmt.Errorf("图像超分处理失败: %v", err)
	}

	// 检查处理结果
	if picProcessResult != nil && len(picProcessResult.ProcessResults) > 0 {
		result := picProcessResult.ProcessResults[0]
		if result.Key != outputFile {
			return fmt.Errorf("处理结果文件名不匹配: 期望 %s, 实际 %s", outputFile, result.Key)
		}
		fmt.Printf("✅ 图像超分处理成功: %s -> %s (PNG格式，95%质量)\n", inputFile, result.Key)
	} else {
		return fmt.Errorf("图像超分处理结果为空")
	}

	return nil
}

// uploadToCOS 上传图片到腾讯云COS
func (e *PhotoEnhancer) uploadToCOS(imageData []byte, fileName string) (string, error) {
	// 使用 SDK 上传图片
	_, err := e.cosClient.Object.Put(context.Background(), fileName, bytes.NewReader(imageData), nil)
	if err != nil {
		return "", fmt.Errorf("COS上传失败: %v", err)
	}

	fmt.Printf("✅ 图片已上传到COS: %s\n", fileName)
	return fileName, nil
}

// downloadFromCOS 从COS下载文件
func (e *PhotoEnhancer) downloadFromCOS(fileName string) ([]byte, error) {
	resp, err := e.cosClient.Object.Get(context.Background(), fileName, nil)
	if err != nil {
		return nil, fmt.Errorf("下载文件失败: %v", err)
	}
	defer resp.Body.Close()

	data, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取文件内容失败: %v", err)
	}

	return data, nil
}
