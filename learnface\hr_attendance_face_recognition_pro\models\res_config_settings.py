# -*- coding: utf-8 -*-
from odoo import fields, models, api
from odoo.exceptions import ValidationError


class ResConfigSettingsWebcam(models.TransientModel):
    _inherit = "res.config.settings"

    face_recognition_pro_backend = fields.Selection(
        [
            ("humangl", "humangl"),
            ("wasm", "wasm"),
            ("webgl", "webgl"),
            ("webgpu", "webgpu"),
            ("cpu", "cpu"),
        ],
        string="Backend",
        default="humangl",
    )
    face_recognition_pro_scale_recognition = fields.Integer(
        string="相似脸比例尺",
        help="0-100, 55 - 标准值 大于55 最严格的匹配",
        default=55,
    )
    face_recognition_pro_scale_spoofing = fields.Integer(
        string="反欺骗规",
        help="0-100，100-100%真人，0-禁用，70 -标准值",
        default=70,
    )
    face_recognition_pro_photo_check = fields.Boolean(
        string="启用人脸识别，从照片/图像中检查真人",
        help="仅当用户为真人而非照片时，才可办理签入/签出手续",
    )
    face_recognition_pro_access = fields.<PERSON><PERSON><PERSON>(
        string="启用人脸识别访问",
        help="仅启用人脸识别进行签入/签出用户",
    )
    face_recognition_pro_store = fields.Boolean(
        string="存储员工快照和描述符?",
        help="将用户办理签入/签出时的快照和描述符存储在数据库中进行可视化控制，需要占用大量服务器存储空间",
    )
    face_recognition_pro_kiosk_auto = fields.Boolean(
        string="kiosk模式启用自动人脸识别签或签出",
        help="当摄像头识别到用户时候，自动签入或签出",
    )
    face_recognition_pro_timeout = fields.Integer(
        string="人脸识别超时X失败后5秒，0-禁用超时",
        help="人脸识别超时，未识别而出，识别失败的提醒",
        default=0,
    )

    def set_values(self):
        res = super().set_values()
        cf = self.env["ir.config_parameter"]
        cf.set_param(
            "hr_attendance_face_recognition_pro_access",
            self.face_recognition_pro_access,
        )
        cf.set_param(
            "hr_attendance_face_recognition_pro_store", self.face_recognition_pro_store
        )
        cf.set_param(
            "hr_attendance_face_recognition_pro_kiosk_auto",
            self.face_recognition_pro_kiosk_auto,
        )
        cf.set_param(
            "face_recognition_pro_photo_check", self.face_recognition_pro_photo_check
        )
        cf.set_param("face_recognition_pro_backend", self.face_recognition_pro_backend)
        if (
            self.face_recognition_pro_scale_recognition > 100
            or self.face_recognition_pro_scale_spoofing > 100
            or self.face_recognition_pro_scale_recognition < 0
            or self.face_recognition_pro_scale_spoofing < 0
        ):
            raise ValidationError("错误！请检查比例字段允许的范围 0-100")
        cf.set_param(
            "face_recognition_pro_scale_spoofing",
            self.face_recognition_pro_scale_spoofing,
        )
        cf.set_param("face_recognition_pro_timeout", self.face_recognition_pro_timeout)
        if self.face_recognition_pro_timeout < 0:
            raise ValidationError(
                "错误！请检查面部超时失败。允许值应为0或大于10"
            )
        if (
            self.face_recognition_pro_timeout > 0
            and self.face_recognition_pro_timeout < 10
        ):
            raise ValidationError(
                "错误！请检查面部超时失败。允许值应为0或大于10"
            )
        cf.set_param(
            "face_recognition_pro_scale_recognition",
            self.face_recognition_pro_scale_recognition,
        )
        return res

    @api.model
    def get_values(self):
        res = super().get_values()
        cf = self.env["ir.config_parameter"]
        res.update(
            face_recognition_pro_access=cf.get_param(
                "hr_attendance_face_recognition_pro_access"
            )
        )
        res.update(
            face_recognition_pro_store=cf.get_param(
                "hr_attendance_face_recognition_pro_store"
            )
        )
        res.update(
            face_recognition_pro_kiosk_auto=cf.get_param(
                "hr_attendance_face_recognition_pro_kiosk_auto"
            )
        )
        res.update(
            face_recognition_pro_photo_check=cf.get_param(
                "face_recognition_pro_photo_check"
            )
        )

        res.update(
            face_recognition_pro_scale_recognition=cf.get_param(
                "face_recognition_pro_scale_recognition"
            )
            or 55
        )

        res.update(
            face_recognition_pro_scale_spoofing=cf.get_param(
                "face_recognition_pro_scale_spoofing"
            )
            or 70
        )

        res.update(
            face_recognition_pro_timeout=cf.get_param("face_recognition_pro_timeout")
            or 0
        )

        res.update(
            face_recognition_pro_backend=cf.get_param("face_recognition_pro_backend")
            or "humangl"
        )
        return res
