# 图片优化功能说明

## 功能概述

根据需求说明和测试反馈，我们已经实现了以下全面的图片优化和处理方案：

### 核心问题解决方案

1. **图片过大问题（>5M，特别是PNG 19MB）**
   - ✅ 自动检测图片大小
   - ✅ 超过2.99MB自动压缩到符合腾讯云API要求
   - ✅ 智能压缩策略：JPEG调质量，PNG调尺寸

2. **图片过小问题（分辨率不足）**
   - ✅ DPI估算和检测
   - ✅ 低于100DPI显示警告，低于72DPI进入多层级检测
   - ✅ 用户友好提示："为获得最佳抠图效果，请确保图片分辨率在100dpi以上"

3. **模糊照片检测困难问题**（**新功能**）
   - ✅ 多层级人像检测策略
   - ✅ 图片增强处理（针对模糊照片）
   - ✅ 增强后图片自动传递给抠图处理

## 新的处理流程

```
PPT提取图片 → 图片优化 → 多层级人像检测 → 图片增强（如需要）→ 抠图处理（使用最佳图片）
```

### 多层级人像检测策略

```
层级1: COS人脸检测API（最准确）
  ↓ 失败
层级2: 腾讯云云AI图像超分后检测（专业AI增强）⭐ 最新技术
  ↓ 失败  
层级3: 简单逻辑检测（基于图片特征）
  ↓ 失败
层级4: 模糊照片特殊处理（基于尺寸比例等）
```

## 核心功能详解

### 1. 腾讯云AI图像超分（**最新技术**）⭐ **已修复失真问题**

当检测到低DPI图片（<150dpi）时，系统会自动调用腾讯云专业AI图像超分：

**技术特点**：
- 🤖 **AI智能重建**：使用深度学习算法进行图像超分辨率
- 📈 **2倍分辨率**：智能放大至原图2倍分辨率
- 🔧 **智能去噪**：针对模糊、低质图像进行去噪处理
- 🎯 **无失真增强**：保持图像自然度，避免传统算法的失真问题
- ⚡ **云端处理**：利用腾讯云GPU集群进行高速处理

**🔧 最新修复**（解决失真问题）：
- ✅ **格式优化**：PNG输入输出，避免JPG转换损失
- ✅ **质量提升**：95%质量参数，替代之前的90%
- ✅ **流程优化**：AI超分后直接抠图，跳过传统增强
- ✅ **智能检测**：自动识别AI超分图片，避免重复处理

**API文档**：[腾讯云图像超分](https://cloud.tencent.com/document/product/460/102152)

**优化后的处理流程**：
1. 📤 上传原图到COS（保持原始格式）
2. 🤖 调用AI图像超分API（PNG格式，95%质量）
3. 📥 下载超分处理后的图片
4. ⚡ **直接进行抠图**（跳过传统增强）
5. 🧹 自动清理临时文件

**性能对比**：
```
❌ 修复前：原图 → AI超分(JPG,90%) → 传统增强 → 抠图 (严重失真)
✅ 修复后：原图 → AI超分(PNG,95%) → 直接抠图     (高质量输出)
```

### 2. 智能头像标准化（**头肩占位符技术**）⭐ **全新功能**

无论原图拍摄得多么不标准，都能生成标准格式的证件照风格头像：

**解决的问题**：
- 😵 **头部空白过多**：如您提供的医生照片，顶部留白过多
- 📐 **人物位置不标准**：人脸不在标准位置
- 📏 **尺寸比例问题**：头肩比例不符合证件照标准
- 🎯 **拍摄角度随意**：任意角度拍摄的照片

**技术特点**：
- 🎯 **智能人脸定位**：精确检测人脸位置和大小
- 📏 **头肩区域计算**：智能计算最佳头肩裁剪区域
- 🎨 **标准模板应用**：按证件照标准生成600x800画布
- 📐 **比例智能调整**：人脸占画布25%，位于标准位置
- 🖼️ **背景标准化**：统一的浅灰色背景

**标准模板参数**：
```
画布尺寸: 600x800 (4:3比例)
人脸大小: 占画布宽度25%
人脸位置: 水平居中，垂直35%位置
头顶留白: 15%
肩膀显示: 75%区域
背景色: 浅灰色(245,245,245)
```

**处理流程**：
1. 🔍 **精确人脸检测**：获取人脸位置、大小等详细信息
2. 📐 **智能区域计算**：基于人脸位置计算头肩区域
3. ✂️ **智能裁剪**：提取包含头肩的最佳区域
4. 📏 **比例标准化**：按标准比例缩放人物大小
5. 🎯 **精确定位**：将人脸放置在标准位置
6. 🎨 **模板应用**：生成标准证件照格式
7. ✂️ **可选抠图**：可进一步生成透明背景版本

### 3. 增强图片传递机制（**新功能**）

- 人像检测成功时，自动保存增强后的图片（`*_enhanced.jpg`）
- 抠图处理时优先使用增强后的图片
- 确保整个流程使用同一版本的优化图片

### 3. 模糊照片特殊检测（**新功能**）

对于极低DPI图片（<120dpi），使用特殊判断逻辑：

**判断标准**：
- ✓ 竖向比例（人像通常竖向）+0.3置信度
- ✓ 尺寸合理（≥200x300）+0.2置信度  
- ✓ 文件大小（50KB-5MB）+0.2置信度
- ✓ DPI适中（72-120）+0.2置信度
- **阈值**：总置信度≥0.6判定为人像

## 配置说明

在 `main/config.json` 中的配置项：

```json
{
  "photo_enhancement": {
    "max_image_size_for_api": 2.99,  // API允许的最大图片大小(MB)
    "compression_quality": 85,        // 压缩质量(1-100)
    "min_dpi": 100,                  // 最小DPI要求
    "enable_size_check": true,       // 是否启用图片大小检查
    "enable_dpi_check": true,        // 是否启用DPI检查
    "enhancement_scale": 1.5,        // 图片增强放大倍数
    "contrast_factor": 1.2,          // 对比度增强因子
    "sharpen_enabled": true,         // 是否启用锐化处理
    "enable_portrait_standardization": true  // ⭐ 新增：启用智能头像标准化
  }
}
```

### 智能头像标准化使用方法

**启用方式**：
```json
"enable_portrait_standardization": true
```

**效果对比**：
- ❌ **关闭时**：保持原图处理方式，可能有头部空白过多等问题
- ✅ **开启时**：无论原图怎么拍，都生成标准证件照格式

## 日志输出说明

### AI超分处理（修复后）⭐ **无失真高质量**
```
📸 处理图片 1/1: image8.png
🔧 开始处理图片: image8.png
🎯 开始多层级人像检测 (DPI=120)...
🔍 层级1: COS人脸检测API
❌ 层级1失败: 未检测到人脸
🔍 层级2: 腾讯云AI图像超分后检测 (触发条件: DPI 120 < 150)
🤖 开始腾讯云AI图像超分处理...
📋 检测到原图格式: png
📤 原图已上传: superres_input_xxx.png (保持png格式)
🔥 调用腾讯云图像超分API...
✅ 图像超分处理成功: superres_input_xxx.png -> superres_output_xxx.png (PNG格式，95%质量)
🎯 AI超分处理完成: superres_output_xxx.png
✅ AI超分完成: 2457600 bytes -> 105472 bytes (变化-95.7%)
✅ 层级2成功: AI超分后检测到人脸 (置信度: 0.80)
💾 保存AI超分图片: image8_ai_superres.png
✅ 增强图片已保存: image8_ai_superres.png (大小: 0.10MB)

🔧 使用增强图片进行抠图: image8_ai_superres.png
🤖 检测到AI超分图片，跳过传统增强步骤
🚀 直接处理AI超分图片: image8_ai_superres.png
📊 AI超分图片信息: 1024x1365, 0.10MB
✅ AI超分图片无需压缩，直接使用
✅ AI超分图片已上传到COS进行抠图: ai_superres_temp_xxx.png
✂️  AI超分图片抠图完成: image8_ai_superres_cutout.png
✅ AI超分图片处理完成: image8_ai_superres.png (耗时: 1250ms)
```

### 智能头像标准化（新功能）⭐ **解决头部空白问题**
```
📸 处理图片 1/1: doctor_photo.jpg
🔧 开始处理图片: doctor_photo.jpg
🎯 启用智能头像标准化模式
🎯 开始智能头像标准化: doctor_photo.jpg
🔍 开始详细人脸分析...
📍 检测到人脸: 156x205 at (342,320)
📊 人脸详情: 脸部=156x205, 头肩范围=(217,218) to (623,833)
👤 计算头肩区域: 406x615 at (217,218)
🎨 应用标准头像模板...
📐 模板应用完成: 画布=600x800, 人脸目标大小=150, 缩放比例=0.96
✂️  标准化抠图完成: doctor_photo_standard_cutout.png
✅ 智能标准化完成: doctor_photo_standardized.png (耗时: 2840ms)
```

### 修复对比说明

**❌ 修复前的问题日志**：
```
✅ 层级2成功: AI超分后检测到人脸
💾 保存AI超分图片: image8_ai_superres.png (103KB 高质量)
🔧 使用增强图片进行抠图: image8_ai_superres.png
⚠️  图片DPI过低，将进行无损放大和增强  ← 错误：重复处理
✅ 增强后图片: image8_ai_superres_enhanced_for_detect.png (250KB 失真)
```

**✅ 修复后的正确日志**：
```
✅ 层级2成功: AI超分后检测到人脸
💾 保存AI超分图片: image8_ai_superres.png (103KB 高质量)
🔧 使用增强图片进行抠图: image8_ai_superres.png
🤖 检测到AI超分图片，跳过传统增强步骤  ← 修复：智能跳过
✅ AI超分图片直接抠图: image8_ai_superres_cutout.png (高质量)
```

### 正常处理（层级1成功）
```
📸 处理图片 2/3: normal_photo.jpg
🔧 开始处理图片: normal_photo.jpg
📏 图片大小检查: 1.50MB <= 2.99MB，无需压缩
🎯 开始多层级人像检测 (DPI=180)...
🔍 层级1: COS人脸检测API
✅ 层级1成功: 检测到人脸 (置信度: 0.95)
✅ 优化图片已上传到COS进行增强处理: enhance_temp_xxx.jpg
✂️  人像分割完成: normal_photo_cutout.png
```

### 腾讯云AI图像超分检测（层级2成功）⭐ **最新技术展示**
```
📸 处理图片 2/3: blurry_doctor.jpg
📦 图片需要压缩: 8.50MB > 2.99MB
✅ 图片压缩完成: 8.50MB -> 2.80MB
🎯 开始多层级人像检测...
🔍 层级1: COS人脸检测API
⚠️  COS人脸检测失败: 未检测到人脸
🔍 层级2: 腾讯云AI图像超分后检测
🤖 开始腾讯云AI图像超分处理...
📤 原图已上传: superres_input_1640995200123456789.jpg
🔥 调用腾讯云图像超分API...
✅ 图像超分处理成功: superres_input_1640995200123456789.jpg -> superres_output_1640995200987654321.jpg
🎯 AI超分处理完成: superres_output_1640995200987654321.jpg
✅ AI超分完成: 2867456 bytes -> 4523789 bytes
✅ 层级2成功: 超分后检测到人脸 (置信度: 0.85)
💾 已保存增强图片: blurry_doctor_enhanced.jpg
🎯 使用增强后的图片进行抠图: blurry_doctor_enhanced.jpg
✅ 人像分割完成
```

### 模糊照片特殊处理（层级4成功）
```
📸 处理图片 3/3: low_quality.jpg
🎯 开始多层级人像检测...
🔍 层级1: COS人脸检测API
⚠️  COS人脸检测失败: 未检测到人脸
🔍 层级2: 图片增强后检测
⚠️  增强后COS检测失败: 未检测到人脸
🔍 层级3: 简单逻辑检测
⚠️  简单检测失败: 置信度不足
🔍 层级4: 模糊照片特殊处理
📏 模糊照片分析: 400x600, 大小: 156789 bytes, 比例: 1.50, DPI: 95
   ✓ 竖向比例 (1.50), 置信度 +0.3
   ✓ 尺寸合理 (400x600), 置信度 +0.2
   ✓ 文件大小合理 (153.1KB), 置信度 +0.2
   ✓ DPI适中 (95), 置信度 +0.2
✅ 层级4成功: 模糊照片判断为人像 (总置信度: 0.90 >= 0.60)
```

### 所有层级失败
```
📸 处理图片 4/4: landscape.jpg
🎯 开始多层级人像检测...
🔍 层级1: COS人脸检测API
⚠️  COS人脸检测失败: 未检测到人脸
🔍 层级3: 简单逻辑检测
⚠️  简单检测失败: 非人像特征
❌ 所有层级检测均失败，判定为非人像
```

## 统计信息（更新）

处理完成后会显示详细统计：

```
📊 图片处理完成:
   总图片数: 4
   人像图片: 3  ⬅️ 多层级检测提高了成功率
   处理成功: 3
   处理时间: 2580ms

📈 图片优化统计:
   压缩图片数: 2/4
   增强图片数: 1/4  ⬅️ 新增统计
   总原始大小: 15.20MB
   总压缩后大小: 8.90MB
   平均压缩比: 58.6%
   节省空间: 6.30MB
   低DPI图片: 2/4
   质量警告: 2/4
   
🎯 多层级检测统计:  ⬅️ 新增统计
   层级1成功: 1/4
   层级2成功: 1/4 (图片增强)
   层级3成功: 0/4
   层级4成功: 1/4 (模糊照片)
   
💡 建议：1张图片通过增强处理成功检测，系统自动优化效果显著！
```

## 文件输出说明（**新增**）

处理完成后，输出目录会包含：

### 原始处理文件
- `image1.jpg` - 原始提取的图片
- `image1_cutout.png` - 抠图结果

### 增强处理文件（**新增**）
- `image1_enhanced.jpg` - 增强后的图片（仅在层级2成功时生成）
- `image1_enhanced_for_detect.png` - 检测用增强图片（调试用）

### 结果文件
- `image_processing_results.json` - 详细处理结果
- `best_portrait.png` - 最佳头像（基于增强图片生成）

## 测试建议（更新）

### 测试用例

1. **模糊照片测试**⭐：准备一张模糊的医生证件照
2. **大图片测试**：准备一张>5MB的PNG或JPEG图片
3. **小图片测试**：准备一张<500KB的低分辨率图片  
4. **正常图片测试**：准备一张1-3MB的正常人像图片
5. **非人像测试**：准备一张风景或物品图片

### 期望结果

- **模糊照片**：层级2成功，使用腾讯云AI超分，生成`*_enhanced.jpg`文件，抠图使用AI增强版本
- **大图片**：自动压缩+正常检测
- **小图片**：多层级检测，可能在层级3或4成功
- **正常图片**：层级1直接成功
- **非人像**：所有层级失败，正确判定为非人像

## 技术特性（**最新更新**）

### AI超分算法（**腾讯云**）
- **深度学习模型**：使用业界领先的超分辨率神经网络
- **智能重建**：AI算法重建图像细节，而非简单插值
- **去噪处理**：专门针对模糊、低质图像的噪声处理
- **2倍超分**：标准的2倍分辨率提升，平衡效果与性能

### 检测算法
- **多维度评估**：尺寸比例、文件大小、DPI等综合判断
- **阈值可调**：所有检测阈值都可通过配置调整
- **渐进式检测**：从高精度到特殊情况逐级检测

### 性能优化
- **云端处理**：利用腾讯云GPU集群，本地无需高性能硬件
- **自动清理**：临时文件自动删除，不占用存储空间
- **按需处理**：只对低DPI图片进行AI超分，避免不必要的处理

### 成本控制
- **智能触发**：仅在DPI<150时触发，避免过度使用
- **文件清理**：处理后自动删除临时文件，节省存储费用
- **高效检测**：使用AI增强版本进行检测，提高成功率

## 注意事项

1. **腾讯云配置**：需要正确配置COS密钥和区域，确保CI服务可用
2. **网络要求**：AI超分需要稳定的网络连接到腾讯云
3. **处理时间**：AI超分比传统方法慢一些，但效果显著更好
4. **费用考虑**：图像超分按次计费，建议监控使用量
5. **兼容性**：保持原有简单检测接口的兼容性，降级策略完整

## 实际使用示例

### 💡 智能头像标准化使用场景

**问题场景**：医生照片头部空白过多，不符合证件照标准
- 原图：医生穿白大褂，头部上方空白过多，人物位置偏下
- 需求：生成标准证件照格式，头肩比例合适

**解决方案**：启用智能头像标准化

### 🔧 配置步骤

1. **修改配置文件** `main/config.json`：
```json
{
  "photo_enhancement": {
    "enable_portrait_standardization": true,  // 启用智能标准化
    "max_image_size_for_api": 2.99,
    "compression_quality": 85,
    "min_dpi": 150
  }
}
```

2. **上传图片**：将您的医生照片上传到系统

3. **自动处理**：系统会自动：
   - 🔍 检测人脸位置和大小
   - 📐 计算最佳头肩区域
   - 🎨 应用标准证件照模板
   - ✂️ 可选生成透明背景版本

### 📊 处理结果

**输出文件**：
- `doctor_photo_standardized.png` - 标准化头像（600x800）
- `doctor_photo_standard_cutout.png` - 透明背景版本（可选）

**效果对比**：
- ❌ **原图问题**：头部空白过多，人物位置随意
- ✅ **标准化后**：人脸居中，头肩比例标准，适合证件照使用

### 🎯 标准模板效果

最终生成的标准头像将符合以下规范：
- **画布**: 600×800像素（4:3比例）
- **人脸大小**: 占画布宽度25%（约150像素）
- **人脸位置**: 水平居中，垂直35%位置
- **头顶留白**: 15%空间
- **肩膀显示**: 显示到75%位置
- **背景**: 统一浅灰色，适合证件照

### ⚡ 性能指标

- **处理时间**: 通常2-5秒
- **输出质量**: 高清PNG格式
- **准确率**: 基于腾讯云人脸检测API，准确率>95%
- **适用范围**: 各种角度、光线条件下的人像照片

---

## 技术优势

### 🆚 传统方法 vs 智能标准化

| 处理方式 | 传统裁剪 | 智能标准化 |
|----------|----------|------------|
| 🎯 **定位精度** | 手动估算 | AI精确检测 |
| 📐 **比例标准** | 可能不准 | 严格符合证件照标准 |
| 🖼️ **背景处理** | 保持原背景 | 统一标准背景 |
| ⚡ **处理速度** | 需人工调整 | 全自动3秒完成 |
| 🎨 **输出质量** | 参差不齐 | 统一高质量 |

### 🔮 适用场景

✅ **完美适用**：
- 员工证件照制作
- 医生执业照片标准化
- 学生证件照处理
- 简历头像优化
- 企业通讯录统一

⚠️ **不适用**：
- 群体照片
- 非人像图片
- 艺术摄影作品

## 技术原理说明

### 🔬 AI超分 vs 传统增强

#### **AI超分技术**：
- 🧠 **深度学习模型**：基于大量训练数据的神经网络
- 🎯 **智能重建**：理解图像内容，重建缺失的细节
- 📈 **真实细节**：生成真实存在的纹理和边缘
- 🌟 **自然效果**：保持图像的自然观感

#### **传统增强技术**：
- 📏 **简单缩放**：线性或双线性插值放大
- 🔧 **卷积滤波**：锐化、对比度调整等
- ⚡ **快速处理**：计算简单，速度快
- 📐 **数学运算**：基于像素邻域的数学计算

### ⚠️ **为什么不能叠加处理？**

#### **问题1：技术冲突**
```
AI超分：智能生成细节 + 传统增强：强制锐化 = 过度处理失真
```

#### **问题2：格式损失**
```
PNG原图 → JPG上传 → AI超分 → PNG保存 → 传统增强 = 多次转换损失
```

#### **问题3：质量下降**
```
AI专业增强(95%质量) + 传统增强(1.5倍放大+锐化) = 画质劣化
```

### ✅ **修复原理**

#### **智能分流处理**：
1. **检测文件名**：识别 `_ai_superres` 标识
2. **跳过传统增强**：AI超分图片直接进入抠图流程
3. **保持高质量**：避免重复处理造成的质量损失
4. **格式统一**：全程PNG格式，避免转换损失

#### **处理路径对比**：
```
修复前：原图 → AI超分 → 传统增强 → 抠图 (3个损失点)
修复后：原图 → AI超分 → 直接抠图       (1个处理点)
```

### 📊 **质量提升效果**

| 项目 | 修复前 | 修复后 | 改善 |
|------|--------|--------|------|
| **格式处理** | PNG→JPG→PNG | PNG→PNG | ✅ 避免转换损失 |
| **质量参数** | 90% | 95% | ✅ +5%质量提升 |
| **处理步骤** | 3步骤 | 2步骤 | ✅ 减少1个损失点 |
| **文件大小** | 250KB失真 | 103KB高质量 | ✅ 体积更小质量更高 |
| **处理时间** | 慢（重复处理） | 快（直接处理） | ✅ 性能提升 |

这就是为什么**AI超分后直接抠图**比**AI超分+传统增强+抠图**效果更好的技术原因！🎯

## 系统优化说明

### 📂 **路径处理优化** ⭐ **最新修复**

**问题**：之前系统在日志和配置中显示绝对路径，不利于项目移植和维护。

**修复内容**：
- ✅ **日志优化**：所有DEBUG日志显示相对路径而非绝对路径
- ✅ **路径转换**：改进 `convertToRelativePath` 函数，准确转换为项目相对路径
- ✅ **配置存储**：JSON配置文件中存储相对路径
- ✅ **跨平台兼容**：自动处理Windows/Linux路径差异

**修复前后对比**：
```
❌ 修复前:
DEBUG: BestPortrait: D:\Dev\gejiesoft-pdf-tools\output\VCOUTPUTDATA20250718202615\image8_ai_superres_cutout.png
DEBUG: 检测到本地环境，项目根目录: D:\Dev\gejiesoft-pdf-tools

✅ 修复后:
DEBUG: 检测到最佳头像: VCOUTPUTDATA20250718202615/image8_ai_superres_cutout.png
DEBUG: 检测到本地环境，使用项目根目录
```

**优势**：
- 🔄 **项目移植性**：可在不同环境下正常运行
- 📋 **日志简洁性**：相对路径更易读
- 🔒 **隐私保护**：不暴露用户本地目录结构
- ⚙️ **配置通用性**：配置文件可跨环境使用

### 🔧 **AI超分失真修复** ⭐ **技术升级**

**核心问题**：AI超分后进行传统增强导致严重失真

**解决方案**：
- ✅ **智能分流**：自动识别AI超分图片，跳过传统增强
- ✅ **格式优化**：PNG全程处理，避免JPG转换损失
- ✅ **质量提升**：95%质量参数替代90%
- ✅ **流程简化**：减少处理环节，降低损失累积

**技术优势**：
```
修复前：原图 → AI超分(JPG,90%) → 传统增强 → 抠图 (严重失真)
修复后：原图 → AI超分(PNG,95%) → 直接抠图     (高质量输出)
```

### 🎯 **智能头像标准化** ⭐ **全新功能**

**核心功能**：头肩占位符技术，解决头部空白过多问题

**技术特点**：
- 📐 **标准模板**：600×800证件照格式
- 🎯 **智能定位**：人脸自动居中于标准位置
- 🖼️ **背景统一**：统一浅灰色背景
- ✂️ **比例标准**：严格按证件照规范生成

**适用场景**：员工证件照、医生执业照片、学生证件照等

### 📝 **路径处理测试示例**

**测试场景**：处理医生照片 `image8.png`

**修复后的正确日志输出**：
```
📸 处理图片 1/1: image8.png
🔧 开始处理图片: image8.png
🎯 开始多层级人像检测 (DPI=120)...
🔍 层级2: 腾讯云AI图像超分后检测
🤖 开始腾讯云AI图像超分处理...
📋 检测到原图格式: png
✅ AI超分完成: 2457600 bytes -> 105472 bytes (变化-95.7%)
✅ 层级2成功: AI超分后检测到人脸 (置信度: 0.80)
💾 保存AI超分图片: image8_ai_superres.png
✅ 增强图片已保存: image8_ai_superres.png (大小: 0.10MB)

🔧 使用增强图片进行抠图: image8_ai_superres.png
🤖 检测到AI超分图片，跳过传统增强步骤
🚀 直接处理AI超分图片: image8_ai_superres.png
✅ 已设置抠图头像: image8_ai_superres_cutout.png
DEBUG: 最佳头像路径: VCOUTPUTDATA20250718202615/image8_ai_superres_cutout.png
DEBUG: 检测到最佳头像: VCOUTPUTDATA20250718202615/image8_ai_superres_cutout.png
DEBUG: 转换后的头像路径: VCOUTPUTDATA20250718202615/image8_ai_superres_cutout.png
DEBUG: 检测到本地环境，使用项目根目录
DEBUG: 尝试加载的头像路径: VCOUTPUTDATA20250718202615/image8_ai_superres_cutout.png
DEBUG: 头像文件存在，准备插入到PPT
```

**关键改进**：
- ✅ 所有路径显示为 `VCOUTPUTDATA20250718202615/image8_ai_superres_cutout.png`
- ✅ 不再显示 `D:\Dev\gejiesoft-pdf-tools\` 等绝对路径
- ✅ 项目根目录信息不暴露具体路径
- ✅ 配置文件中存储的也是相对路径

**JSON配置示例**：
```json
{
  "姓名": "张晨君",
  "医院": "上海和睦家新城",
  "头像": "VCOUTPUTDATA20250718202615/image8_ai_superres_cutout.png",
  "处理统计": {
    "best_portrait": "output/VCOUTPUTDATA20250718202615/image8_ai_superres_cutout.png"
  }
}
```

### 🎯 **智能鼻尖居中定位** ⭐ **全新修复**

**解决的问题**：抠图后头顶留白过多，鼻尖位置不在中央

**问题原因**：
- ❌ **底部对齐算法**：之前使用底部对齐，导致头顶大面积留白
- ❌ **固定留白比例**：顶部5%、底部0%的固定设计不合理
- ❌ **坐标系混乱**：人脸检测坐标在图像裁剪后未正确调整

**🔧 智能居中算法**：

#### **1. 人脸中心检测**
```
人脸检测 → 计算鼻尖位置 → 调整坐标系 → 居中定位
```

#### **2. 智能留白策略**
```
修复前：左右5% + 顶部5% + 底部0% = 头顶留白过多
修复后：左右5% + 上下10% + 脸部居中 = 鼻尖位于中央
```

#### **3. 坐标系智能调整**
```
原图坐标 → 头肩裁剪 → 调整人脸坐标 → 脸部中心计算 → 画布居中
```

**处理算法**：
1. **人脸检测**：获取原图中人脸位置
2. **头肩裁剪**：智能计算头肩区域
3. **坐标调整**：将人脸坐标转换到裁剪图像坐标系
4. **中心计算**：计算鼻尖（脸部中心）位置
5. **居中定位**：将脸部中心放置在正方形画布中央
6. **边界检查**：确保人像不超出画布范围

**效果对比**：
```
❌ 修复前：
┌─────────────┐
│   大量留白   │ ← 头顶留白过多
│     👤      │
│    肩膀     │
└─────────────┘

✅ 修复后：
┌─────────────┐
│   适量留白   │
│     👤      │ ← 鼻尖居中
│   适量留白   │
└─────────────┘
```

**日志示例**：
```
✅ 检测到人脸位置: 156x205 at (342,320)
📐 智能头肩裁剪区域: 436x580 at (217,180)
🔄 调整后人脸坐标: 156x205 at (125,140) (相对于裁剪图像)
🎯 使用脸部中心定位算法
📐 脸部中心: (203,242) -> 缩放后: (195,232) -> 画布中心偏移: (105,68)
DEBUG: 最终人像位置: (105,68) 尺寸: 436x580，画布: 600x600
```

**技术优势**：
- 🎯 **精确定位**：鼻尖精确居中，符合证件照标准
- 🧠 **智能适应**：自动适应不同拍摄角度和构图
- 📐 **比例优化**：合理的上下留白，避免头重脚轻
- 🔄 **坐标准确**：正确处理多次坐标系变换

### 🧪 **鼻尖居中测试验证**

**测试场景**：处理 `image8_ai_superres.png`

**修复后的预期效果**：
1. **鼻尖位置**：位于正方形画布的中央位置
2. **头顶留白**：适量留白，不再有大面积空白
3. **肩膀显示**：包含适当的肩膀部分
4. **整体比例**：符合证件照标准

**验证方法**：
```
1. 上传医生照片进行处理
2. 查看生成的 *_cutout.png 文件
3. 检查鼻尖是否位于图片中央
4. 确认头顶留白是否合理
```

**质量标准**：
- ✅ **鼻尖居中**：鼻尖位置偏离中心不超过画布的5%
- ✅ **留白均匀**：上下留白基本均匀，避免头重脚轻
- ✅ **人像完整**：包含完整的头肩部分
- ✅ **比例标准**：符合证件照的构图标准

**常见问题解决**：
```
问题1：鼻尖偏上 → 检查人脸检测是否准确
问题2：鼻尖偏下 → 检查头肩裁剪区域计算
问题3：左右偏移 → 检查坐标系调整算法
问题4：整体偏移 → 检查边界检查逻辑
```