package main

import (
	"fmt"
	"os"
	"strconv"
	"testing"
)

// TestLoadConfig 测试配置加载功能
func TestLoadConfig(t *testing.T) {
	// 保存原始环境变量
	originalPort := os.Getenv("PORT")
	originalAPIKey := os.Getenv("AIBOT_API_KEY")

	// 测试完成后恢复环境变量
	defer func() {
		os.Setenv("PORT", originalPort)
		os.Setenv("AIBOT_API_KEY", originalAPIKey)
	}()

	// 测试用例1：使用环境变量
	os.Setenv("PORT", "9999")
	os.Setenv("AIBOT_API_KEY", "test_key")

	config := loadConfig()

	if config.Port != "9999" {
		t.<PERSON><PERSON><PERSON>("Expected port 9999, got %s", config.Port)
	}

	if config.AibotAPIKey != "test_key" {
		t.<PERSON><PERSON>("Expected API key 'test_key', got %s", config.AibotAPIKey)
	}
}

// TestValidateConfig 测试配置验证功能
func TestValidateConfig(t *testing.T) {
	tests := []struct {
		name        string
		config      *Config
		shouldError bool
	}{
		{
			name: "valid config",
			config: &Config{
				Port:             "8088",
				AibotAPIKey:      "valid_key",
				TencentSecretID:  "valid_id",
				TencentSecretKey: "valid_key",
				TencentBucket:    "valid_bucket",
			},
			shouldError: false,
		},
		{
			name: "missing API key",
			config: &Config{
				Port:             "8088",
				AibotAPIKey:      "",
				TencentSecretID:  "valid_id",
				TencentSecretKey: "valid_key",
				TencentBucket:    "valid_bucket",
			},
			shouldError: true,
		},
		{
			name: "invalid port",
			config: &Config{
				Port:             "invalid",
				AibotAPIKey:      "valid_key",
				TencentSecretID:  "valid_id",
				TencentSecretKey: "valid_key",
				TencentBucket:    "valid_bucket",
			},
			shouldError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateConfig(tt.config)
			if tt.shouldError && err == nil {
				t.Errorf("Expected error for %s, got nil", tt.name)
			}
			if !tt.shouldError && err != nil {
				t.Errorf("Unexpected error for %s: %v", tt.name, err)
			}
		})
	}
}

// TestGetDefaultConfig 测试默认配置获取
func TestGetDefaultConfig(t *testing.T) {
	config := getDefaultConfig()

	if config.Port != "8088" {
		t.Errorf("Expected default port 8088, got %s", config.Port)
	}

	if config.AibotEndpoint != "https://aibot.gejiesoft.com/v1" {
		t.Errorf("Expected default endpoint, got %s", config.AibotEndpoint)
	}

	if config.TencentRegion != "ap-shanghai" {
		t.Errorf("Expected default region ap-shanghai, got %s", config.TencentRegion)
	}
}

// 辅助函数：验证配置
func validateConfig(config *Config) error {
	if config.AibotAPIKey == "" {
		return fmt.Errorf("AIBOT_API_KEY is required")
	}
	if config.TencentSecretID == "" {
		return fmt.Errorf("TENCENT_SECRET_ID is required")
	}
	if config.TencentSecretKey == "" {
		return fmt.Errorf("TENCENT_SECRET_KEY is required")
	}
	if config.TencentBucket == "" {
		return fmt.Errorf("TENCENT_BUCKET is required")
	}

	// 验证端口格式
	if _, err := strconv.Atoi(config.Port); err != nil {
		return fmt.Errorf("invalid port format: %s", config.Port)
	}

	return nil
}

// 辅助函数：获取默认配置
func getDefaultConfig() *Config {
	return &Config{
		Port:          "8088",
		AibotEndpoint: "https://aibot.gejiesoft.com/v1",
		AibotModel:    "简历信息提取",
		TencentRegion: "ap-shanghai",
	}
}
