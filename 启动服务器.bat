@echo off
chcp 65001
echo 🚀 启动头像抠图处理系统本地服务器 (支持背景移除功能)
echo.

echo 正在检查环境...
echo.

REM 检查Python环境
echo 📋 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Python，请先安装Python
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python环境正常

REM 检查Node.js环境
echo 📋 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js环境正常

REM 检查rembg依赖
echo 📋 检查背景移除依赖...
python -c "import rembg" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  未找到rembg库，背景移除功能可能无法使用
    echo 💡 安装命令: pip install rembg onnxruntime
    echo.
) else (
    echo ✅ 背景移除依赖正常
)

echo.
echo 🌐 启动增强版HTTP服务器 (支持API功能)...
echo 服务器地址: http://localhost:8000
echo.
echo 📖 使用说明:
echo 1. 保持此窗口打开
echo 2. 在浏览器中访问: http://localhost:8000/start.html
echo 3. 支持背景移除功能的头像处理
echo 4. 按 Ctrl+C 停止服务器
echo.
echo 🎭 功能特性:
echo • 智能人脸检测和头像生成
echo • AI背景移除 (u2net_human_seg模型)
echo • 批量处理支持
echo • 实时预览和下载
echo.

node server.js

pause
