# -*- coding: utf-8 -*-
from odoo import models, fields, api, _


class HrAttendance(models.Model):
    _inherit = "hr.attendance"

    face_recognition_image_check_in = fields.Binary(string="Face recognition check in", readonly=True)
    face_recognition_image_check_out = fields.Binary(string="Face recognition check out", readonly=True)
    face_recognition_access_check_in = fields.Html(string="FaceID in", readonly=True)
    face_recognition_access_check_out = fields.Html(string="FaceID out", readonly=True)

    webcam_check_in = fields.Binary(string="摄像头签到", readonly=True)
    webcam_check_out = fields.Binary(string="摄像头签出", readonly=True)
