<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_employee_form_face_recognition" model="ir.ui.view">
            <field name="name">view.employee.form.face.recognition</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_form"/>
            <field name="priority" eval="99"/>
            <field name="arch" type="xml">
                <xpath expr="//notebook" position="inside">
                    <page name="Face recognition" string="人脸识别员工">
                        <group string="脸部滤镜">
                            <field name="face_emotion"/>
                            <field name="face_gender"/>
                            <field name="face_age"/>
                        </group>
                        <group name="face_recognition_images" string="识别图像">
                            <field name="recognition_image_ids"
                            class="o_website_sale_image_list"
                            context="{'default_name': name, 'default_hr_employee_id': id}"
                            mode="kanban"
                            options="{'create_text':'Add face'}"
                            nolabel="1"/>
                        </group>
                    </page>
                </xpath>
            </field>
        </record>

        <!-- This view should only be used from the product o2m because the required field product_tmpl_id has to be automatically set. -->
        <record id="view_hr_employee_image_form" model="ir.ui.view">
            <field name="name">hr.employee.image.view.form</field>
            <field name="model">hr.employee.image</field>
            <field name="arch" type="xml">
                <form string="员工识别图像">
                    <field name="sequence" invisible="1"/>
                    <div class="row o_website_sale_image_modal">
                        <div class="col-md-6 col-xl-5">
                            <label for="name" string="图像名称"/>
                            <h2><field name="name" placeholder="图像名称"/></h2>
                            <label for="name" string="关联员工"/>
                            <field name="descriptor"/>
                            <field name="hr_employee_id" readonly="1"/>
                        </div>
                        <div class="col-md-6 col-xl-7 text-center o_website_sale_image_modal_container">
                            <div class="row">
                                <div class="col" id="face-recognition-image">
                                    <field name="image" widget="image"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </field>
        </record>

        <record id="hr_employee_image_view_kanban" model="ir.ui.view">
            <field name="name">hr.employee.image.view.kanban</field>
            <field name="model">hr.employee.image</field>
            <field name="arch" type="xml">
                <kanban string="员工识别图像" default_order="sequence">
                    <field name="id"/>
                    <field name="name"/>
                    <field name="image"/>
                    <field name="descriptor"/>
                    <field name="sequence" widget="handle"/>
                    <input type="checkbox" id="toggle-face-rec" />
                    <templates>
                        <t t-name="kanban-box">
                            <div class="card oe_kanban_global_click p-0">
                                <div class="o_squared_image">

                                    <t t-if="record.descriptor.value">
                                        <img class="card-img-top" t-att-src="kanban_image('hr.employee.image', 'image', record.id.value)" t-att-alt="record.name.value" t-att-data-id="record.id.value" t-att-data-descriptor="1" />
                                    </t>
                                    <t t-else="1">
                                        <img class="card-img-top" t-att-src="kanban_image('hr.employee.image', 'image', record.id.value)" t-att-alt="record.name.value" t-att-data-id="record.id.value" t-att-data-descriptor="0" />
                                    </t>
                                    <img class="card-img-top only-descriptor" t-att-src="kanban_image('hr.employee.image', 'image_detection', record.id.value)" t-att-alt="record.name.value" />
                                </div>
                                <div class="card-body p-0">
                                    <h4 class="card-title p-2 m-0 bg-200">
                                        <small>
                                            <field name="name"/>
                                        </small>
                                    </h4>
                                </div>
                                <!-- below 100 Kb: bad -->
                                <t t-if="record.image.raw_value.length &lt; 100*1000">
                                    <t t-set="size_status" t-value="'badge-danger'"/>
                                    <t t-set="message">可接受的文件大小</t>
                                </t>
                                <!-- below 1000 Kb: decent -->
                                <t t-elif="record.image.raw_value.length &lt; 1000*1000">
                                    <t t-set="size_status" t-value="'badge-warning'" />
                                    <t t-set="message">文件尺寸太大，应优化/缩小图像。</t>
                                </t>
                                <!-- above 1000 Kb: good -->
                                <t t-else="1">
                                    <t t-set="size_status" t-value="'badge-success'"/>
                                    <t t-set="message">需要优化！减小图像大小或增加压缩设置</t>
                                </t>

                                <t t-if="record.descriptor.value">
                                    <span class="badge badge-success o_product_image_size" title="Descriptor is successfully created and the image is involved in face recognition.">
                                      已创建人脸数据描述符
                                    </span>
                                </t>
                                <t t-else="1">
                                    <span class="badge badge-danger o_product_image_size" title="Descriptor is missing, in order to create it, click on the button to make descriptors.">
                                      人脸数据描述符丢失
                                    </span>
                                </t>
                                <span t-attf-class="badge #{size_status} o_product_image_size" style="top:20px" t-esc="record.image.value" t-att-title="message"/>

                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
    </data>
</odoo>