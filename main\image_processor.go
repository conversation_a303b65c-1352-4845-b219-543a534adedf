package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/tencentyun/cos-go-sdk-v5"
)

// ImageProcessor 图片处理器
type ImageProcessor struct {
	config        *Config
	photoEnhancer *PhotoEnhancer
	cosClient     *cos.Client
}

// NewImageProcessor 创建图片处理器
func NewImageProcessor(config *Config) (*ImageProcessor, error) {
	// 验证COS配置
	fmt.Printf("🔍 验证COS配置...\n")
	fmt.Printf("   存储桶: %s\n", config.TencentBucket)
	fmt.Printf("   区域: %s\n", config.TencentRegion)
	fmt.Printf("   SecretID: %s...\n", config.TencentSecretID[:8]+"***")

	// 检查存储桶名称格式
	if !strings.Contains(config.TencentBucket, "-") {
		fmt.Printf("⚠️  存储桶名称格式可能不正确，应为: bucketname-appid\n")
	}

	// 检查区域格式
	validRegions := []string{"ap-beijing", "ap-shanghai", "ap-guangzhou", "ap-chengdu", "ap-chongqing", "ap-shenzhen-fsi", "ap-shanghai-fsi", "ap-beijing-fsi"}
	isValidRegion := false
	for _, region := range validRegions {
		if config.TencentRegion == region {
			isValidRegion = true
			break
		}
	}
	if !isValidRegion {
		fmt.Printf("⚠️  区域配置可能不正确，常见区域: %v\n", validRegions)
	}

	// 初始化COS客户端
	u, _ := url.Parse(fmt.Sprintf("https://%s.cos.%s.myqcloud.com", config.TencentBucket, config.TencentRegion))
	b := &cos.BaseURL{BucketURL: u}
	c := cos.NewClient(b, &http.Client{
		Timeout: 100 * time.Second,
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.TencentSecretID,
			SecretKey: config.TencentSecretKey,
		},
	})

	photoEnhancer := NewPhotoEnhancer(config, c)

	return &ImageProcessor{
		config:        config,
		photoEnhancer: photoEnhancer,
		cosClient:     c,
	}, nil
}

// ProcessImages 处理图片列表
func (p *ImageProcessor) ProcessImages(imagePaths []string, outputDir string) (*ProcessingStats, error) {
	startTime := time.Now()

	stats := &ProcessingStats{
		TotalImages:     len(imagePaths),
		ProcessedImages: 0,
		PortraitImages:  0,
		Results:         make([]ImageResult, 0, len(imagePaths)),
	}

	fmt.Printf("🔍 开始处理 %d 张图片\n", len(imagePaths))

	// 第一步：图片优化和人像检测
	fmt.Printf("👤 开始图片优化和人像检测...\n")
	var portraitImages []string

	for i, imgFileName := range imagePaths {
		fullImagePath := filepath.Join(outputDir, imgFileName)
		fmt.Printf("\n📸 处理图片 %d/%d: %s\n", i+1, len(imagePaths), imgFileName)

		result := ImageResult{
			OriginalPath: fullImagePath,
		}

		// 1. 图片大小优化和质量检查
		optimizedImageData, compressionInfo, err := p.compressImageIfNeeded(fullImagePath)
		if err != nil {
			fmt.Printf("⚠️  图片优化失败: %v\n", err)
			result.Error = err.Error()
			stats.Results = append(stats.Results, result)
			continue
		}

		// 记录压缩信息
		result.CompressionInfo = compressionInfo

		// 2. 质量检查和警告生成
		warnings := p.checkImageQuality(compressionInfo)
		result.QualityWarnings = warnings

		if len(warnings) > 0 {
			fmt.Printf("⚠️  图片质量警告:\n")
			for _, warning := range warnings {
				fmt.Printf("   - %s\n", warning)
			}
		}

		// 3. 如果图片质量过低，跳过人像检测
		if compressionInfo.EstimatedDPI < 72 {
			fmt.Printf("❌ 图片分辨率过低（%ddpi），跳过人像检测\n", compressionInfo.EstimatedDPI)
			result.Error = fmt.Sprintf("图片分辨率过低（%ddpi < 72dpi）", compressionInfo.EstimatedDPI)
			stats.Results = append(stats.Results, result)
			continue
		}

		// 4. 检测是否包含人像
		isPortrait, confidence, err := p.detectPortraitWithData(optimizedImageData)
		if err != nil {
			fmt.Printf("⚠️  人像检测失败: %v\n", err)
			result.Error = err.Error()
		} else if isPortrait {
			fmt.Printf("✅ 检测到人像 (置信度: %.2f)\n", confidence)
			result.IsPortrait = true
			result.Confidence = confidence
			portraitImages = append(portraitImages, fullImagePath)
			stats.PortraitImages++
		} else {
			fmt.Printf("❌ 未检测到人像\n")
			result.IsPortrait = false
		}

		stats.Results = append(stats.Results, result)
	}

	fmt.Printf("\n📊 人像检测完成:\n")
	fmt.Printf("   总图片数: %d\n", stats.TotalImages)
	fmt.Printf("   人像图片: %d\n", stats.PortraitImages)

	// 第二步：只对包含人像的图片进行抠图处理
	if len(portraitImages) > 0 {
		fmt.Printf("\n✂️  开始对 %d 张人像图片进行抠图处理...\n", len(portraitImages))

		for i, fullImagePath := range portraitImages {
			fmt.Printf("\n📸 抠图处理 %d/%d: %s\n", i+1, len(portraitImages), filepath.Base(fullImagePath))

			// 进行人像分割处理
			enhancementResult, err := p.photoEnhancer.EnhanceImage(fullImagePath, outputDir)
			if err != nil {
				fmt.Printf("❌ 人像分割失败: %v\n", err)
				// 更新对应的结果
				for j := range stats.Results {
					if stats.Results[j].OriginalPath == fullImagePath {
						stats.Results[j].Error = err.Error()
						break
					}
				}
			} else {
				fmt.Printf("✅ 人像分割完成\n")
				// 更新对应的结果
				for j := range stats.Results {
					if stats.Results[j].OriginalPath == fullImagePath {
						stats.Results[j].ProcessedPath = enhancementResult.EnhancedPath
						stats.Results[j].CutoutPath = enhancementResult.CutoutPath
						stats.Results[j].ProcessingTime = enhancementResult.ProcessingTime
						stats.ProcessedImages++
						break
					}
				}

				// 设置最佳头像（第一张处理成功的图片）
				if stats.BestPortrait == "" {
					if enhancementResult.CutoutPath != "" {
						stats.BestPortrait = p.convertToRelativePath(enhancementResult.CutoutPath)
						fmt.Printf("✅ 已设置抠图头像: %s\n", filepath.Base(enhancementResult.CutoutPath))
					} else {
						stats.BestPortrait = p.convertToRelativePath(enhancementResult.EnhancedPath)
						fmt.Printf("✅ 已设置头像: %s\n", filepath.Base(enhancementResult.EnhancedPath))
					}
				}
			}
		}
	} else {
		fmt.Printf("⚠️  没有检测到包含人像的图片，跳过抠图处理\n")
	}

	stats.ProcessingTime = time.Since(startTime).Milliseconds()

	fmt.Printf("\n📊 图片处理完成:\n")
	fmt.Printf("   总图片数: %d\n", stats.TotalImages)
	fmt.Printf("   人像图片: %d\n", stats.PortraitImages)
	fmt.Printf("   处理成功: %d\n", stats.ProcessedImages)
	fmt.Printf("   处理时间: %dms\n", stats.ProcessingTime)

	// 显示压缩和质量统计
	p.printProcessingStatistics(stats)

	if stats.BestPortrait != "" {
		fmt.Printf("   最佳头像: %s\n", filepath.Base(stats.BestPortrait))
	}

	return stats, nil
}

// detectPortrait 检测图片是否包含人像（带图片优化和质量检查）
func (p *ImageProcessor) detectPortrait(imagePath string) (bool, float64, error) {
	fmt.Printf("🔍 开始人像检测（含图片优化）: %s\n", filepath.Base(imagePath))

	// 1. 图片大小优化和质量检查
	optimizedImageData, compressionInfo, err := p.compressImageIfNeeded(imagePath)
	if err != nil {
		return false, 0, fmt.Errorf("图片优化失败: %v", err)
	}

	// 2. 质量检查和警告生成
	warnings := p.checkImageQuality(compressionInfo)
	if len(warnings) > 0 {
		fmt.Printf("⚠️  图片质量警告:\n")
		for _, warning := range warnings {
			fmt.Printf("   - %s\n", warning)
		}
	}

	// 3. 基本检查
	if len(optimizedImageData) < 10000 { // 小于10KB的图片可能不是人像
		fmt.Printf("❌ 图片太小（< 10KB），可能不是人像\n")
		return false, 0, nil
	}

	// 4. 检查文件扩展名
	ext := strings.ToLower(filepath.Ext(imagePath))
	if ext != ".jpg" && ext != ".jpeg" && ext != ".png" && ext != ".bmp" {
		fmt.Printf("❌ 不支持的图片格式: %s\n", ext)
		return false, 0, nil
	}

	// 5. 如果图片质量太低，提前返回失败并提示用户
	if compressionInfo.EstimatedDPI < 72 {
		fmt.Printf("❌ 图片分辨率过低（%ddpi），无法进行有效的人像识别\n", compressionInfo.EstimatedDPI)
		fmt.Printf("💡 建议：为获得最佳抠图效果，请确保图片分辨率在100dpi以上\n")
		return false, 0, fmt.Errorf("图片分辨率过低（%ddpi < 72dpi）", compressionInfo.EstimatedDPI)
	}

	// 6. 调用腾讯云数据万象人脸检测API（使用优化后的图片数据）
	return p.detectFaceWithCOSAPI(optimizedImageData)
}

// detectPortraitWithData 使用预处理的图片数据进行人像检测（内部函数）
func (p *ImageProcessor) detectPortraitWithData(imageData []byte) (bool, float64, error) {
	// 基本检查
	if len(imageData) < 10000 { // 小于10KB的图片可能不是人像
		return false, 0, nil
	}

	// 调用腾讯云数据万象人脸检测API
	return p.detectFaceWithCOSAPI(imageData)
}

// detectFaceWithCOSAPI 使用COS API检测人像 - 修复格式转换和API参数
func (p *ImageProcessor) detectFaceWithCOSAPI(imageData []byte) (bool, float64, error) {
	fmt.Printf("🔍 开始人像检测...\n")

	// 检测原始图片格式
	_, originalFormat, err := image.DecodeConfig(bytes.NewReader(imageData))
	if err != nil {
		fmt.Printf("❌ 无法检测图片格式: %v\n", err)
		return false, 0, err
	}

	// 使用原始格式的扩展名，避免不必要的格式转换
	var tempFileName string
	switch strings.ToLower(originalFormat) {
	case "png":
		tempFileName = fmt.Sprintf("temp_%d.png", time.Now().Unix())
	case "jpeg", "jpg":
		tempFileName = fmt.Sprintf("temp_%d.jpg", time.Now().Unix())
	default:
		// 对于其他格式，保留原有逻辑转换为JPEG
		tempFileName = fmt.Sprintf("temp_%d.jpg", time.Now().Unix())
	}

	// 输出基本信息用于调试
	fmt.Printf("📄 临时文件: %s, 大小: %.2fMB, 原始格式: %s\n",
		tempFileName, float64(len(imageData))/(1024*1024), originalFormat)

	// 尝试上传到COS
	_, err = p.uploadToCOS(imageData, tempFileName)
	if err != nil {
		fmt.Printf("⚠️  COS上传失败: %v\n", err)
		fmt.Printf("💡 可能的原因：\n")
		fmt.Printf("   1. 存储桶不存在或名称错误\n")
		fmt.Printf("   2. 区域配置错误\n")
		fmt.Printf("   3. 权限不足\n")
		fmt.Printf("   4. 网络连接问题\n")
		fmt.Printf("🔧 使用简单检测逻辑作为备用方案\n")

		// 如果COS上传失败，使用简单的人像检测逻辑
		return p.detectFaceWithSimpleLogic(imageData)
	}

	// 调用COS人脸检测API（使用优化的参数）
	return p.callCOSFaceDetectionAPI(tempFileName)
}

// uploadToCOS 上传图片到腾讯云COS - 修复版本
func (p *ImageProcessor) uploadToCOS(imageData []byte, fileName string) (string, error) {
	name := fileName
	// 使用 SDK 上传图片
	_, err := p.cosClient.Object.Put(context.Background(), name, bytes.NewReader(imageData), nil)
	if err != nil {
		return "", fmt.Errorf("COS上传失败: %v", err)
	}

	fmt.Printf("✅ 图片已上传到COS: %s\n", fileName)
	return fileName, nil
}

// callCOSFaceDetectionAPI 调用COS人脸检测API - 优化检测参数
func (p *ImageProcessor) callCOSFaceDetectionAPI(fileName string) (bool, float64, error) {
	fmt.Printf("🔍 调用COS人脸检测API...\n")

	// 使用优化的检测参数，提高检测敏感度
	opt := &cos.DetectFaceOptions{
		MaxFaceNum: 5, // 最多检测5个人脸
	}

	resp, _, err := p.cosClient.CI.DetectFace(context.Background(), fileName, opt)
	if err != nil {
		fmt.Printf("❌ 人脸检测API调用失败: %v\n", err)
		return false, 0, fmt.Errorf("调用人脸检测API失败: %v", err)
	}

	// 详细的响应解析和调试信息
	if resp != nil {
		fmt.Printf("🔍 API响应详情: 检测到 %d 个人脸区域\n", len(resp.FaceInfos))

		if len(resp.FaceInfos) > 0 {
			// 输出检测到的人脸信息（用于调试）
			for i, faceInfo := range resp.FaceInfos {
				fmt.Printf("   人脸 %d: X=%d Y=%d Width=%d Height=%d\n",
					i+1, faceInfo.X, faceInfo.Y, faceInfo.Width, faceInfo.Height)
			}

			// 计算置信度（基于检测到的人脸数量和位置）
			confidence := float64(len(resp.FaceInfos)) * 0.8 // 基础置信度
			if confidence > 1.0 {
				confidence = 1.0
			}

			fmt.Printf("✅ 检测到 %d 个人脸 (置信度: %.2f)\n", len(resp.FaceInfos), confidence)
			return true, confidence, nil
		}
	}

	fmt.Printf("❌ 未检测到人脸\n")
	return false, 0, nil
}

// SaveProcessingResults 保存处理结果
func (p *ImageProcessor) SaveProcessingResults(stats *ProcessingStats, outputDir string) error {
	// 保存处理结果到JSON文件
	resultPath := filepath.Join(outputDir, "image_processing_results.json")

	// 创建一个副本并转换所有路径为相对路径
	statsWithRelativePaths := *stats // 浅拷贝

	// 转换 BestPortrait 路径为相对路径
	if statsWithRelativePaths.BestPortrait != "" {
		statsWithRelativePaths.BestPortrait = p.convertToRelativePath(statsWithRelativePaths.BestPortrait)
		fmt.Printf("DEBUG: BestPortrait 转换后: %s\n", statsWithRelativePaths.BestPortrait)
	}

	// 转换每个 ImageResult 中的路径
	resultsWithRelativePaths := make([]ImageResult, len(stats.Results))
	for i, result := range stats.Results {
		resultsWithRelativePaths[i] = result // 拷贝结构体

		// 转换所有路径字段为相对路径
		resultsWithRelativePaths[i].OriginalPath = p.convertToRelativePath(result.OriginalPath)
		if result.ProcessedPath != "" {
			resultsWithRelativePaths[i].ProcessedPath = p.convertToRelativePath(result.ProcessedPath)
		}
		if result.CutoutPath != "" {
			resultsWithRelativePaths[i].CutoutPath = p.convertToRelativePath(result.CutoutPath)
		}
		if result.StandardPhotoPath != "" {
			resultsWithRelativePaths[i].StandardPhotoPath = p.convertToRelativePath(result.StandardPhotoPath)
		}
	}

	// 更新副本的 Results
	statsWithRelativePaths.Results = resultsWithRelativePaths

	resultData := map[string]interface{}{
		"processing_time":    statsWithRelativePaths.ProcessingTime,
		"total_images":       statsWithRelativePaths.TotalImages,
		"processed_images":   statsWithRelativePaths.ProcessedImages,
		"portrait_images":    statsWithRelativePaths.PortraitImages,
		"best_portrait":      statsWithRelativePaths.BestPortrait,
		"enhancement_config": p.config.PhotoEnhancement,
		"results":            statsWithRelativePaths.Results,
	}

	jsonData, err := json.MarshalIndent(resultData, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化处理结果失败: %v", err)
	}

	if err := os.WriteFile(resultPath, jsonData, 0644); err != nil {
		return fmt.Errorf("保存处理结果失败: %v", err)
	}

	fmt.Printf("💾 处理结果已保存到: %s\n", resultPath)
	return nil
}

// GenerateEnhancementReport 生成增强报告
func (p *ImageProcessor) GenerateEnhancementReport(results []*ImageEnhancementResult, outputPath string) error {
	report := map[string]interface{}{
		"enhancement_config": p.config.PhotoEnhancement,
		"total_images":       len(results),
		"processed_time":     time.Now().Format("2006-01-02 15:04:05"),
		"results":            results,
	}

	// 计算统计信息
	var totalTime int64
	var avgQuality float64
	skewCorrected := 0
	faceCentered := 0

	for _, result := range results {
		totalTime += result.ProcessingTime
		avgQuality += result.QualityScore
		if result.SkewCorrection {
			skewCorrected++
		}
		if result.FaceCentered {
			faceCentered++
		}
	}

	if len(results) > 0 {
		avgQuality /= float64(len(results))
	}

	report["statistics"] = map[string]interface{}{
		"total_processing_time":  totalTime,
		"average_quality":        avgQuality,
		"skew_corrected":         skewCorrected,
		"face_centered":          faceCentered,
		"average_time_per_image": totalTime / int64(len(results)),
	}

	// 保存报告
	reportData, err := json.MarshalIndent(report, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(outputPath, reportData, 0644)
}

// CleanXMLContent 清理XML内容
func CleanXMLContent(content string) string {
	// 移除常见的XML标签
	content = strings.ReplaceAll(content, "<br>", "\n")
	content = strings.ReplaceAll(content, "<br/>", "\n")
	content = strings.ReplaceAll(content, "<br />", "\n")
	content = strings.ReplaceAll(content, "<p>", "")
	content = strings.ReplaceAll(content, "</p>", "\n")
	content = strings.ReplaceAll(content, "<div>", "")
	content = strings.ReplaceAll(content, "</div>", "\n")
	content = strings.ReplaceAll(content, "<span>", "")
	content = strings.ReplaceAll(content, "</span>", "")

	// 移除其他可能的HTML标签
	// 这里可以添加更多的标签清理逻辑

	// 清理多余的空白字符
	content = strings.TrimSpace(content)

	return content
}

// detectFaceWithSimpleLogic 使用简单逻辑检测人像 - 优化版本
func (p *ImageProcessor) detectFaceWithSimpleLogic(imageData []byte) (bool, float64, error) {
	// 简单的图片分析逻辑
	// 1. 检查图片大小（人像通常较大）
	// 2. 检查图片比例（人像通常是竖向）
	// 3. 检查图片内容特征

	// 解码图片获取尺寸信息
	img, _, err := image.Decode(bytes.NewReader(imageData))
	if err != nil {
		return false, 0, fmt.Errorf("图片解码失败: %v", err)
	}

	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// 计算图片特征
	aspectRatio := float64(height) / float64(width)
	imageSize := len(imageData)

	fmt.Printf("📏 图片分析: %dx%d, 大小: %d bytes, 比例: %.2f\n", width, height, imageSize, aspectRatio)

	// 人像特征判断 - 优化后的逻辑
	var confidence float64 = 0.0
	var isPortrait bool = false

	// 1. 比例判断（人像通常是竖向，比例大于1.1）
	if aspectRatio > 1.1 {
		confidence += 0.4
		fmt.Printf("   ✅ 竖向比例: +0.4\n")
	} else {
		fmt.Printf("   ❌ 横向比例: +0.0\n")
	}

	// 2. 分辨率判断（人像通常有足够的分辨率）
	if width > 200 && height > 250 {
		confidence += 0.3
		fmt.Printf("   ✅ 足够分辨率: +0.3\n")
	} else {
		fmt.Printf("   ❌ 分辨率不足: +0.0\n")
	}

	// 3. 文件大小合理性判断（人像通常20KB-1MB）
	if imageSize > 20000 && imageSize < 1000000 {
		confidence += 0.2
		fmt.Printf("   ✅ 合理文件大小: +0.2\n")
	} else {
		fmt.Printf("   ❌ 文件大小异常: +0.0\n")
	}

	// 4. 宽高合理性判断（人像不会太宽或太窄）
	if width > 100 && height > 100 && aspectRatio < 3.0 {
		confidence += 0.1
		fmt.Printf("   ✅ 宽高合理: +0.1\n")
	} else {
		fmt.Printf("   ❌ 宽高异常: +0.0\n")
	}

	// 综合判断 - 提高阈值
	if confidence >= 0.7 {
		isPortrait = true
	}

	fmt.Printf("🔍 简单检测结果: 人像=%v, 置信度=%.2f\n", isPortrait, confidence)
	return isPortrait, confidence, nil
}

// compressImageIfNeeded 如果图片大小超过限制则进行压缩
func (p *ImageProcessor) compressImageIfNeeded(imagePath string) ([]byte, *ImageCompressionInfo, error) {
	// 读取原始图片数据
	imageData, err := os.ReadFile(imagePath)
	if err != nil {
		return nil, nil, fmt.Errorf("读取图片失败: %v", err)
	}

	// 计算原始大小（MB）
	originalSizeMB := float64(len(imageData)) / (1024 * 1024)

	// 获取图片尺寸信息
	img, format, err := image.Decode(bytes.NewReader(imageData))
	if err != nil {
		return nil, nil, fmt.Errorf("图片解码失败: %v", err)
	}

	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// 估算DPI（基于图片尺寸，假设常见的显示尺寸）
	estimatedDPI := p.estimateDPI(width, height, originalSizeMB)

	compressionInfo := &ImageCompressionInfo{
		OriginalSizeMB:  originalSizeMB,
		EstimatedDPI:    estimatedDPI,
		ImageDimensions: Size{Width: width, Height: height},
		WasCompressed:   false,
	}

	// 检查是否需要压缩（文件大小和尺寸）
	maxSizeMB := p.config.PhotoEnhancement.MaxImageSizeForAPI
	maxDimension := 4096 // 腾讯云API的最大尺寸限制

	needsCompression := false
	var compressionReason string

	if p.config.PhotoEnhancement.EnableSizeCheck && originalSizeMB > maxSizeMB {
		needsCompression = true
		compressionReason += fmt.Sprintf("文件大小%.2fMB > %.2fMB", originalSizeMB, maxSizeMB)
	}

	if width > maxDimension || height > maxDimension {
		needsCompression = true
		if compressionReason != "" {
			compressionReason += "，"
		}
		compressionReason += fmt.Sprintf("图片尺寸%dx%d > %dx%d", width, height, maxDimension, maxDimension)
	}

	if !needsCompression {
		fmt.Printf("📏 图片检查通过: 大小%.2fMB <= %.2fMB，尺寸%dx%d <= %dx%d\n",
			originalSizeMB, maxSizeMB, width, height, maxDimension, maxDimension)
		compressionInfo.CompressedSizeMB = originalSizeMB
		compressionInfo.CompressionRatio = 1.0
		return imageData, compressionInfo, nil
	}

	fmt.Printf("📦 图片需要压缩: %s\n", compressionReason)

	// 进行图片压缩（文件大小和尺寸）
	compressedData, err := p.compressImageWithDimensions(img, format, maxSizeMB, maxDimension)
	if err != nil {
		return nil, nil, fmt.Errorf("图片压缩失败: %v", err)
	}

	compressedSizeMB := float64(len(compressedData)) / (1024 * 1024)
	compressionRatio := compressedSizeMB / originalSizeMB

	compressionInfo.CompressedSizeMB = compressedSizeMB
	compressionInfo.CompressionRatio = compressionRatio
	compressionInfo.WasCompressed = true

	fmt.Printf("✅ 图片压缩完成: %.2fMB -> %.2fMB (压缩比: %.1f%%)\n",
		originalSizeMB, compressedSizeMB, compressionRatio*100)

	return compressedData, compressionInfo, nil
}

// compressImageWithDimensions 智能压缩图片（质量优先）
func (p *ImageProcessor) compressImageWithDimensions(img image.Image, format string, targetSizeMB float64, maxDimension int) ([]byte, error) {
	bounds := img.Bounds()
	originalWidth := bounds.Dx()
	originalHeight := bounds.Dy()

	fmt.Printf("🎯 开始智能压缩 (质量优先模式)\n")
	fmt.Printf("   原始尺寸: %dx%d\n", originalWidth, originalHeight)

	// 第一步：智能尺寸缩放（如果图片过大）
	smartResizeThreshold := p.config.PhotoEnhancement.SmartResizeThresholdWidth
	smartResizeTarget := p.config.PhotoEnhancement.SmartResizeTargetWidth

	processedImg := img
	var compressionSteps []string

	if originalWidth > smartResizeThreshold {
		// 计算缩放比例（基于宽度）
		scale := float64(smartResizeTarget) / float64(originalWidth)
		newWidth := smartResizeTarget
		newHeight := int(float64(originalHeight) * scale)

		fmt.Printf("🔧 智能缩放: %dx%d -> %dx%d (目标宽度: %dpx)\n",
			originalWidth, originalHeight, newWidth, newHeight, smartResizeTarget)

		processedImg = p.resizeImageForCompression(processedImg, scale)
		compressionSteps = append(compressionSteps, fmt.Sprintf("尺寸缩放 %dx%d->%dx%d", originalWidth, originalHeight, newWidth, newHeight))
	}

	// 第二步：生成当前图片数据，检查文件大小
	currentData, err := p.encodeImage(processedImg, format, p.config.PhotoEnhancement.CompressionQuality)
	if err != nil {
		return nil, fmt.Errorf("编码图片失败: %v", err)
	}

	currentSizeMB := float64(len(currentData)) / (1024 * 1024)
	fmt.Printf("📊 缩放后大小: %.2fMB\n", currentSizeMB)

	// 第三步：如果文件大小仍然超标，进行质量压缩
	if currentSizeMB > targetSizeMB {
		fmt.Printf("📦 需要进一步质量压缩: %.2fMB > %.2fMB\n", currentSizeMB, targetSizeMB)

		qualityCompressedData, err := p.compressImageByQuality(processedImg, format, targetSizeMB)
		if err != nil {
			return nil, fmt.Errorf("质量压缩失败: %v", err)
		}

		qualityCompressedSizeMB := float64(len(qualityCompressedData)) / (1024 * 1024)
		compressionSteps = append(compressionSteps, fmt.Sprintf("质量压缩 %.2fMB->%.2fMB", currentSizeMB, qualityCompressedSizeMB))
		currentData = qualityCompressedData
		currentSizeMB = qualityCompressedSizeMB
	}

	// 第四步：最终安全检查（API尺寸限制）
	bounds = processedImg.Bounds()
	finalWidth := bounds.Dx()
	finalHeight := bounds.Dy()

	if finalWidth > maxDimension || finalHeight > maxDimension {
		fmt.Printf("⚠️  最终安全检查: 图片尺寸 %dx%d 超过API限制 %d，进行最终缩放\n", finalWidth, finalHeight, maxDimension)

		scaleX := float64(maxDimension) / float64(finalWidth)
		scaleY := float64(maxDimension) / float64(finalHeight)
		scale := scaleX
		if scaleY < scaleX {
			scale = scaleY
		}

		processedImg = p.resizeImageForCompression(processedImg, scale)
		currentData, err = p.encodeImage(processedImg, format, p.config.PhotoEnhancement.CompressionQuality)
		if err != nil {
			return nil, fmt.Errorf("最终编码失败: %v", err)
		}

		currentSizeMB = float64(len(currentData)) / (1024 * 1024)
		compressionSteps = append(compressionSteps, "API安全缩放")
	}

	fmt.Printf("✅ 智能压缩完成: %.2fMB\n", currentSizeMB)
	if len(compressionSteps) > 0 {
		fmt.Printf("   压缩步骤: %s\n", strings.Join(compressionSteps, " → "))
	}

	return currentData, nil
}

// encodeImage 将图片编码为指定格式的字节数组
func (p *ImageProcessor) encodeImage(img image.Image, format string, quality int) ([]byte, error) {
	var buf bytes.Buffer

	switch strings.ToLower(format) {
	case "jpeg", "jpg":
		err := jpeg.Encode(&buf, img, &jpeg.Options{Quality: quality})
		if err != nil {
			return nil, err
		}
	case "png":
		err := png.Encode(&buf, img)
		if err != nil {
			return nil, err
		}
	default:
		// 默认使用JPEG编码
		err := jpeg.Encode(&buf, img, &jpeg.Options{Quality: quality})
		if err != nil {
			return nil, err
		}
	}

	return buf.Bytes(), nil
}

// compressImageByQuality 通过调整质量参数压缩图片
func (p *ImageProcessor) compressImageByQuality(img image.Image, format string, targetSizeMB float64) ([]byte, error) {
	baseQuality := p.config.PhotoEnhancement.CompressionQuality
	if p.config.PhotoEnhancement.QualityPriority {
		baseQuality = 90 // 质量优先模式使用更高的起始质量
	}

	maxAttempts := 6
	quality := baseQuality
	minQuality := 40 // 质量优先模式的最低质量更高

	if p.config.PhotoEnhancement.QualityPriority {
		minQuality = 60 // 质量优先时最低质量为60
	}

	for attempt := 0; attempt < maxAttempts; attempt++ {
		data, err := p.encodeImage(img, format, quality)
		if err != nil {
			return nil, err
		}

		sizeMB := float64(len(data)) / (1024 * 1024)

		fmt.Printf("   🔄 质量压缩尝试 %d/%d: 质量=%d, 大小=%.2fMB\n", attempt+1, maxAttempts, quality, sizeMB)

		if sizeMB <= targetSizeMB {
			fmt.Printf("   ✅ 质量压缩成功: 质量=%d, 大小=%.2fMB\n", quality, sizeMB)
			return data, nil
		}

		// 如果质量已经很低了，停止尝试
		if quality <= minQuality {
			fmt.Printf("   ⚠️  已达到最低质量限制(%d)，停止压缩\n", minQuality)
			return data, nil
		}

		// 降低质量，质量优先模式降幅更小
		if p.config.PhotoEnhancement.QualityPriority {
			quality = int(float64(quality) * 0.85) // 质量优先：每次降低15%
		} else {
			quality = int(float64(quality) * 0.75) // 标准模式：每次降低25%
		}

		if quality < minQuality {
			quality = minQuality
		}
	}

	// 如果最后还是太大，返回最低质量的版本
	data, err := p.encodeImage(img, format, minQuality)
	if err != nil {
		return nil, err
	}

	sizeMB := float64(len(data)) / (1024 * 1024)
	fmt.Printf("   ⚠️  最终压缩结果: 质量=%d, 大小=%.2fMB (可能仍超过目标大小)\n", minQuality, sizeMB)

	return data, nil
}

// resizeImageForCompression 为压缩目的调整图片尺寸
func (p *ImageProcessor) resizeImageForCompression(img image.Image, scale float64) image.Image {
	bounds := img.Bounds()
	width := int(float64(bounds.Dx()) * scale)
	height := int(float64(bounds.Dy()) * scale)

	resized := image.NewRGBA(image.Rect(0, 0, width, height))

	// 简单的最近邻缩放
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			srcX := int(float64(x) / scale)
			srcY := int(float64(y) / scale)

			if srcX < bounds.Dx() && srcY < bounds.Dy() {
				resized.Set(x, y, img.At(srcX, srcY))
			}
		}
	}

	return resized
}

// estimateDPI 估算图片的DPI
func (p *ImageProcessor) estimateDPI(width, height int, sizeMB float64) int {
	// 基于图片尺寸和文件大小的简单DPI估算
	totalPixels := width * height

	// 假设标准的打印尺寸比例
	var estimatedDPI int

	if totalPixels > 4000000 { // 超过4MP
		estimatedDPI = 300
	} else if totalPixels > 2000000 { // 2-4MP
		estimatedDPI = 200
	} else if totalPixels > 1000000 { // 1-2MP
		estimatedDPI = 150
	} else if totalPixels > 500000 { // 0.5-1MP
		estimatedDPI = 100
	} else {
		estimatedDPI = 72 // 低分辨率
	}

	// 根据文件大小调整估算
	if sizeMB > 5 {
		estimatedDPI += 50
	} else if sizeMB < 0.5 {
		estimatedDPI -= 50
	}

	if estimatedDPI < 72 {
		estimatedDPI = 72
	}

	return estimatedDPI
}

// checkImageQuality 检查图片质量并生成警告信息
func (p *ImageProcessor) checkImageQuality(compressionInfo *ImageCompressionInfo) []string {
	var warnings []string

	if !p.config.PhotoEnhancement.EnableDPICheck {
		return warnings
	}

	minDPI := p.config.PhotoEnhancement.MinDPI

	// 检查DPI
	if compressionInfo.EstimatedDPI < minDPI {
		warnings = append(warnings, fmt.Sprintf(
			"图片分辨率偏低（估算%ddpi < 建议%ddpi），可能影响抠图质量。建议使用分辨率更高的图片。",
			compressionInfo.EstimatedDPI, minDPI))
	}

	// 检查图片尺寸
	if compressionInfo.ImageDimensions.Width < 300 || compressionInfo.ImageDimensions.Height < 400 {
		warnings = append(warnings, fmt.Sprintf(
			"图片尺寸较小（%dx%d），可能影响人像识别效果。建议使用尺寸更大的图片。",
			compressionInfo.ImageDimensions.Width, compressionInfo.ImageDimensions.Height))
	}

	// 检查压缩情况
	if compressionInfo.WasCompressed && compressionInfo.CompressionRatio < 0.5 {
		warnings = append(warnings, fmt.Sprintf(
			"图片已被大幅压缩（压缩比%.1f%%），可能影响处理质量。",
			compressionInfo.CompressionRatio*100))
	}

	return warnings
}

// printProcessingStatistics 打印处理统计信息
func (p *ImageProcessor) printProcessingStatistics(stats *ProcessingStats) {
	var compressedCount int
	var totalOriginalSize, totalCompressedSize float64
	var lowDPICount int
	var warningCount int

	for _, result := range stats.Results {
		if result.CompressionInfo != nil {
			totalOriginalSize += result.CompressionInfo.OriginalSizeMB
			totalCompressedSize += result.CompressionInfo.CompressedSizeMB

			if result.CompressionInfo.WasCompressed {
				compressedCount++
			}

			if result.CompressionInfo.EstimatedDPI < p.config.PhotoEnhancement.MinDPI {
				lowDPICount++
			}
		}

		if len(result.QualityWarnings) > 0 {
			warningCount++
		}
	}

	fmt.Printf("\n📈 图片优化统计:\n")
	fmt.Printf("   压缩图片数: %d/%d\n", compressedCount, stats.TotalImages)

	if totalOriginalSize > 0 {
		fmt.Printf("   总原始大小: %.2fMB\n", totalOriginalSize)
		fmt.Printf("   总压缩后大小: %.2fMB\n", totalCompressedSize)

		if compressedCount > 0 {
			avgCompressionRatio := totalCompressedSize / totalOriginalSize
			spaceSaved := totalOriginalSize - totalCompressedSize
			fmt.Printf("   平均压缩比: %.1f%%\n", avgCompressionRatio*100)
			fmt.Printf("   节省空间: %.2fMB\n", spaceSaved)
		}
	}

	fmt.Printf("   低DPI图片: %d/%d\n", lowDPICount, stats.TotalImages)
	fmt.Printf("   质量警告: %d/%d\n", warningCount, stats.TotalImages)

	if lowDPICount > 0 {
		fmt.Printf("💡 建议：%d张图片分辨率偏低，可能影响抠图效果\n", lowDPICount)
	}
}

// convertToRelativePath 将绝对路径转换为相对于项目根目录的路径
func (p *ImageProcessor) convertToRelativePath(absolutePath string) string {
	// 获取项目根目录（输出目录的父目录）
	cwd, err := os.Getwd()
	if err != nil {
		// 如果获取工作目录失败，直接返回文件名
		return filepath.Base(absolutePath)
	}

	// 尝试获取相对路径
	relPath, err := filepath.Rel(cwd, absolutePath)
	if err != nil {
		// 如果转换失败，返回文件名
		return filepath.Base(absolutePath)
	}

	return relPath
}
