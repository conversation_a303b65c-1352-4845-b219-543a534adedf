{"globals": {}, "rules": {"@typescript-eslint/no-require-imports": "off"}, "overrides": [{"files": ["**/*.ts"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "project": ["./tsconfig.json"]}, "plugins": ["@typescript-eslint"], "env": {"browser": true, "commonjs": false, "node": false, "es2021": true}, "extends": ["airbnb-base", "eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking", "plugin:@typescript-eslint/strict", "plugin:import/recommended", "plugin:promise/recommended"], "rules": {"@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/dot-notation": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-inferrable-types": "off", "@typescript-eslint/no-misused-promises": "off", "@typescript-eslint/no-unnecessary-condition": "off", "@typescript-eslint/no-unsafe-argument": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/no-unsafe-call": "off", "@typescript-eslint/no-unsafe-member-access": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/no-require-imports": "off", "@typescript-eslint/no-empty-object-type": "off", "@typescript-eslint/non-nullable-type-assertion-style": "off", "@typescript-eslint/prefer-for-of": "off", "@typescript-eslint/prefer-nullish-coalescing": "off", "@typescript-eslint/prefer-ts-expect-error": "off", "@typescript-eslint/restrict-plus-operands": "off", "@typescript-eslint/restrict-template-expressions": "off", "dot-notation": "off", "guard-for-in": "off", "import/extensions": ["off", "always"], "import/no-unresolved": "off", "import/prefer-default-export": "off", "lines-between-class-members": "off", "max-len": [1, 275, 3], "no-async-promise-executor": "off", "no-await-in-loop": "off", "no-bitwise": "off", "no-continue": "off", "no-lonely-if": "off", "no-mixed-operators": "off", "no-param-reassign": "off", "no-plusplus": "off", "no-regex-spaces": "off", "no-restricted-syntax": "off", "no-return-assign": "off", "no-void": "off", "object-curly-newline": "off", "prefer-destructuring": "off", "prefer-template": "off", "radix": "off"}}, {"files": ["**/*.d.ts"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "project": ["./tsconfig.json"]}, "plugins": ["@typescript-eslint"], "env": {"browser": true, "commonjs": false, "node": false, "es2021": true}, "extends": ["airbnb-base", "eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking", "plugin:@typescript-eslint/strict", "plugin:import/recommended", "plugin:promise/recommended"], "rules": {"@typescript-eslint/array-type": "off", "@typescript-eslint/ban-types": "off", "@typescript-eslint/consistent-indexed-object-style": "off", "@typescript-eslint/consistent-type-definitions": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/no-unnecessary-type-arguments": "off", "@typescript-eslint/no-unnecessary-type-constraint": "off", "comma-dangle": "off", "indent": "off", "lines-between-class-members": "off", "max-classes-per-file": "off", "max-len": "off", "no-multiple-empty-lines": "off", "no-shadow": "off", "no-use-before-define": "off", "quotes": "off", "semi": "off"}}, {"files": ["**/*.js"], "parserOptions": {"sourceType": "module", "ecmaVersion": "latest"}, "plugins": [], "env": {"browser": true, "commonjs": true, "node": true, "es2021": true}, "extends": ["airbnb-base", "eslint:recommended", "plugin:node/recommended", "plugin:promise/recommended"], "rules": {"dot-notation": "off", "import/extensions": ["error", "always"], "import/no-extraneous-dependencies": "off", "max-len": [1, 275, 3], "no-await-in-loop": "off", "no-bitwise": "off", "no-continue": "off", "no-mixed-operators": "off", "no-param-reassign": "off", "no-plusplus": "off", "no-regex-spaces": "off", "no-restricted-syntax": "off", "no-return-assign": "off", "node/no-unsupported-features/es-syntax": "off", "object-curly-newline": "off", "prefer-destructuring": "off", "prefer-template": "off", "radix": "off"}}, {"files": ["**/*.json"], "parserOptions": {"ecmaVersion": "latest"}, "plugins": ["json"], "env": {"browser": false, "commonjs": false, "node": false, "es2021": false}, "extends": []}, {"files": ["**/*.html"], "parserOptions": {"sourceType": "module", "ecmaVersion": "latest"}, "parser": "@html-eslint/parser", "plugins": ["html", "@html-eslint"], "env": {"browser": true, "commonjs": false, "node": false, "es2021": false}, "extends": ["plugin:@html-eslint/recommended"], "rules": {"@html-eslint/element-newline": "off", "@html-eslint/attrs-newline": "off", "@html-eslint/indent": ["error", 2]}}, {"files": ["**/*.md"], "plugins": ["markdown"], "processor": "markdown/markdown", "rules": {"no-undef": "off"}}, {"files": ["**/*.md/*.js"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/triple-slash-reference": "off", "import/newline-after-import": "off", "import/no-unresolved": "off", "no-console": "off", "no-global-assign": "off", "no-multi-spaces": "off", "no-restricted-globals": "off", "no-undef": "off", "no-unused-vars": "off", "node/no-missing-import": "off", "node/no-missing-require": "off", "promise/catch-or-return": "off"}}], "ignorePatterns": ["node_modules", "assets", "dist", "demo/helpers/*.js", "demo/typescript/*.js", "demo/faceid/*.js", "demo/tracker/*.js", "typedoc"]}