services:
  ppt-extractor:
    build: .
    container_name: ppt-extractor
    ports:
      - "8088:8088"
    volumes:
      # 挂载输出目录，方便查看提取结果
      - ./output:/app/output
      - ./templates-ppt/:/templates-ppt/
    env_file:
      - .env
    environment:
      - GIN_MODE=release
      - DEBUG=true  # 启用debug模式
      - DOCKER_CONTAINER=true  # 标识Docker环境
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8088/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  default:
    name: ppt-extractor-network 