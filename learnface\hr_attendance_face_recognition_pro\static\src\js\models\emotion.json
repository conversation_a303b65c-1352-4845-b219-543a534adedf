{"format": "graph-model", "generatedBy": "https://github.com/oarriaga/face_classification", "convertedBy": "https://github.com/vladmandic", "userDefinedMetadata": {"signature": {"inputs": {"input_1:0": {"name": "input_1:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "64"}, {"size": "64"}, {"size": "1"}]}}}, "outputs": {"Identity:0": {"name": "Identity:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "7"}]}}}}}, "modelTopology": {"node": [{"name": "unknown_26", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_32", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "256"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_9", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}}}, {"name": "unknown_15", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "1"}]}}}}}, {"name": "unknown_43", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "256"}, {"size": "7"}]}}}}}, {"name": "unknown_44", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "7"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/global_average_pooling2d_1/Mean/reduction_indices", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "input_1", "op": "Placeholder", "attr": {"shape": {"shape": {"dim": [{"size": "-1"}, {"size": "64"}, {"size": "64"}, {"size": "1"}]}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/conv2d_1/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "1"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/conv2d_1/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/conv2d_2/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/separable_conv2d_4/separable_conv2d_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "256"}, {"size": "256"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/conv2d_2/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/conv2d_3/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/separable_conv2d_4/separable_conv2d_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/conv2d_3/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/separable_conv2d_1/separable_conv2d_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/separable_conv2d_1/separable_conv2d_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/separable_conv2d_2/separable_conv2d_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/separable_conv2d_2/separable_conv2d_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/conv2d_4/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/conv2d_4/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/separable_conv2d_3/separable_conv2d_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/separable_conv2d_3/separable_conv2d_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/block1_conv1_act/Relu", "op": "_FusedConv2D", "input": ["input_1", "StatefulPartitionedCall/model_1/conv2d_1/Conv2D_weights", "StatefulPartitionedCall/model_1/conv2d_1/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "padding": {"s": "VkFMSUQ="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0}}}, {"name": "StatefulPartitionedCall/model_1/block1_conv2_act/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model_1/block1_conv1_act/Relu", "StatefulPartitionedCall/model_1/conv2d_2/Conv2D_weights", "StatefulPartitionedCall/model_1/conv2d_2/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/model_1/separable_conv2d_1/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model_1/block1_conv2_act/Relu", "unknown_9"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/model_1/batch_normalization_1/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model_1/block1_conv2_act/Relu", "StatefulPartitionedCall/model_1/conv2d_3/Conv2D_weights", "StatefulPartitionedCall/model_1/conv2d_3/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/block2_sepconv2_act/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model_1/separable_conv2d_1/separable_conv2d/depthwise", "StatefulPartitionedCall/model_1/separable_conv2d_1/separable_conv2d_weights", "StatefulPartitionedCall/model_1/separable_conv2d_1/separable_conv2d_bn_offset"], "device": "/device:CPU:0", "attr": {"padding": {"s": "VkFMSUQ="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/model_1/separable_conv2d_2/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model_1/block2_sepconv2_act/Relu", "unknown_15"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/model_1/block2_sepconv2_bn/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model_1/separable_conv2d_2/separable_conv2d/depthwise", "StatefulPartitionedCall/model_1/separable_conv2d_2/separable_conv2d_weights", "StatefulPartitionedCall/model_1/separable_conv2d_2/separable_conv2d_bn_offset"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model_1/max_pooling2d_1/MaxPool", "op": "MaxPool", "input": ["StatefulPartitionedCall/model_1/block2_sepconv2_bn/FusedBatchNormV3"], "attr": {"strides": {"list": {"i": ["1", "2", "2", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "ksize": {"list": {"i": ["1", "3", "3", "1"]}}}}, {"name": "StatefulPartitionedCall/model_1/add_1/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model_1/max_pooling2d_1/MaxPool", "StatefulPartitionedCall/model_1/batch_normalization_1/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/block3_sepconv1_act/Relu", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model_1/add_1/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/batch_normalization_2/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model_1/add_1/add", "StatefulPartitionedCall/model_1/conv2d_4/Conv2D_weights", "StatefulPartitionedCall/model_1/conv2d_4/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/model_1/separable_conv2d_3/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model_1/block3_sepconv1_act/Relu", "unknown_26"], "attr": {"padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model_1/block3_sepconv2_act/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model_1/separable_conv2d_3/separable_conv2d/depthwise", "StatefulPartitionedCall/model_1/separable_conv2d_3/separable_conv2d_weights", "StatefulPartitionedCall/model_1/separable_conv2d_3/separable_conv2d_bn_offset"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0}}}, {"name": "StatefulPartitionedCall/model_1/separable_conv2d_4/separable_conv2d/depthwise", "op": "DepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model_1/block3_sepconv2_act/Relu", "unknown_32"], "attr": {"explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/model_1/block3_sepconv2_bn/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model_1/separable_conv2d_4/separable_conv2d/depthwise", "StatefulPartitionedCall/model_1/separable_conv2d_4/separable_conv2d_weights", "StatefulPartitionedCall/model_1/separable_conv2d_4/separable_conv2d_bn_offset"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model_1/max_pooling2d_2/MaxPool", "op": "MaxPool", "input": ["StatefulPartitionedCall/model_1/block3_sepconv2_bn/FusedBatchNormV3"], "attr": {"strides": {"list": {"i": ["1", "2", "2", "1"]}}, "ksize": {"list": {"i": ["1", "3", "3", "1"]}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/add_2/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model_1/max_pooling2d_2/MaxPool", "StatefulPartitionedCall/model_1/batch_normalization_2/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/conv2d_5/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model_1/add_2/add", "unknown_43", "unknown_44"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "epsilon": {"f": 0}, "padding": {"s": "U0FNRQ=="}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/model_1/global_average_pooling2d_1/Mean", "op": "Mean", "input": ["StatefulPartitionedCall/model_1/conv2d_5/BiasAdd", "StatefulPartitionedCall/model_1/global_average_pooling2d_1/Mean/reduction_indices"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/model_1/predictions/Softmax", "op": "Softmax", "input": ["StatefulPartitionedCall/model_1/global_average_pooling2d_1/Mean"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity", "op": "Identity", "input": ["StatefulPartitionedCall/model_1/predictions/Softmax"], "attr": {"T": {"type": "DT_FLOAT"}}}], "library": {}, "versions": {}}, "weightsManifest": [{"paths": ["emotion.bin"], "weights": [{"name": "unknown_26", "shape": [3, 3, 128, 1], "dtype": "float32"}, {"name": "unknown_32", "shape": [3, 3, 256, 1], "dtype": "float32"}, {"name": "unknown_9", "shape": [3, 3, 64, 1], "dtype": "float32"}, {"name": "unknown_15", "shape": [3, 3, 128, 1], "dtype": "float32"}, {"name": "unknown_43", "shape": [3, 3, 256, 7], "dtype": "float32"}, {"name": "unknown_44", "shape": [7], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/global_average_pooling2d_1/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model_1/conv2d_1/Conv2D_weights", "shape": [3, 3, 1, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/conv2d_1/Conv2D_bn_offset", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/conv2d_2/Conv2D_weights", "shape": [3, 3, 32, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/separable_conv2d_4/separable_conv2d_weights", "shape": [1, 1, 256, 256], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/conv2d_2/Conv2D_bn_offset", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/conv2d_3/Conv2D_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/separable_conv2d_4/separable_conv2d_bn_offset", "shape": [256], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/conv2d_3/Conv2D_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/separable_conv2d_1/separable_conv2d_weights", "shape": [1, 1, 64, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/separable_conv2d_1/separable_conv2d_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/separable_conv2d_2/separable_conv2d_weights", "shape": [1, 1, 128, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/separable_conv2d_2/separable_conv2d_bn_offset", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/conv2d_4/Conv2D_weights", "shape": [1, 1, 128, 256], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/conv2d_4/Conv2D_bn_offset", "shape": [256], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/separable_conv2d_3/separable_conv2d_weights", "shape": [1, 1, 128, 256], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/separable_conv2d_3/separable_conv2d_bn_offset", "shape": [256], "dtype": "float32"}]}]}