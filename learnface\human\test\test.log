2025-02-05 09:39:19 [36mINFO: [39m @vladmandic/human version 3.3.5 
2025-02-05 09:39:19 [36mINFO: [39m User: vlado Platform: linux Arch: x64 Node: v22.13.1 
2025-02-05 09:39:19 [36mINFO: [39m demos: [{"cmd":"../demo/nodejs/node.js","args":[]},{"cmd":"../demo/nodejs/node-simple.js","args":[]},{"cmd":"../demo/nodejs/node-event.js","args":["samples/in/ai-body.jpg"]},{"cmd":"../demo/nodejs/node-similarity.js","args":["samples/in/ai-face.jpg","samples/in/ai-upper.jpg"]},{"cmd":"../demo/nodejs/node-canvas.js","args":["samples/in/ai-body.jpg","samples/out/ai-body.jpg"]},{"cmd":"../demo/nodejs/process-folder.js","args":["samples"]},{"cmd":"../demo/multithread/node-multiprocess.js","args":[]},{"cmd":"../demo/facematch/node-match.js","args":[]},{"cmd":"../demo/nodejs/node-bench.js","args":[]},{"cmd":"../test/test-node-emotion.js","args":[]}] 
2025-02-05 09:39:19 [36mINFO: [39m {"cmd":"../demo/nodejs/node.js","args":[]} start 
2025-02-05 09:39:21 [36mINFO: [39m {"cmd":"../demo/nodejs/node-simple.js","args":[]} start 
2025-02-05 09:39:21 [36mINFO: [39m {"cmd":"../demo/nodejs/node-event.js","args":["samples/in/ai-body.jpg"]} start 
2025-02-05 09:39:22 [36mINFO: [39m {"cmd":"../demo/nodejs/node-similarity.js","args":["samples/in/ai-face.jpg","samples/in/ai-upper.jpg"]} start 
2025-02-05 09:39:23 [36mINFO: [39m {"cmd":"../demo/nodejs/node-canvas.js","args":["samples/in/ai-body.jpg","samples/out/ai-body.jpg"]} start 
2025-02-05 09:39:24 [36mINFO: [39m {"cmd":"../demo/nodejs/process-folder.js","args":["samples"]} start 
2025-02-05 09:39:26 [36mINFO: [39m {"cmd":"../demo/multithread/node-multiprocess.js","args":[]} start 
2025-02-05 09:39:40 [36mINFO: [39m {"cmd":"../demo/facematch/node-match.js","args":[]} start 
2025-02-05 09:39:41 [36mINFO: [39m {"cmd":"../demo/nodejs/node-bench.js","args":[]} start 
2025-02-05 09:39:52 [36mINFO: [39m {"cmd":"../test/test-node-emotion.js","args":[]} start 
2025-02-05 09:39:58 [36mINFO: [39m tests: ["test-node-load.js","test-node-gear.js","test-backend-node.js","test-backend-node-gpu.js","test-backend-node-wasm.js"] 
2025-02-05 09:39:58 [36mINFO: [39m 
2025-02-05 09:39:58 [36mINFO: [39m test-node-load.js start 
2025-02-05 09:39:58 [36mINFO: [39m test-node-load.js load start {"human":"3.3.5","tf":"4.22.0","progress":0} 
2025-02-05 09:39:58 [32mDATA: [39m test-node-load.js load interval {"elapsed":0,"progress":0} 
2025-02-05 09:39:58 [32mDATA: [39m test-node-load.js load interval {"elapsed":11,"progress":0} 
2025-02-05 09:39:58 [32mDATA: [39m test-node-load.js load interval {"elapsed":26,"progress":0.12865823126207815} 
2025-02-05 09:39:58 [32mDATA: [39m test-node-load.js load interval {"elapsed":36,"progress":0.2204080722078057} 
2025-02-05 09:39:58 [32mDATA: [39m test-node-load.js load interval {"elapsed":59,"progress":0.4726970548784964} 
2025-02-05 09:39:58 [32mDATA: [39m test-node-load.js load interval {"elapsed":71,"progress":0.68355493315821} 
2025-02-05 09:39:58 [35mSTATE:[39m test-node-load.js passed {"progress":1} 
2025-02-05 09:39:58 [36mINFO: [39m test-node-load.js load final {"progress":1} 
2025-02-05 09:39:59 [32mDATA: [39m test-node-load.js load interval {"elapsed":461,"progress":1} 
2025-02-05 09:39:59 [36mINFO: [39m 
2025-02-05 09:39:59 [36mINFO: [39m test-node-gear.js start 
2025-02-05 09:39:59 [32mDATA: [39m test-node-gear.js input: ["samples/in/ai-face.jpg"] 
2025-02-05 09:40:00 [35mSTATE:[39m test-node-gear.js passed: gear faceres samples/in/ai-face.jpg 
2025-02-05 09:40:00 [32mDATA: [39m test-node-gear.js results {"face":0,"model":"faceres","image":"samples/in/ai-face.jpg","age":23.6,"gender":"female","genderScore":0.95,"emotion":[]} 
2025-02-05 09:40:00 [35mSTATE:[39m test-node-gear.js passed: gear gear samples/in/ai-face.jpg 
2025-02-05 09:40:00 [32mDATA: [39m test-node-gear.js results {"face":0,"model":"gear","image":"samples/in/ai-face.jpg","age":25.2,"gender":"female","genderScore":0.7,"race":[{"score":0.96,"race":"white"}],"emotion":[]} 
2025-02-05 09:40:00 [35mSTATE:[39m test-node-gear.js passed: gear ssrnet samples/in/ai-face.jpg 
2025-02-05 09:40:00 [32mDATA: [39m test-node-gear.js results {"face":0,"model":"ssrnet","image":"samples/in/ai-face.jpg","age":22.6,"gender":"female","genderScore":0.98,"emotion":[]} 
2025-02-05 09:40:01 [35mSTATE:[39m test-node-gear.js passed: gear ssrnet samples/in/ai-face.jpg 
2025-02-05 09:40:01 [32mDATA: [39m test-node-gear.js results {"face":0,"model":"ssrnet","image":"samples/in/ai-face.jpg","age":22.6,"gender":"male","emotion":[]} 
2025-02-05 09:40:01 [36mINFO: [39m 
2025-02-05 09:40:01 [36mINFO: [39m test-backend-node.js start 
2025-02-05 09:40:01 [36mINFO: [39m test-backend-node.js test: configuration validation 
2025-02-05 09:40:01 [35mSTATE:[39m test-backend-node.js passed: configuration default validation [] 
2025-02-05 09:40:01 [35mSTATE:[39m test-backend-node.js passed: configuration invalid validation [{"reason":"unknown property","where":"config.invalid = true"}] 
2025-02-05 09:40:01 [36mINFO: [39m test-backend-node.js test: model load 
2025-02-05 09:40:01 [35mSTATE:[39m test-backend-node.js passed: models loaded 11 11 [{"name":"liveness","loaded":true,"size":592976,"url":"file://models/liveness.json"},{"name":"emotion","loaded":true,"size":820516,"url":"file://models/emotion.json"},{"name":"antispoof","loaded":true,"size":853098,"url":"file://models/antispoof.json"},{"name":"blazeface","loaded":true,"size":538928,"url":"file://models/blazeface.json"},{"name":"facemesh","loaded":true,"size":1477958,"url":"file://models/facemesh.json"},{"name":"handskeleton","loaded":true,"size":0,"url":"file://models/handlandmark-lite.json"},{"name":"iris","loaded":true,"size":2599092,"url":"file://models/iris.json"},{"name":"handtrack","loaded":true,"size":2964837,"url":"file://models/handtrack.json"},{"name":"centernet","loaded":true,"size":4030290,"url":"file://models/centernet.json"},{"name":"movenet","loaded":true,"size":4650216,"url":"file://models/movenet-lightning.json"},{"name":"faceres","loaded":true,"size":6978814,"url":"file://models/faceres.json"}] 
2025-02-05 09:40:01 [36mINFO: [39m test-backend-node.js memory: {"memory":{"unreliable":true,"numTensors":1785,"numDataBuffers":1785,"numBytes":56431460}} 
2025-02-05 09:40:01 [36mINFO: [39m test-backend-node.js state: {"state":{"registeredVariables":{},"nextTapeNodeId":0,"numBytes":56431460,"numTensors":1785,"numStringTensors":0,"numDataBuffers":1785,"gradientDepth":0,"kernelDepth":0,"scopeStack":[],"numDataMovesStack":[],"nextScopeId":0,"tensorInfo":{},"profiling":false,"activeProfile":{"newBytes":0,"newTensors":0,"peakBytes":0,"kernels":[],"result":null,"kernelNames":[]}}} 
2025-02-05 09:40:01 [36mINFO: [39m test-backend-node.js test: warmup 
2025-02-05 09:40:01 [35mSTATE:[39m test-backend-node.js passed: create human 
2025-02-05 09:40:01 [36mINFO: [39m test-backend-node.js human version: 3.3.5 
2025-02-05 09:40:01 [36mINFO: [39m test-backend-node.js platform:  agent:  
2025-02-05 09:40:01 [36mINFO: [39m test-backend-node.js tfjs version: 4.22.0 
2025-02-05 09:40:01 [36mINFO: [39m test-backend-node.js env: {"browser":false,"node":true,"platform":"","agent":"","backends":["cpu","tensorflow"],"initial":false,"tfjs":{"version":"4.22.0"},"offscreen":false,"perfadd":false,"tensorflow":{"version":"2.9.1","gpu":false},"wasm":{"supported":true,"backend":false},"webgl":{"supported":false,"backend":false},"webgpu":{"supported":false,"backend":false},"cpu":{"flags":[]},"kernels":172} 
2025-02-05 09:40:01 [35mSTATE:[39m test-backend-node.js passed: set backend: tensorflow 
2025-02-05 09:40:01 [35mSTATE:[39m test-backend-node.js tensors 1785 
2025-02-05 09:40:01 [35mSTATE:[39m test-backend-node.js  result: defined models: 11 loaded models: 11 
2025-02-05 09:40:01 [35mSTATE:[39m test-backend-node.js passed: load models 11 
2025-02-05 09:40:01 [35mSTATE:[39m test-backend-node.js passed: warmup: none default 
2025-02-05 09:40:01 [32mDATA: [39m test-backend-node.js  result: face: 0 body: 0 hand: 0 gesture: 0 object: 0 person: 0 {} {} {} 
2025-02-05 09:40:01 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: null 
2025-02-05 09:40:01 [35mSTATE:[39m test-backend-node.js passed: warmup none result match 
2025-02-05 09:40:01 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:01 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:01 [35mSTATE:[39m test-backend-node.js event: warmup 
2025-02-05 09:40:01 [35mSTATE:[39m test-backend-node.js passed: warmup: face default 
2025-02-05 09:40:01 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 1 gesture: 6 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.82,"class":"person"} {"score":0.42,"keypoints":4} 
2025-02-05 09:40:01 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 460 
2025-02-05 09:40:01 [35mSTATE:[39m test-backend-node.js passed: warmup face result match 
2025-02-05 09:40:01 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js event: warmup 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js passed: warmup: body default 
2025-02-05 09:40:02 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 1 gesture: 7 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.72,"class":"person"} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:02 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 348 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js passed: warmup body result match 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js details: {"face":{"boxScore":0.93,"faceScore":1,"age":23.6,"gender":"female","genderScore":0.97},"emotion":[{"score":0.47,"emotion":"angry"},{"score":0.32,"emotion":"fear"},{"score":0.1,"emotion":"neutral"}],"body":{"score":0.92,"keypoints":17},"hand":{"boxScore":0.52,"fingerScore":0.73,"keypoints":21},"gestures":[{"face":0,"gesture":"facing right"},{"face":0,"gesture":"mouth 16% open"},{"hand":0,"gesture":"pinky forward"},{"hand":0,"gesture":"thumb up"},{"hand":0,"gesture":"open palm"},{"iris":0,"gesture":"looking left"},{"iris":0,"gesture":"looking up"}]} 
2025-02-05 09:40:02 [36mINFO: [39m test-backend-node.js test: details verification 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js start default 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-body.jpg default 
2025-02-05 09:40:02 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 1 gesture: 7 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.72,"class":"person"} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:02 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 340 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js passed: details face length 1 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js passed: details face score 1 0.93 1 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js passed: details face age/gender 23.6 female 0.97 2.34 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js passed: details face arrays 4 478 1024 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js passed: details face emotion 3 {"score":0.48,"emotion":"angry"} 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js passed: details face anti-spoofing 0.8 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js passed: details face liveness 0.93 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js passed: details body length 1 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js passed: details body 0.92 17 6 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js passed: details hand length 1 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js passed: details hand 0.51 0.73 point 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js passed: details hand arrays 21 5 7 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js passed: details gesture length 7 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js passed: details gesture first {"face":0,"gesture":"facing right"} 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js passed: details object length 1 
2025-02-05 09:40:02 [35mSTATE:[39m test-backend-node.js passed: details object 0.72 person 
2025-02-05 09:40:03 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,4] {"checksum":1371996928} 
2025-02-05 09:40:03 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:03 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:03 [35mSTATE:[39m test-backend-node.js passed: tensor shape: [1,1200,1200,4] dtype: float32 
2025-02-05 09:40:03 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-body.jpg [1200,1200,4] {"checksum":1371996928} 
2025-02-05 09:40:03 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:04 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:04 [35mSTATE:[39m test-backend-node.js passed: tensor shape: [1200,1200,4] dtype: float32 
2025-02-05 09:40:04 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:04 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:04 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:04 [35mSTATE:[39m test-backend-node.js passed: tensor shape: [1,1200,1200,3] dtype: float32 
2025-02-05 09:40:04 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-body.jpg [1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:04 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:05 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:05 [35mSTATE:[39m test-backend-node.js passed: tensor shape: [1200,1200,3] dtype: float32 
2025-02-05 09:40:05 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,4] {"checksum":1371996871} 
2025-02-05 09:40:05 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:05 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:05 [35mSTATE:[39m test-backend-node.js passed: tensor shape: [1,1200,1200,4] dtype: int32 
2025-02-05 09:40:05 [36mINFO: [39m test-backend-node.js test default 
2025-02-05 09:40:05 [35mSTATE:[39m test-backend-node.js start async 
2025-02-05 09:40:06 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:06 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:06 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:06 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-body.jpg async 
2025-02-05 09:40:06 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 1 gesture: 7 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.72,"class":"person"} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:06 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 346 
2025-02-05 09:40:06 [35mSTATE:[39m test-backend-node.js passed: default result face match 1 female 0.97 
2025-02-05 09:40:06 [36mINFO: [39m test-backend-node.js test sync 
2025-02-05 09:40:06 [35mSTATE:[39m test-backend-node.js start sync 
2025-02-05 09:40:06 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:06 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:07 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:07 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-body.jpg sync 
2025-02-05 09:40:07 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 1 gesture: 7 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.72,"class":"person"} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:07 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 343 
2025-02-05 09:40:07 [35mSTATE:[39m test-backend-node.js passed: default sync 1 female 0.97 
2025-02-05 09:40:07 [36mINFO: [39m test-backend-node.js test: image process 
2025-02-05 09:40:07 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34696120} 
2025-02-05 09:40:07 [35mSTATE:[39m test-backend-node.js passed: image input null [1,256,256,3] 
2025-02-05 09:40:07 [36mINFO: [39m test-backend-node.js test: image null 
2025-02-05 09:40:07 [35mSTATE:[39m test-backend-node.js passed: invalid input could not convert input to tensor 
2025-02-05 09:40:07 [36mINFO: [39m test-backend-node.js test face similarity 
2025-02-05 09:40:07 [35mSTATE:[39m test-backend-node.js start face similarity 
2025-02-05 09:40:07 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34696120} 
2025-02-05 09:40:07 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:07 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:07 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-face.jpg face similarity 
2025-02-05 09:40:07 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 1 gesture: 6 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.82,"class":"person"} {"score":0.47,"keypoints":3} 
2025-02-05 09:40:07 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 340 
2025-02-05 09:40:07 [35mSTATE:[39m test-backend-node.js start face similarity 
2025-02-05 09:40:07 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:07 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:08 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:08 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-body.jpg face similarity 
2025-02-05 09:40:08 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 1 gesture: 7 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.72,"class":"person"} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:08 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 324 
2025-02-05 09:40:08 [35mSTATE:[39m test-backend-node.js start face similarity 
2025-02-05 09:40:08 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-upper.jpg [1,720,688,3] {"checksum":151289024} 
2025-02-05 09:40:08 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:08 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:08 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-upper.jpg face similarity 
2025-02-05 09:40:08 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 0 gesture: 4 object: 1 person: 1 {"score":1,"age":23.5,"gender":"female"} {"score":0.71,"class":"person"} {"score":0.75,"keypoints":7} 
2025-02-05 09:40:08 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 304 
2025-02-05 09:40:08 [35mSTATE:[39m test-backend-node.js passed: face descriptor 
2025-02-05 09:40:08 [35mSTATE:[39m test-backend-node.js passed: face similarity {"similarity":[1,0.43,0.57],"descriptors":[1024,1024,1024]} 
2025-02-05 09:40:08 [36mINFO: [39m test-backend-node.js test object 
2025-02-05 09:40:08 [35mSTATE:[39m test-backend-node.js start object 
2025-02-05 09:40:08 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:08 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:09 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:09 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-body.jpg object 
2025-02-05 09:40:09 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 1 gesture: 7 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.72,"class":"person"} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:09 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 337 
2025-02-05 09:40:09 [35mSTATE:[39m test-backend-node.js passed: centernet 
2025-02-05 09:40:09 [35mSTATE:[39m test-backend-node.js start object 
2025-02-05 09:40:10 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:10 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:10 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:10 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-body.jpg object 
2025-02-05 09:40:10 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 1 gesture: 7 object: 3 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.86,"class":"person"} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:10 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 343 
2025-02-05 09:40:10 [35mSTATE:[39m test-backend-node.js passed: nanodet 
2025-02-05 09:40:10 [36mINFO: [39m test-backend-node.js test sensitive 
2025-02-05 09:40:10 [35mSTATE:[39m test-backend-node.js start sensitive 
2025-02-05 09:40:11 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:11 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:11 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:11 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-body.jpg sensitive 
2025-02-05 09:40:11 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 2 gesture: 9 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:11 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 308 
2025-02-05 09:40:11 [35mSTATE:[39m test-backend-node.js passed: sensitive result match 
2025-02-05 09:40:11 [35mSTATE:[39m test-backend-node.js passed: sensitive face result match 
2025-02-05 09:40:11 [35mSTATE:[39m test-backend-node.js passed: sensitive face emotion result [{"score":0.48,"emotion":"angry"},{"score":0.31,"emotion":"fear"},{"score":0.11,"emotion":"neutral"}] 
2025-02-05 09:40:11 [35mSTATE:[39m test-backend-node.js passed: sensitive body result match 
2025-02-05 09:40:11 [35mSTATE:[39m test-backend-node.js passed: sensitive hand result match 
2025-02-05 09:40:11 [36mINFO: [39m test-backend-node.js test body 
2025-02-05 09:40:11 [35mSTATE:[39m test-backend-node.js start blazepose 
2025-02-05 09:40:13 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:13 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:13 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:13 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-body.jpg blazepose 
2025-02-05 09:40:13 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 2 gesture: 9 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.99,"keypoints":39} 
2025-02-05 09:40:13 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 419 
2025-02-05 09:40:13 [35mSTATE:[39m test-backend-node.js passed: blazepose 
2025-02-05 09:40:13 [35mSTATE:[39m test-backend-node.js start efficientpose 
2025-02-05 09:40:14 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:14 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:15 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:15 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-body.jpg efficientpose 
2025-02-05 09:40:15 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 2 gesture: 9 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.75,"keypoints":13} 
2025-02-05 09:40:15 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 384 
2025-02-05 09:40:15 [35mSTATE:[39m test-backend-node.js passed: efficientpose 
2025-02-05 09:40:15 [35mSTATE:[39m test-backend-node.js start posenet 
2025-02-05 09:40:16 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:16 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:16 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:16 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-body.jpg posenet 
2025-02-05 09:40:16 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 2 gesture: 9 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.96,"keypoints":16} 
2025-02-05 09:40:16 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 308 
2025-02-05 09:40:16 [35mSTATE:[39m test-backend-node.js passed: posenet 
2025-02-05 09:40:16 [35mSTATE:[39m test-backend-node.js start movenet 
2025-02-05 09:40:16 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:16 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:16 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:16 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-body.jpg movenet 
2025-02-05 09:40:16 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 2 gesture: 9 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:16 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 290 
2025-02-05 09:40:16 [35mSTATE:[39m test-backend-node.js passed: movenet 
2025-02-05 09:40:16 [36mINFO: [39m test-backend-node.js test face matching 
2025-02-05 09:40:16 [35mSTATE:[39m test-backend-node.js passed: face database 40 
2025-02-05 09:40:16 [35mSTATE:[39m test-backend-node.js passed: face match {"first":{"index":4,"similarity":0.78}} {"second":{"index":4,"similarity":0.5}} {"third":{"index":4,"similarity":0.54}} 
2025-02-05 09:40:16 [36mINFO: [39m test-backend-node.js test face similarity alternative 
2025-02-05 09:40:16 [35mSTATE:[39m test-backend-node.js start face embeddings 
2025-02-05 09:40:17 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34696120} 
2025-02-05 09:40:17 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:17 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:17 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-face.jpg face embeddings 
2025-02-05 09:40:17 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 2 gesture: 8 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.47,"keypoints":3} 
2025-02-05 09:40:17 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 347 
2025-02-05 09:40:17 [35mSTATE:[39m test-backend-node.js passed: mobilefacenet {"embedding":192} 
2025-02-05 09:40:17 [35mSTATE:[39m test-backend-node.js start face embeddings 
2025-02-05 09:40:18 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34696120} 
2025-02-05 09:40:18 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:19 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:19 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-face.jpg face embeddings 
2025-02-05 09:40:19 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 2 gesture: 8 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.47,"keypoints":3} 
2025-02-05 09:40:19 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 360 
2025-02-05 09:40:19 [35mSTATE:[39m test-backend-node.js passed: insightface {"embedding":512} 
2025-02-05 09:40:19 [36mINFO: [39m test-backend-node.js test face attention 
2025-02-05 09:40:19 [35mSTATE:[39m test-backend-node.js start face attention 
2025-02-05 09:40:19 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34696120} 
2025-02-05 09:40:19 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:20 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:20 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-face.jpg face attention 
2025-02-05 09:40:20 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 2 gesture: 7 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.47,"keypoints":3} 
2025-02-05 09:40:20 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 347 
2025-02-05 09:40:20 [35mSTATE:[39m test-backend-node.js passed: face attention 
2025-02-05 09:40:20 [36mINFO: [39m test-backend-node.js test detectors 
2025-02-05 09:40:20 [35mSTATE:[39m test-backend-node.js start detectors 
2025-02-05 09:40:20 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:20 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:20 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:20 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-body.jpg detectors 
2025-02-05 09:40:20 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 1 gesture: 0 object: 0 person: 1 {"score":0.93,"gender":"unknown"} {} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:20 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 152 
2025-02-05 09:40:20 [35mSTATE:[39m test-backend-node.js passed: detector result face match 
2025-02-05 09:40:20 [35mSTATE:[39m test-backend-node.js passed: detector result hand match 
2025-02-05 09:40:20 [36mINFO: [39m test-backend-node.js test: multi-instance 
2025-02-05 09:40:20 [35mSTATE:[39m test-backend-node.js start multi instance 
2025-02-05 09:40:20 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:20 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:20 [35mSTATE:[39m test-backend-node.js passed: detect: random multi instance 
2025-02-05 09:40:20 [32mDATA: [39m test-backend-node.js  result: face: 0 body: 1 hand: 0 gesture: 0 object: 0 person: 0 {} {} {"score":0,"keypoints":0} 
2025-02-05 09:40:20 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 146 
2025-02-05 09:40:20 [36mINFO: [39m test-backend-node.js test: first instance 
2025-02-05 09:40:20 [35mSTATE:[39m test-backend-node.js start multi instance 
2025-02-05 09:40:21 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-upper.jpg [1,720,688,3] {"checksum":151289024} 
2025-02-05 09:40:21 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-upper.jpg multi instance 
2025-02-05 09:40:21 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 0 gesture: 0 object: 0 person: 1 {"score":0.97,"gender":"unknown"} {} {"score":0.75,"keypoints":7} 
2025-02-05 09:40:21 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 149 
2025-02-05 09:40:21 [36mINFO: [39m test-backend-node.js test: second instance 
2025-02-05 09:40:21 [35mSTATE:[39m test-backend-node.js start multi instance 
2025-02-05 09:40:21 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-upper.jpg [1,720,688,3] {"checksum":151289024} 
2025-02-05 09:40:21 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-upper.jpg multi instance 
2025-02-05 09:40:21 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 0 gesture: 0 object: 0 person: 1 {"score":0.97,"gender":"unknown"} {} {"score":0.75,"keypoints":7} 
2025-02-05 09:40:21 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 151 
2025-02-05 09:40:21 [36mINFO: [39m test-backend-node.js test: concurrent 
2025-02-05 09:40:21 [35mSTATE:[39m test-backend-node.js start concurrent 
2025-02-05 09:40:21 [35mSTATE:[39m test-backend-node.js start concurrent 
2025-02-05 09:40:21 [35mSTATE:[39m test-backend-node.js start concurrent 
2025-02-05 09:40:21 [35mSTATE:[39m test-backend-node.js start concurrent 
2025-02-05 09:40:21 [35mSTATE:[39m test-backend-node.js start concurrent 
2025-02-05 09:40:21 [35mSTATE:[39m test-backend-node.js start concurrent 
2025-02-05 09:40:21 [35mSTATE:[39m test-backend-node.js start concurrent 
2025-02-05 09:40:21 [35mSTATE:[39m test-backend-node.js start concurrent 
2025-02-05 09:40:21 [35mSTATE:[39m test-backend-node.js start concurrent 
2025-02-05 09:40:21 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34696120} 
2025-02-05 09:40:21 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34696120} 
2025-02-05 09:40:21 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34696120} 
2025-02-05 09:40:21 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:22 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:22 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:22 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-upper.jpg [1,720,688,3] {"checksum":151289024} 
2025-02-05 09:40:22 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-upper.jpg [1,720,688,3] {"checksum":151289024} 
2025-02-05 09:40:22 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-upper.jpg [1,720,688,3] {"checksum":151289024} 
2025-02-05 09:40:22 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:22 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:22 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:24 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:24 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-upper.jpg concurrent 
2025-02-05 09:40:24 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 0 gesture: 0 object: 0 person: 1 {"score":0.97,"gender":"unknown"} {} {"score":0.75,"keypoints":7} 
2025-02-05 09:40:24 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 1286 
2025-02-05 09:40:24 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-upper.jpg concurrent 
2025-02-05 09:40:24 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 0 gesture: 0 object: 0 person: 1 {"score":0.97,"gender":"unknown"} {} {"score":0.75,"keypoints":7} 
2025-02-05 09:40:24 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 1286 
2025-02-05 09:40:24 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-upper.jpg concurrent 
2025-02-05 09:40:24 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 0 gesture: 0 object: 0 person: 1 {"score":0.97,"gender":"unknown"} {} {"score":0.75,"keypoints":7} 
2025-02-05 09:40:24 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 1286 
2025-02-05 09:40:24 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:24 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:24 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-face.jpg concurrent 
2025-02-05 09:40:24 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 1 gesture: 0 object: 0 person: 1 {"score":0.9,"gender":"unknown"} {} {"score":0.47,"keypoints":3} 
2025-02-05 09:40:24 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 1287 
2025-02-05 09:40:24 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-face.jpg concurrent 
2025-02-05 09:40:24 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 1 gesture: 0 object: 0 person: 1 {"score":0.9,"gender":"unknown"} {} {"score":0.47,"keypoints":3} 
2025-02-05 09:40:24 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 1287 
2025-02-05 09:40:24 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-face.jpg concurrent 
2025-02-05 09:40:24 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 1 gesture: 0 object: 0 person: 1 {"score":0.9,"gender":"unknown"} {} {"score":0.47,"keypoints":3} 
2025-02-05 09:40:24 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 1287 
2025-02-05 09:40:24 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-body.jpg concurrent 
2025-02-05 09:40:24 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 1 gesture: 0 object: 0 person: 1 {"score":0.93,"gender":"unknown"} {} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:24 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 1287 
2025-02-05 09:40:24 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-body.jpg concurrent 
2025-02-05 09:40:24 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 1 gesture: 0 object: 0 person: 1 {"score":0.93,"gender":"unknown"} {} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:24 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 1287 
2025-02-05 09:40:24 [35mSTATE:[39m test-backend-node.js passed: detect: samples/in/ai-body.jpg concurrent 
2025-02-05 09:40:24 [32mDATA: [39m test-backend-node.js  result: face: 1 body: 1 hand: 1 gesture: 0 object: 0 person: 1 {"score":0.93,"gender":"unknown"} {} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:24 [32mDATA: [39m test-backend-node.js  result: performance: load: null total: 1287 
2025-02-05 09:40:24 [36mINFO: [39m test-backend-node.js test: monkey-patch 
2025-02-05 09:40:24 [35mSTATE:[39m test-backend-node.js event: image 
2025-02-05 09:40:24 [35mSTATE:[39m test-backend-node.js event: detect 
2025-02-05 09:40:24 [35mSTATE:[39m test-backend-node.js passed: monkey patch 
2025-02-05 09:40:24 [35mSTATE:[39m test-backend-node.js passed: segmentation [262144] 
2025-02-05 09:40:24 [35mSTATE:[39m test-backend-node.js passeed: equal usage 
2025-02-05 09:40:24 [36mINFO: [39m test-backend-node.js test: input compare 
2025-02-05 09:40:24 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34696120} 
2025-02-05 09:40:25 [35mSTATE:[39m test-backend-node.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:25 [35mSTATE:[39m test-backend-node.js passed: image compare 0 23.275441687091504 
2025-02-05 09:40:25 [36mINFO: [39m test-backend-node.js events: {"image":29,"detect":29,"warmup":2} 
2025-02-05 09:40:25 [36mINFO: [39m test-backend-node.js tensors 14855 
2025-02-05 09:40:25 [36mINFO: [39m test-backend-node.js test complete: 23780 ms 
2025-02-05 09:40:25 [36mINFO: [39m 
2025-02-05 09:40:25 [36mINFO: [39m test-backend-node-gpu.js start 
2025-02-05 09:40:25 [36mINFO: [39m test-backend-node-gpu.js test: configuration validation 
2025-02-05 09:40:25 [35mSTATE:[39m test-backend-node-gpu.js passed: configuration default validation [] 
2025-02-05 09:40:25 [35mSTATE:[39m test-backend-node-gpu.js passed: configuration invalid validation [{"reason":"unknown property","where":"config.invalid = true"}] 
2025-02-05 09:40:25 [36mINFO: [39m test-backend-node-gpu.js test: model load 
2025-02-05 09:40:25 [35mSTATE:[39m test-backend-node-gpu.js passed: models loaded 11 11 [{"name":"blazeface","loaded":true,"size":538928,"url":"file://models/blazeface.json"},{"name":"liveness","loaded":true,"size":592976,"url":"file://models/liveness.json"},{"name":"emotion","loaded":true,"size":820516,"url":"file://models/emotion.json"},{"name":"antispoof","loaded":true,"size":853098,"url":"file://models/antispoof.json"},{"name":"facemesh","loaded":true,"size":1477958,"url":"file://models/facemesh.json"},{"name":"iris","loaded":true,"size":2599092,"url":"file://models/iris.json"},{"name":"handskeleton","loaded":true,"size":0,"url":"file://models/handlandmark-lite.json"},{"name":"handtrack","loaded":true,"size":2964837,"url":"file://models/handtrack.json"},{"name":"centernet","loaded":true,"size":4030290,"url":"file://models/centernet.json"},{"name":"movenet","loaded":true,"size":4650216,"url":"file://models/movenet-lightning.json"},{"name":"faceres","loaded":true,"size":6978814,"url":"file://models/faceres.json"}] 
2025-02-05 09:40:25 [36mINFO: [39m test-backend-node-gpu.js memory: {"memory":{"unreliable":true,"numTensors":1785,"numDataBuffers":1785,"numBytes":56431460}} 
2025-02-05 09:40:25 [36mINFO: [39m test-backend-node-gpu.js state: {"state":{"registeredVariables":{},"nextTapeNodeId":0,"numBytes":56431460,"numTensors":1785,"numStringTensors":0,"numDataBuffers":1785,"gradientDepth":0,"kernelDepth":0,"scopeStack":[],"numDataMovesStack":[],"nextScopeId":0,"tensorInfo":{},"profiling":false,"activeProfile":{"newBytes":0,"newTensors":0,"peakBytes":0,"kernels":[],"result":null,"kernelNames":[]}}} 
2025-02-05 09:40:25 [36mINFO: [39m test-backend-node-gpu.js test: warmup 
2025-02-05 09:40:25 [35mSTATE:[39m test-backend-node-gpu.js passed: create human 
2025-02-05 09:40:25 [36mINFO: [39m test-backend-node-gpu.js human version: 3.3.5 
2025-02-05 09:40:25 [36mINFO: [39m test-backend-node-gpu.js platform:  agent:  
2025-02-05 09:40:25 [36mINFO: [39m test-backend-node-gpu.js tfjs version: 4.22.0 
2025-02-05 09:40:25 [36mINFO: [39m test-backend-node-gpu.js env: {"browser":false,"node":true,"platform":"","agent":"","backends":["cpu","tensorflow"],"initial":false,"tfjs":{"version":"4.22.0"},"offscreen":false,"perfadd":false,"tensorflow":{"version":"2.9.1","gpu":false},"wasm":{"supported":true,"backend":false},"webgl":{"supported":false,"backend":false},"webgpu":{"supported":false,"backend":false},"cpu":{"flags":[]},"kernels":172} 
2025-02-05 09:40:25 [35mSTATE:[39m test-backend-node-gpu.js passed: set backend: tensorflow 
2025-02-05 09:40:25 [35mSTATE:[39m test-backend-node-gpu.js tensors 1785 
2025-02-05 09:40:25 [35mSTATE:[39m test-backend-node-gpu.js  result: defined models: 11 loaded models: 11 
2025-02-05 09:40:25 [35mSTATE:[39m test-backend-node-gpu.js passed: load models 11 
2025-02-05 09:40:25 [35mSTATE:[39m test-backend-node-gpu.js passed: warmup: none default 
2025-02-05 09:40:25 [32mDATA: [39m test-backend-node-gpu.js  result: face: 0 body: 0 hand: 0 gesture: 0 object: 0 person: 0 {} {} {} 
2025-02-05 09:40:25 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: null 
2025-02-05 09:40:25 [35mSTATE:[39m test-backend-node-gpu.js passed: warmup none result match 
2025-02-05 09:40:25 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:26 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:26 [35mSTATE:[39m test-backend-node-gpu.js event: warmup 
2025-02-05 09:40:26 [35mSTATE:[39m test-backend-node-gpu.js passed: warmup: face default 
2025-02-05 09:40:26 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 1 gesture: 6 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.82,"class":"person"} {"score":0.42,"keypoints":4} 
2025-02-05 09:40:26 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 502 
2025-02-05 09:40:26 [35mSTATE:[39m test-backend-node-gpu.js passed: warmup face result match 
2025-02-05 09:40:26 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:26 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:26 [35mSTATE:[39m test-backend-node-gpu.js event: warmup 
2025-02-05 09:40:26 [35mSTATE:[39m test-backend-node-gpu.js passed: warmup: body default 
2025-02-05 09:40:26 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 1 gesture: 7 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.72,"class":"person"} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:26 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 354 
2025-02-05 09:40:26 [35mSTATE:[39m test-backend-node-gpu.js passed: warmup body result match 
2025-02-05 09:40:26 [35mSTATE:[39m test-backend-node-gpu.js details: {"face":{"boxScore":0.93,"faceScore":1,"age":23.6,"gender":"female","genderScore":0.97},"emotion":[{"score":0.47,"emotion":"angry"},{"score":0.32,"emotion":"fear"},{"score":0.1,"emotion":"neutral"}],"body":{"score":0.92,"keypoints":17},"hand":{"boxScore":0.52,"fingerScore":0.73,"keypoints":21},"gestures":[{"face":0,"gesture":"facing right"},{"face":0,"gesture":"mouth 16% open"},{"hand":0,"gesture":"pinky forward"},{"hand":0,"gesture":"thumb up"},{"hand":0,"gesture":"open palm"},{"iris":0,"gesture":"looking left"},{"iris":0,"gesture":"looking up"}]} 
2025-02-05 09:40:26 [36mINFO: [39m test-backend-node-gpu.js test: details verification 
2025-02-05 09:40:26 [35mSTATE:[39m test-backend-node-gpu.js start default 
2025-02-05 09:40:26 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:26 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-body.jpg default 
2025-02-05 09:40:27 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 1 gesture: 7 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.72,"class":"person"} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:27 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 361 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js passed: details face length 1 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js passed: details face score 1 0.93 1 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js passed: details face age/gender 23.6 female 0.97 2.34 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js passed: details face arrays 4 478 1024 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js passed: details face emotion 3 {"score":0.48,"emotion":"angry"} 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js passed: details face anti-spoofing 0.8 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js passed: details face liveness 0.93 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js passed: details body length 1 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js passed: details body 0.92 17 6 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js passed: details hand length 1 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js passed: details hand 0.51 0.73 point 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js passed: details hand arrays 21 5 7 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js passed: details gesture length 7 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js passed: details gesture first {"face":0,"gesture":"facing right"} 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js passed: details object length 1 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js passed: details object 0.72 person 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,4] {"checksum":1371996928} 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:27 [35mSTATE:[39m test-backend-node-gpu.js passed: tensor shape: [1,1200,1200,4] dtype: float32 
2025-02-05 09:40:28 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-body.jpg [1200,1200,4] {"checksum":1371996928} 
2025-02-05 09:40:28 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:28 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:28 [35mSTATE:[39m test-backend-node-gpu.js passed: tensor shape: [1200,1200,4] dtype: float32 
2025-02-05 09:40:28 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:28 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:29 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:29 [35mSTATE:[39m test-backend-node-gpu.js passed: tensor shape: [1,1200,1200,3] dtype: float32 
2025-02-05 09:40:29 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-body.jpg [1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:29 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:29 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:29 [35mSTATE:[39m test-backend-node-gpu.js passed: tensor shape: [1200,1200,3] dtype: float32 
2025-02-05 09:40:29 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,4] {"checksum":1371996871} 
2025-02-05 09:40:29 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:30 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:30 [35mSTATE:[39m test-backend-node-gpu.js passed: tensor shape: [1,1200,1200,4] dtype: int32 
2025-02-05 09:40:30 [36mINFO: [39m test-backend-node-gpu.js test default 
2025-02-05 09:40:30 [35mSTATE:[39m test-backend-node-gpu.js start async 
2025-02-05 09:40:30 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:30 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:30 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:30 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-body.jpg async 
2025-02-05 09:40:30 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 1 gesture: 7 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.72,"class":"person"} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:30 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 367 
2025-02-05 09:40:30 [35mSTATE:[39m test-backend-node-gpu.js passed: default result face match 1 female 0.97 
2025-02-05 09:40:30 [36mINFO: [39m test-backend-node-gpu.js test sync 
2025-02-05 09:40:30 [35mSTATE:[39m test-backend-node-gpu.js start sync 
2025-02-05 09:40:31 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:31 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:31 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:31 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-body.jpg sync 
2025-02-05 09:40:31 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 1 gesture: 7 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.72,"class":"person"} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:31 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 372 
2025-02-05 09:40:31 [35mSTATE:[39m test-backend-node-gpu.js passed: default sync 1 female 0.97 
2025-02-05 09:40:31 [36mINFO: [39m test-backend-node-gpu.js test: image process 
2025-02-05 09:40:31 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34696120} 
2025-02-05 09:40:31 [35mSTATE:[39m test-backend-node-gpu.js passed: image input null [1,256,256,3] 
2025-02-05 09:40:31 [36mINFO: [39m test-backend-node-gpu.js test: image null 
2025-02-05 09:40:31 [35mSTATE:[39m test-backend-node-gpu.js passed: invalid input could not convert input to tensor 
2025-02-05 09:40:31 [36mINFO: [39m test-backend-node-gpu.js test face similarity 
2025-02-05 09:40:31 [35mSTATE:[39m test-backend-node-gpu.js start face similarity 
2025-02-05 09:40:31 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34696120} 
2025-02-05 09:40:31 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:32 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:32 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-face.jpg face similarity 
2025-02-05 09:40:32 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 1 gesture: 6 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.82,"class":"person"} {"score":0.47,"keypoints":3} 
2025-02-05 09:40:32 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 360 
2025-02-05 09:40:32 [35mSTATE:[39m test-backend-node-gpu.js start face similarity 
2025-02-05 09:40:32 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:32 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:32 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:32 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-body.jpg face similarity 
2025-02-05 09:40:32 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 1 gesture: 7 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.72,"class":"person"} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:32 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 344 
2025-02-05 09:40:32 [35mSTATE:[39m test-backend-node-gpu.js start face similarity 
2025-02-05 09:40:32 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-upper.jpg [1,720,688,3] {"checksum":151289024} 
2025-02-05 09:40:32 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:33 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:33 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-upper.jpg face similarity 
2025-02-05 09:40:33 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 0 gesture: 4 object: 1 person: 1 {"score":1,"age":23.5,"gender":"female"} {"score":0.71,"class":"person"} {"score":0.75,"keypoints":7} 
2025-02-05 09:40:33 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 331 
2025-02-05 09:40:33 [35mSTATE:[39m test-backend-node-gpu.js passed: face descriptor 
2025-02-05 09:40:33 [35mSTATE:[39m test-backend-node-gpu.js passed: face similarity {"similarity":[1,0.43,0.57],"descriptors":[1024,1024,1024]} 
2025-02-05 09:40:33 [36mINFO: [39m test-backend-node-gpu.js test object 
2025-02-05 09:40:33 [35mSTATE:[39m test-backend-node-gpu.js start object 
2025-02-05 09:40:33 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:33 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:33 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:33 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-body.jpg object 
2025-02-05 09:40:33 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 1 gesture: 7 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.72,"class":"person"} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:33 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 335 
2025-02-05 09:40:33 [35mSTATE:[39m test-backend-node-gpu.js passed: centernet 
2025-02-05 09:40:33 [35mSTATE:[39m test-backend-node-gpu.js start object 
2025-02-05 09:40:34 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:34 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:34 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:34 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-body.jpg object 
2025-02-05 09:40:34 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 1 gesture: 7 object: 3 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.86,"class":"person"} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:34 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 333 
2025-02-05 09:40:34 [35mSTATE:[39m test-backend-node-gpu.js passed: nanodet 
2025-02-05 09:40:34 [36mINFO: [39m test-backend-node-gpu.js test sensitive 
2025-02-05 09:40:34 [35mSTATE:[39m test-backend-node-gpu.js start sensitive 
2025-02-05 09:40:35 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:35 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:35 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:35 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-body.jpg sensitive 
2025-02-05 09:40:35 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 2 gesture: 9 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:35 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 306 
2025-02-05 09:40:35 [35mSTATE:[39m test-backend-node-gpu.js passed: sensitive result match 
2025-02-05 09:40:35 [35mSTATE:[39m test-backend-node-gpu.js passed: sensitive face result match 
2025-02-05 09:40:35 [35mSTATE:[39m test-backend-node-gpu.js passed: sensitive face emotion result [{"score":0.48,"emotion":"angry"},{"score":0.31,"emotion":"fear"},{"score":0.11,"emotion":"neutral"}] 
2025-02-05 09:40:35 [35mSTATE:[39m test-backend-node-gpu.js passed: sensitive body result match 
2025-02-05 09:40:35 [35mSTATE:[39m test-backend-node-gpu.js passed: sensitive hand result match 
2025-02-05 09:40:35 [36mINFO: [39m test-backend-node-gpu.js test body 
2025-02-05 09:40:35 [35mSTATE:[39m test-backend-node-gpu.js start blazepose 
2025-02-05 09:40:37 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:37 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:37 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:37 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-body.jpg blazepose 
2025-02-05 09:40:37 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 2 gesture: 9 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.99,"keypoints":39} 
2025-02-05 09:40:37 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 425 
2025-02-05 09:40:37 [35mSTATE:[39m test-backend-node-gpu.js passed: blazepose 
2025-02-05 09:40:37 [35mSTATE:[39m test-backend-node-gpu.js start efficientpose 
2025-02-05 09:40:38 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:38 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:38 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:38 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-body.jpg efficientpose 
2025-02-05 09:40:38 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 2 gesture: 9 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.75,"keypoints":13} 
2025-02-05 09:40:38 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 362 
2025-02-05 09:40:38 [35mSTATE:[39m test-backend-node-gpu.js passed: efficientpose 
2025-02-05 09:40:38 [35mSTATE:[39m test-backend-node-gpu.js start posenet 
2025-02-05 09:40:39 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:39 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:39 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:39 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-body.jpg posenet 
2025-02-05 09:40:39 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 2 gesture: 9 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.96,"keypoints":16} 
2025-02-05 09:40:39 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 291 
2025-02-05 09:40:39 [35mSTATE:[39m test-backend-node-gpu.js passed: posenet 
2025-02-05 09:40:39 [35mSTATE:[39m test-backend-node-gpu.js start movenet 
2025-02-05 09:40:39 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:39 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:40 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:40 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-body.jpg movenet 
2025-02-05 09:40:40 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 2 gesture: 9 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:40 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 291 
2025-02-05 09:40:40 [35mSTATE:[39m test-backend-node-gpu.js passed: movenet 
2025-02-05 09:40:40 [36mINFO: [39m test-backend-node-gpu.js test face matching 
2025-02-05 09:40:40 [35mSTATE:[39m test-backend-node-gpu.js passed: face database 40 
2025-02-05 09:40:40 [35mSTATE:[39m test-backend-node-gpu.js passed: face match {"first":{"index":4,"similarity":0.78}} {"second":{"index":4,"similarity":0.5}} {"third":{"index":4,"similarity":0.54}} 
2025-02-05 09:40:40 [36mINFO: [39m test-backend-node-gpu.js test face similarity alternative 
2025-02-05 09:40:40 [35mSTATE:[39m test-backend-node-gpu.js start face embeddings 
2025-02-05 09:40:40 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34696120} 
2025-02-05 09:40:40 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:41 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:41 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-face.jpg face embeddings 
2025-02-05 09:40:41 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 2 gesture: 8 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.47,"keypoints":3} 
2025-02-05 09:40:41 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 365 
2025-02-05 09:40:41 [35mSTATE:[39m test-backend-node-gpu.js passed: mobilefacenet {"embedding":192} 
2025-02-05 09:40:41 [35mSTATE:[39m test-backend-node-gpu.js start face embeddings 
2025-02-05 09:40:41 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34696120} 
2025-02-05 09:40:41 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:42 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:42 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-face.jpg face embeddings 
2025-02-05 09:40:42 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 2 gesture: 8 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.47,"keypoints":3} 
2025-02-05 09:40:42 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 353 
2025-02-05 09:40:42 [35mSTATE:[39m test-backend-node-gpu.js passed: insightface {"embedding":512} 
2025-02-05 09:40:42 [36mINFO: [39m test-backend-node-gpu.js test face attention 
2025-02-05 09:40:42 [35mSTATE:[39m test-backend-node-gpu.js start face attention 
2025-02-05 09:40:42 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34696120} 
2025-02-05 09:40:42 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:42 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:42 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-face.jpg face attention 
2025-02-05 09:40:42 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 2 gesture: 7 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.47,"keypoints":3} 
2025-02-05 09:40:42 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 349 
2025-02-05 09:40:42 [35mSTATE:[39m test-backend-node-gpu.js passed: face attention 
2025-02-05 09:40:42 [36mINFO: [39m test-backend-node-gpu.js test detectors 
2025-02-05 09:40:42 [35mSTATE:[39m test-backend-node-gpu.js start detectors 
2025-02-05 09:40:43 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:43 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:43 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:43 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-body.jpg detectors 
2025-02-05 09:40:43 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 1 gesture: 0 object: 0 person: 1 {"score":0.93,"gender":"unknown"} {} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:43 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 152 
2025-02-05 09:40:43 [35mSTATE:[39m test-backend-node-gpu.js passed: detector result face match 
2025-02-05 09:40:43 [35mSTATE:[39m test-backend-node-gpu.js passed: detector result hand match 
2025-02-05 09:40:43 [36mINFO: [39m test-backend-node-gpu.js test: multi-instance 
2025-02-05 09:40:43 [35mSTATE:[39m test-backend-node-gpu.js start multi instance 
2025-02-05 09:40:43 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:43 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:43 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: random multi instance 
2025-02-05 09:40:43 [32mDATA: [39m test-backend-node-gpu.js  result: face: 0 body: 1 hand: 0 gesture: 0 object: 0 person: 0 {} {} {"score":0,"keypoints":0} 
2025-02-05 09:40:43 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 144 
2025-02-05 09:40:43 [36mINFO: [39m test-backend-node-gpu.js test: first instance 
2025-02-05 09:40:43 [35mSTATE:[39m test-backend-node-gpu.js start multi instance 
2025-02-05 09:40:43 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-upper.jpg [1,720,688,3] {"checksum":151289024} 
2025-02-05 09:40:43 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-upper.jpg multi instance 
2025-02-05 09:40:43 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 0 gesture: 0 object: 0 person: 1 {"score":0.97,"gender":"unknown"} {} {"score":0.75,"keypoints":7} 
2025-02-05 09:40:43 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 144 
2025-02-05 09:40:43 [36mINFO: [39m test-backend-node-gpu.js test: second instance 
2025-02-05 09:40:43 [35mSTATE:[39m test-backend-node-gpu.js start multi instance 
2025-02-05 09:40:44 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-upper.jpg [1,720,688,3] {"checksum":151289024} 
2025-02-05 09:40:44 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-upper.jpg multi instance 
2025-02-05 09:40:44 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 0 gesture: 0 object: 0 person: 1 {"score":0.97,"gender":"unknown"} {} {"score":0.75,"keypoints":7} 
2025-02-05 09:40:44 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 148 
2025-02-05 09:40:44 [36mINFO: [39m test-backend-node-gpu.js test: concurrent 
2025-02-05 09:40:44 [35mSTATE:[39m test-backend-node-gpu.js start concurrent 
2025-02-05 09:40:44 [35mSTATE:[39m test-backend-node-gpu.js start concurrent 
2025-02-05 09:40:44 [35mSTATE:[39m test-backend-node-gpu.js start concurrent 
2025-02-05 09:40:44 [35mSTATE:[39m test-backend-node-gpu.js start concurrent 
2025-02-05 09:40:44 [35mSTATE:[39m test-backend-node-gpu.js start concurrent 
2025-02-05 09:40:44 [35mSTATE:[39m test-backend-node-gpu.js start concurrent 
2025-02-05 09:40:44 [35mSTATE:[39m test-backend-node-gpu.js start concurrent 
2025-02-05 09:40:44 [35mSTATE:[39m test-backend-node-gpu.js start concurrent 
2025-02-05 09:40:44 [35mSTATE:[39m test-backend-node-gpu.js start concurrent 
2025-02-05 09:40:44 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34696120} 
2025-02-05 09:40:44 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34696120} 
2025-02-05 09:40:44 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34696120} 
2025-02-05 09:40:44 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:44 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:45 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:45 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-upper.jpg [1,720,688,3] {"checksum":151289024} 
2025-02-05 09:40:45 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-upper.jpg [1,720,688,3] {"checksum":151289024} 
2025-02-05 09:40:45 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-upper.jpg [1,720,688,3] {"checksum":151289024} 
2025-02-05 09:40:45 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:45 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:45 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:46 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:46 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-upper.jpg concurrent 
2025-02-05 09:40:46 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 0 gesture: 0 object: 0 person: 1 {"score":0.97,"gender":"unknown"} {} {"score":0.75,"keypoints":7} 
2025-02-05 09:40:46 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 1257 
2025-02-05 09:40:46 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-upper.jpg concurrent 
2025-02-05 09:40:46 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 0 gesture: 0 object: 0 person: 1 {"score":0.97,"gender":"unknown"} {} {"score":0.75,"keypoints":7} 
2025-02-05 09:40:46 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 1257 
2025-02-05 09:40:46 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-upper.jpg concurrent 
2025-02-05 09:40:46 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 0 gesture: 0 object: 0 person: 1 {"score":0.97,"gender":"unknown"} {} {"score":0.75,"keypoints":7} 
2025-02-05 09:40:46 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 1257 
2025-02-05 09:40:46 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:46 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:46 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-face.jpg concurrent 
2025-02-05 09:40:46 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 1 gesture: 0 object: 0 person: 1 {"score":0.9,"gender":"unknown"} {} {"score":0.47,"keypoints":3} 
2025-02-05 09:40:46 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 1257 
2025-02-05 09:40:46 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-face.jpg concurrent 
2025-02-05 09:40:46 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 1 gesture: 0 object: 0 person: 1 {"score":0.9,"gender":"unknown"} {} {"score":0.47,"keypoints":3} 
2025-02-05 09:40:46 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 1257 
2025-02-05 09:40:46 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-face.jpg concurrent 
2025-02-05 09:40:46 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 1 gesture: 0 object: 0 person: 1 {"score":0.9,"gender":"unknown"} {} {"score":0.47,"keypoints":3} 
2025-02-05 09:40:46 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 1257 
2025-02-05 09:40:46 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-body.jpg concurrent 
2025-02-05 09:40:46 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 1 gesture: 0 object: 0 person: 1 {"score":0.93,"gender":"unknown"} {} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:46 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 1257 
2025-02-05 09:40:46 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-body.jpg concurrent 
2025-02-05 09:40:46 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 1 gesture: 0 object: 0 person: 1 {"score":0.93,"gender":"unknown"} {} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:46 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 1257 
2025-02-05 09:40:46 [35mSTATE:[39m test-backend-node-gpu.js passed: detect: samples/in/ai-body.jpg concurrent 
2025-02-05 09:40:46 [32mDATA: [39m test-backend-node-gpu.js  result: face: 1 body: 1 hand: 1 gesture: 0 object: 0 person: 1 {"score":0.93,"gender":"unknown"} {} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:46 [32mDATA: [39m test-backend-node-gpu.js  result: performance: load: null total: 1257 
2025-02-05 09:40:46 [36mINFO: [39m test-backend-node-gpu.js test: monkey-patch 
2025-02-05 09:40:46 [35mSTATE:[39m test-backend-node-gpu.js event: image 
2025-02-05 09:40:46 [35mSTATE:[39m test-backend-node-gpu.js event: detect 
2025-02-05 09:40:46 [35mSTATE:[39m test-backend-node-gpu.js passed: monkey patch 
2025-02-05 09:40:47 [35mSTATE:[39m test-backend-node-gpu.js passed: segmentation [262144] 
2025-02-05 09:40:47 [35mSTATE:[39m test-backend-node-gpu.js passeed: equal usage 
2025-02-05 09:40:47 [36mINFO: [39m test-backend-node-gpu.js test: input compare 
2025-02-05 09:40:47 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34696120} 
2025-02-05 09:40:47 [35mSTATE:[39m test-backend-node-gpu.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1004796864} 
2025-02-05 09:40:47 [35mSTATE:[39m test-backend-node-gpu.js passed: image compare 0 23.275441687091504 
2025-02-05 09:40:47 [36mINFO: [39m test-backend-node-gpu.js events: {"image":29,"detect":29,"warmup":2} 
2025-02-05 09:40:47 [36mINFO: [39m test-backend-node-gpu.js tensors 14855 
2025-02-05 09:40:47 [36mINFO: [39m test-backend-node-gpu.js test complete: 21990 ms 
2025-02-05 09:40:47 [36mINFO: [39m 
2025-02-05 09:40:47 [36mINFO: [39m test-backend-node-wasm.js start 
2025-02-05 09:40:48 [32mDATA: [39m test-backend-node-wasm.js stdout: 2025-02-05 09:40:48 [36mINFO: [39m { supported: [33mtrue[39m, backend: [33mtrue[39m, simd: [90mundefined[39m, multithread: [90mundefined[39m } https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-backend-wasm@4.22.0/dist/ 
2025-02-05 09:40:48 [35mSTATE:[39m test-backend-node-wasm.js passed: model server: https://vladmandic.github.io/human-models/models/ 
2025-02-05 09:40:48 [36mINFO: [39m test-backend-node-wasm.js test: configuration validation 
2025-02-05 09:40:48 [35mSTATE:[39m test-backend-node-wasm.js passed: configuration default validation [] 
2025-02-05 09:40:48 [35mSTATE:[39m test-backend-node-wasm.js passed: configuration invalid validation [{"reason":"unknown property","where":"config.invalid = true"}] 
2025-02-05 09:40:48 [36mINFO: [39m test-backend-node-wasm.js test: model load 
2025-02-05 09:40:49 [35mSTATE:[39m test-backend-node-wasm.js passed: models loaded 11 11 [{"name":"antispoof","loaded":true,"size":853098,"url":"https://vladmandic.github.io/human-models/models/antispoof.json"},{"name":"liveness","loaded":true,"size":592976,"url":"https://vladmandic.github.io/human-models/models/liveness.json"},{"name":"blazeface","loaded":true,"size":538928,"url":"https://vladmandic.github.io/human-models/models/blazeface.json"},{"name":"emotion","loaded":true,"size":820516,"url":"https://vladmandic.github.io/human-models/models/emotion.json"},{"name":"movenet","loaded":true,"size":4650216,"url":"https://vladmandic.github.io/human-models/models/movenet-lightning.json"},{"name":"facemesh","loaded":true,"size":1477958,"url":"https://vladmandic.github.io/human-models/models/facemesh.json"},{"name":"handskeleton","loaded":true,"size":0,"url":"https://vladmandic.github.io/human-models/models/handlandmark-lite.json"},{"name":"iris","loaded":true,"size":2599092,"url":"https://vladmandic.github.io/human-models/models/iris.json"},{"name":"handtrack","loaded":true,"size":2964837,"url":"https://vladmandic.github.io/human-models/models/handtrack.json"},{"name":"centernet","loaded":true,"size":4030290,"url":"https://vladmandic.github.io/human-models/models/centernet.json"},{"name":"faceres","loaded":true,"size":6978814,"url":"https://vladmandic.github.io/human-models/models/faceres.json"}] 
2025-02-05 09:40:49 [36mINFO: [39m test-backend-node-wasm.js memory: {"memory":{"unreliable":false,"numTensors":1785,"numDataBuffers":1785,"numBytes":56431460}} 
2025-02-05 09:40:49 [36mINFO: [39m test-backend-node-wasm.js state: {"state":{"registeredVariables":{},"nextTapeNodeId":0,"numBytes":56431460,"numTensors":1785,"numStringTensors":0,"numDataBuffers":1785,"gradientDepth":0,"kernelDepth":0,"scopeStack":[],"numDataMovesStack":[],"nextScopeId":0,"tensorInfo":{},"profiling":false,"activeProfile":{"newBytes":0,"newTensors":0,"peakBytes":0,"kernels":[],"result":null,"kernelNames":[]}}} 
2025-02-05 09:40:49 [36mINFO: [39m test-backend-node-wasm.js test: warmup 
2025-02-05 09:40:49 [35mSTATE:[39m test-backend-node-wasm.js passed: create human 
2025-02-05 09:40:49 [36mINFO: [39m test-backend-node-wasm.js human version: 3.3.5 
2025-02-05 09:40:49 [36mINFO: [39m test-backend-node-wasm.js platform:  agent:  
2025-02-05 09:40:49 [36mINFO: [39m test-backend-node-wasm.js tfjs version: 4.22.0 
2025-02-05 09:40:49 [36mINFO: [39m test-backend-node-wasm.js env: {"browser":false,"node":true,"platform":"","agent":"","backends":["wasm"],"initial":false,"tfjs":{"version":"4.22.0"},"offscreen":false,"perfadd":false,"tensorflow":{},"wasm":{"supported":true,"backend":true,"simd":true,"multithread":false},"webgl":{"supported":false,"backend":false},"webgpu":{"supported":false,"backend":false},"cpu":{"flags":[]},"kernels":174} 
2025-02-05 09:40:49 [35mSTATE:[39m test-backend-node-wasm.js passed: set backend: wasm 
2025-02-05 09:40:49 [35mSTATE:[39m test-backend-node-wasm.js tensors 1785 
2025-02-05 09:40:49 [35mSTATE:[39m test-backend-node-wasm.js  result: defined models: 11 loaded models: 11 
2025-02-05 09:40:49 [35mSTATE:[39m test-backend-node-wasm.js passed: load models 11 
2025-02-05 09:40:49 [35mSTATE:[39m test-backend-node-wasm.js passed: warmup: none default 
2025-02-05 09:40:49 [32mDATA: [39m test-backend-node-wasm.js  result: face: 0 body: 0 hand: 0 gesture: 0 object: 0 person: 0 {} {} {} 
2025-02-05 09:40:49 [32mDATA: [39m test-backend-node-wasm.js  result: performance: load: null total: null 
2025-02-05 09:40:49 [35mSTATE:[39m test-backend-node-wasm.js passed: warmup none result match 
2025-02-05 09:40:49 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:40:50 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:40:50 [35mSTATE:[39m test-backend-node-wasm.js event: warmup 
2025-02-05 09:40:50 [35mSTATE:[39m test-backend-node-wasm.js passed: warmup: face default 
2025-02-05 09:40:50 [32mDATA: [39m test-backend-node-wasm.js  result: face: 1 body: 1 hand: 1 gesture: 6 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.82,"class":"person"} {"score":0.47,"keypoints":3} 
2025-02-05 09:40:50 [32mDATA: [39m test-backend-node-wasm.js  result: performance: load: null total: 593 
2025-02-05 09:40:50 [35mSTATE:[39m test-backend-node-wasm.js passed: warmup face result match 
2025-02-05 09:40:50 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js event: warmup 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js passed: warmup: body default 
2025-02-05 09:40:51 [32mDATA: [39m test-backend-node-wasm.js  result: face: 1 body: 1 hand: 1 gesture: 7 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.72,"class":"person"} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:51 [32mDATA: [39m test-backend-node-wasm.js  result: performance: load: null total: 352 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js passed: warmup body result match 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js details: {"face":{"boxScore":0.93,"faceScore":1,"age":23.6,"gender":"female","genderScore":0.97},"emotion":[{"score":0.48,"emotion":"angry"},{"score":0.31,"emotion":"fear"},{"score":0.11,"emotion":"neutral"}],"body":{"score":0.92,"keypoints":17},"hand":{"boxScore":0.51,"fingerScore":0.73,"keypoints":21},"gestures":[{"face":0,"gesture":"facing right"},{"face":0,"gesture":"mouth 16% open"},{"hand":0,"gesture":"pinky forward"},{"hand":0,"gesture":"thumb up"},{"hand":0,"gesture":"open palm"},{"iris":0,"gesture":"looking left"},{"iris":0,"gesture":"looking up"}]} 
2025-02-05 09:40:51 [36mINFO: [39m test-backend-node-wasm.js test: details verification 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js start default 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1038921856} 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js passed: detect: samples/in/ai-body.jpg default 
2025-02-05 09:40:51 [32mDATA: [39m test-backend-node-wasm.js  result: face: 1 body: 1 hand: 1 gesture: 7 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.72,"class":"person"} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:51 [32mDATA: [39m test-backend-node-wasm.js  result: performance: load: null total: 334 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js passed: details face length 1 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js passed: details face score 1 0.93 1 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js passed: details face age/gender 23.6 female 0.97 2.34 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js passed: details face arrays 4 478 1024 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js passed: details face emotion 3 {"score":0.48,"emotion":"angry"} 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js passed: details face anti-spoofing 0.8 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js passed: details face liveness 0.93 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js passed: details body length 1 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js passed: details body 0.92 17 6 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js passed: details hand length 1 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js passed: details hand 0.51 0.73 point 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js passed: details hand arrays 21 5 7 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js passed: details gesture length 7 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js passed: details gesture first {"face":0,"gesture":"facing right"} 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js passed: details object length 1 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js passed: details object 0.72 person 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,4] {"checksum":1413675264} 
2025-02-05 09:40:51 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:40:52 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:40:52 [35mSTATE:[39m test-backend-node-wasm.js passed: tensor shape: [1,1200,1200,4] dtype: float32 
2025-02-05 09:40:52 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-body.jpg [1200,1200,4] {"checksum":1413675264} 
2025-02-05 09:40:52 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:40:52 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:40:52 [35mSTATE:[39m test-backend-node-wasm.js passed: tensor shape: [1200,1200,4] dtype: float32 
2025-02-05 09:40:53 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1038921856} 
2025-02-05 09:40:53 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:40:53 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:40:53 [35mSTATE:[39m test-backend-node-wasm.js passed: tensor shape: [1,1200,1200,3] dtype: float32 
2025-02-05 09:40:53 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-body.jpg [1200,1200,3] {"checksum":1038921856} 
2025-02-05 09:40:53 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:40:54 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:40:54 [35mSTATE:[39m test-backend-node-wasm.js passed: tensor shape: [1200,1200,3] dtype: float32 
2025-02-05 09:40:54 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,4] {"checksum":1371996871} 
2025-02-05 09:40:54 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:40:54 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:40:54 [35mSTATE:[39m test-backend-node-wasm.js passed: tensor shape: [1,1200,1200,4] dtype: int32 
2025-02-05 09:40:54 [36mINFO: [39m test-backend-node-wasm.js test default 
2025-02-05 09:40:54 [35mSTATE:[39m test-backend-node-wasm.js start async 
2025-02-05 09:40:56 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1038921856} 
2025-02-05 09:40:56 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:40:57 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:40:57 [35mSTATE:[39m test-backend-node-wasm.js passed: detect: samples/in/ai-body.jpg async 
2025-02-05 09:40:57 [32mDATA: [39m test-backend-node-wasm.js  result: face: 1 body: 1 hand: 1 gesture: 7 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.72,"class":"person"} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:57 [32mDATA: [39m test-backend-node-wasm.js  result: performance: load: null total: 411 
2025-02-05 09:40:57 [35mSTATE:[39m test-backend-node-wasm.js passed: default result face match 1 female 0.97 
2025-02-05 09:40:57 [36mINFO: [39m test-backend-node-wasm.js test sync 
2025-02-05 09:40:57 [35mSTATE:[39m test-backend-node-wasm.js start sync 
2025-02-05 09:40:59 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1038921856} 
2025-02-05 09:40:59 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:40:59 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:40:59 [35mSTATE:[39m test-backend-node-wasm.js passed: detect: samples/in/ai-body.jpg sync 
2025-02-05 09:40:59 [32mDATA: [39m test-backend-node-wasm.js  result: face: 1 body: 1 hand: 1 gesture: 7 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.72,"class":"person"} {"score":0.92,"keypoints":17} 
2025-02-05 09:40:59 [32mDATA: [39m test-backend-node-wasm.js  result: performance: load: null total: 422 
2025-02-05 09:40:59 [35mSTATE:[39m test-backend-node-wasm.js passed: default sync 1 female 0.97 
2025-02-05 09:40:59 [36mINFO: [39m test-backend-node-wasm.js test: image process 
2025-02-05 09:40:59 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34697856} 
2025-02-05 09:40:59 [35mSTATE:[39m test-backend-node-wasm.js passed: image input null [1,256,256,3] 
2025-02-05 09:40:59 [36mINFO: [39m test-backend-node-wasm.js test: image null 
2025-02-05 09:40:59 [35mSTATE:[39m test-backend-node-wasm.js passed: invalid input could not convert input to tensor 
2025-02-05 09:40:59 [36mINFO: [39m test-backend-node-wasm.js test face similarity 
2025-02-05 09:40:59 [35mSTATE:[39m test-backend-node-wasm.js start face similarity 
2025-02-05 09:41:00 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34697856} 
2025-02-05 09:41:00 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:41:01 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:41:01 [35mSTATE:[39m test-backend-node-wasm.js passed: detect: samples/in/ai-face.jpg face similarity 
2025-02-05 09:41:01 [32mDATA: [39m test-backend-node-wasm.js  result: face: 1 body: 1 hand: 1 gesture: 6 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.82,"class":"person"} {"score":0.47,"keypoints":3} 
2025-02-05 09:41:01 [32mDATA: [39m test-backend-node-wasm.js  result: performance: load: null total: 423 
2025-02-05 09:41:01 [35mSTATE:[39m test-backend-node-wasm.js start face similarity 
2025-02-05 09:41:01 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1038921856} 
2025-02-05 09:41:01 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:41:02 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:41:02 [35mSTATE:[39m test-backend-node-wasm.js passed: detect: samples/in/ai-body.jpg face similarity 
2025-02-05 09:41:02 [32mDATA: [39m test-backend-node-wasm.js  result: face: 1 body: 1 hand: 1 gesture: 7 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.72,"class":"person"} {"score":0.92,"keypoints":17} 
2025-02-05 09:41:02 [32mDATA: [39m test-backend-node-wasm.js  result: performance: load: null total: 343 
2025-02-05 09:41:02 [35mSTATE:[39m test-backend-node-wasm.js start face similarity 
2025-02-05 09:41:02 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-upper.jpg [1,720,688,3] {"checksum":151155104} 
2025-02-05 09:41:02 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:41:02 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:41:02 [35mSTATE:[39m test-backend-node-wasm.js passed: detect: samples/in/ai-upper.jpg face similarity 
2025-02-05 09:41:02 [32mDATA: [39m test-backend-node-wasm.js  result: face: 1 body: 1 hand: 0 gesture: 4 object: 1 person: 1 {"score":1,"age":23.5,"gender":"female"} {"score":0.71,"class":"person"} {"score":0.75,"keypoints":7} 
2025-02-05 09:41:02 [32mDATA: [39m test-backend-node-wasm.js  result: performance: load: null total: 280 
2025-02-05 09:41:02 [35mSTATE:[39m test-backend-node-wasm.js passed: face descriptor 
2025-02-05 09:41:02 [35mSTATE:[39m test-backend-node-wasm.js passed: face similarity {"similarity":[1,0.43,0.57],"descriptors":[1024,1024,1024]} 
2025-02-05 09:41:02 [36mINFO: [39m test-backend-node-wasm.js test object 
2025-02-05 09:41:02 [35mSTATE:[39m test-backend-node-wasm.js start object 
2025-02-05 09:41:02 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1038921856} 
2025-02-05 09:41:02 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:41:03 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:41:03 [35mSTATE:[39m test-backend-node-wasm.js passed: detect: samples/in/ai-body.jpg object 
2025-02-05 09:41:03 [32mDATA: [39m test-backend-node-wasm.js  result: face: 1 body: 1 hand: 1 gesture: 7 object: 1 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.72,"class":"person"} {"score":0.92,"keypoints":17} 
2025-02-05 09:41:03 [32mDATA: [39m test-backend-node-wasm.js  result: performance: load: null total: 323 
2025-02-05 09:41:03 [35mSTATE:[39m test-backend-node-wasm.js passed: centernet 
2025-02-05 09:41:03 [35mSTATE:[39m test-backend-node-wasm.js start object 
2025-02-05 09:41:04 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1038921856} 
2025-02-05 09:41:04 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:41:04 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:41:04 [35mSTATE:[39m test-backend-node-wasm.js passed: detect: samples/in/ai-body.jpg object 
2025-02-05 09:41:04 [32mDATA: [39m test-backend-node-wasm.js  result: face: 1 body: 1 hand: 1 gesture: 7 object: 3 person: 1 {"score":1,"age":23.6,"gender":"female"} {"score":0.86,"class":"person"} {"score":0.92,"keypoints":17} 
2025-02-05 09:41:04 [32mDATA: [39m test-backend-node-wasm.js  result: performance: load: null total: 349 
2025-02-05 09:41:04 [35mSTATE:[39m test-backend-node-wasm.js passed: nanodet 
2025-02-05 09:41:04 [36mINFO: [39m test-backend-node-wasm.js test sensitive 
2025-02-05 09:41:04 [35mSTATE:[39m test-backend-node-wasm.js start sensitive 
2025-02-05 09:41:05 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1038921856} 
2025-02-05 09:41:05 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:41:06 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:41:06 [35mSTATE:[39m test-backend-node-wasm.js passed: detect: samples/in/ai-body.jpg sensitive 
2025-02-05 09:41:06 [32mDATA: [39m test-backend-node-wasm.js  result: face: 1 body: 1 hand: 2 gesture: 9 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.92,"keypoints":17} 
2025-02-05 09:41:06 [32mDATA: [39m test-backend-node-wasm.js  result: performance: load: null total: 292 
2025-02-05 09:41:06 [35mSTATE:[39m test-backend-node-wasm.js passed: sensitive result match 
2025-02-05 09:41:06 [35mSTATE:[39m test-backend-node-wasm.js passed: sensitive face result match 
2025-02-05 09:41:06 [35mSTATE:[39m test-backend-node-wasm.js passed: sensitive face emotion result [{"score":0.48,"emotion":"angry"},{"score":0.31,"emotion":"fear"},{"score":0.11,"emotion":"neutral"}] 
2025-02-05 09:41:06 [35mSTATE:[39m test-backend-node-wasm.js passed: sensitive body result match 
2025-02-05 09:41:06 [35mSTATE:[39m test-backend-node-wasm.js passed: sensitive hand result match 
2025-02-05 09:41:06 [36mINFO: [39m test-backend-node-wasm.js test body 
2025-02-05 09:41:06 [35mSTATE:[39m test-backend-node-wasm.js start blazepose 
2025-02-05 09:41:09 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1038921856} 
2025-02-05 09:41:09 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:41:09 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:41:09 [35mSTATE:[39m test-backend-node-wasm.js passed: detect: samples/in/ai-body.jpg blazepose 
2025-02-05 09:41:09 [32mDATA: [39m test-backend-node-wasm.js  result: face: 1 body: 1 hand: 2 gesture: 9 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.99,"keypoints":39} 
2025-02-05 09:41:09 [32mDATA: [39m test-backend-node-wasm.js  result: performance: load: null total: 468 
2025-02-05 09:41:09 [35mSTATE:[39m test-backend-node-wasm.js passed: blazepose 
2025-02-05 09:41:09 [35mSTATE:[39m test-backend-node-wasm.js start efficientpose 
2025-02-05 09:41:10 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1038921856} 
2025-02-05 09:41:10 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:41:11 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:41:11 [35mSTATE:[39m test-backend-node-wasm.js passed: detect: samples/in/ai-body.jpg efficientpose 
2025-02-05 09:41:11 [32mDATA: [39m test-backend-node-wasm.js  result: face: 1 body: 1 hand: 2 gesture: 9 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.75,"keypoints":13} 
2025-02-05 09:41:11 [32mDATA: [39m test-backend-node-wasm.js  result: performance: load: null total: 637 
2025-02-05 09:41:11 [35mSTATE:[39m test-backend-node-wasm.js passed: efficientpose 
2025-02-05 09:41:11 [35mSTATE:[39m test-backend-node-wasm.js start posenet 
2025-02-05 09:41:12 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1038921856} 
2025-02-05 09:41:12 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:41:12 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:41:12 [35mSTATE:[39m test-backend-node-wasm.js passed: detect: samples/in/ai-body.jpg posenet 
2025-02-05 09:41:12 [32mDATA: [39m test-backend-node-wasm.js  result: face: 1 body: 1 hand: 2 gesture: 9 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.96,"keypoints":16} 
2025-02-05 09:41:12 [32mDATA: [39m test-backend-node-wasm.js  result: performance: load: null total: 264 
2025-02-05 09:41:12 [35mSTATE:[39m test-backend-node-wasm.js passed: posenet 
2025-02-05 09:41:12 [35mSTATE:[39m test-backend-node-wasm.js start movenet 
2025-02-05 09:41:13 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-body.jpg [1,1200,1200,3] {"checksum":1038921856} 
2025-02-05 09:41:13 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:41:13 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:41:13 [35mSTATE:[39m test-backend-node-wasm.js passed: detect: samples/in/ai-body.jpg movenet 
2025-02-05 09:41:13 [32mDATA: [39m test-backend-node-wasm.js  result: face: 1 body: 1 hand: 2 gesture: 9 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.92,"keypoints":17} 
2025-02-05 09:41:13 [32mDATA: [39m test-backend-node-wasm.js  result: performance: load: null total: 234 
2025-02-05 09:41:13 [35mSTATE:[39m test-backend-node-wasm.js passed: movenet 
2025-02-05 09:41:13 [36mINFO: [39m test-backend-node-wasm.js test face matching 
2025-02-05 09:41:13 [35mSTATE:[39m test-backend-node-wasm.js passed: face database 40 
2025-02-05 09:41:13 [35mSTATE:[39m test-backend-node-wasm.js passed: face match {"first":{"index":4,"similarity":0.78}} {"second":{"index":4,"similarity":0.5}} {"third":{"index":4,"similarity":0.54}} 
2025-02-05 09:41:13 [36mINFO: [39m test-backend-node-wasm.js test face similarity alternative 
2025-02-05 09:41:13 [35mSTATE:[39m test-backend-node-wasm.js start face embeddings 
2025-02-05 09:41:14 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34697856} 
2025-02-05 09:41:14 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:41:15 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:41:15 [35mSTATE:[39m test-backend-node-wasm.js passed: detect: samples/in/ai-face.jpg face embeddings 
2025-02-05 09:41:15 [32mDATA: [39m test-backend-node-wasm.js  result: face: 1 body: 1 hand: 2 gesture: 8 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.47,"keypoints":3} 
2025-02-05 09:41:15 [32mDATA: [39m test-backend-node-wasm.js  result: performance: load: null total: 340 
2025-02-05 09:41:15 [35mSTATE:[39m test-backend-node-wasm.js passed: mobilefacenet {"embedding":192} 
2025-02-05 09:41:15 [35mSTATE:[39m test-backend-node-wasm.js start face embeddings 
2025-02-05 09:41:16 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34697856} 
2025-02-05 09:41:16 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:41:17 [35mSTATE:[39m test-backend-node-wasm.js event: detect 
2025-02-05 09:41:17 [35mSTATE:[39m test-backend-node-wasm.js passed: detect: samples/in/ai-face.jpg face embeddings 
2025-02-05 09:41:17 [32mDATA: [39m test-backend-node-wasm.js  result: face: 1 body: 1 hand: 2 gesture: 8 object: 0 person: 1 {"score":1,"age":23.6,"gender":"female"} {} {"score":0.47,"keypoints":3} 
2025-02-05 09:41:17 [32mDATA: [39m test-backend-node-wasm.js  result: performance: load: null total: 309 
2025-02-05 09:41:17 [35mSTATE:[39m test-backend-node-wasm.js passed: insightface {"embedding":512} 
2025-02-05 09:41:17 [36mINFO: [39m test-backend-node-wasm.js test face attention 
2025-02-05 09:41:17 [35mSTATE:[39m test-backend-node-wasm.js start face attention 
2025-02-05 09:41:17 [35mSTATE:[39m test-backend-node-wasm.js passed: load image: samples/in/ai-face.jpg [1,256,256,3] {"checksum":34697856} 
2025-02-05 09:41:17 [35mSTATE:[39m test-backend-node-wasm.js event: image 
2025-02-05 09:41:17 [31mERROR:[39m test-backend-node-wasm.js failed: testDetect face attention 
2025-02-05 09:41:17 [31mERROR:[39m test-backend-node-wasm.js uncaughtException {"name":"Error","message":"BatchMatMul for non non-float32 tensors not yet supported.","stack":["Error: BatchMatMul for non non-float32 tensors not yet supported.","    at Object.batchMatMul [as kernelFunc] (/home/<USER>/dev/human/node_modules/.pnpm/@tensorflow+tfjs-backend-wasm@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-backend-wasm/dist/tf-backend-wasm.node.js:1280:15)","    at kernelFunc (/home/<USER>/dev/human/node_modules/.pnpm/@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-core/dist/tf-core.node.js:4707:32)","    at /home/<USER>/dev/human/node_modules/.pnpm/@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-core/dist/tf-core.node.js:4767:27","    at Engine.scopedRun (/home/<USER>/dev/human/node_modules/.pnpm/@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-core/dist/tf-core.node.js:4572:23)","    at Engine.runKernelFunc (/home/<USER>/dev/human/node_modules/.pnpm/@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-core/dist/tf-core.node.js:4763:14)","    at Engine.runKernel (/home/<USER>/dev/human/node_modules/.pnpm/@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-core/dist/tf-core.node.js:4636:21)","    at matMul_ (/home/<USER>/dev/human/node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/tf-converter.node.js:13414:19)","    at Object.matMul__op [as matMul] (/home/<USER>/dev/human/node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/tf-converter.node.js:12223:29)","    at executeOp$9 (/home/<USER>/dev/human/node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/tf-converter.node.js:29619:25)","    at /home/<USER>/dev/human/node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/tf-converter.node.js:30199:50"]} 
2025-02-05 09:41:17 [35mSTATE:[39m all tests complete 
2025-02-05 09:41:17 [36mINFO: [39m   status {"test":"../demo/nodejs/node.js","passed":1,"failed":0} 
2025-02-05 09:41:17 [36mINFO: [39m   status {"test":"../demo/nodejs/node-simple.js","passed":1,"failed":0} 
2025-02-05 09:41:17 [36mINFO: [39m   status {"test":"../demo/nodejs/node-event.js","passed":1,"failed":0} 
2025-02-05 09:41:17 [36mINFO: [39m   status {"test":"../demo/nodejs/node-similarity.js","passed":1,"failed":0} 
2025-02-05 09:41:17 [36mINFO: [39m   status {"test":"../demo/nodejs/node-canvas.js","passed":1,"failed":0} 
2025-02-05 09:41:17 [36mINFO: [39m   status {"test":"../demo/nodejs/process-folder.js","passed":1,"failed":0} 
2025-02-05 09:41:17 [36mINFO: [39m   status {"test":"../demo/multithread/node-multiprocess.js","passed":1,"failed":0} 
2025-02-05 09:41:17 [36mINFO: [39m   status {"test":"../demo/facematch/node-match.js","passed":1,"failed":0} 
2025-02-05 09:41:17 [36mINFO: [39m   status {"test":"../demo/nodejs/node-bench.js","passed":1,"failed":0} 
2025-02-05 09:41:17 [36mINFO: [39m   status {"test":"../test/test-node-emotion.js","passed":1,"failed":0} 
2025-02-05 09:41:17 [36mINFO: [39m   status {"test":"test-node-load.js","passed":1,"failed":0} 
2025-02-05 09:41:17 [36mINFO: [39m   status {"test":"test-node-gear.js","passed":4,"failed":0} 
2025-02-05 09:41:17 [36mINFO: [39m   status {"test":"test-backend-node.js","passed":125,"failed":0} 
2025-02-05 09:41:17 [36mINFO: [39m   status {"test":"test-backend-node-gpu.js","passed":125,"failed":0} 
2025-02-05 09:41:17 [36mINFO: [39m   status {"test":"test-backend-node-wasm.js","passed":92,"failed":1} 
2025-02-05 09:41:17 [36mINFO: [39m failures {"count":1} 
2025-02-05 09:41:17 [33mWARN: [39m   failed {"test":"test-backend-node-wasm.js","message":["error",["failed:","testDetect face attention"]]} 
