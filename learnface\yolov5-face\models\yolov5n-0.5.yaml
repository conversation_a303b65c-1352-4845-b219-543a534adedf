# parameters
nc: 1  # number of classes
depth_multiple: 1.0  # model depth multiple
width_multiple: 0.5  # layer channel multiple

# anchors
anchors:
  - [4,5,  8,10,  13,16]  # P3/8
  - [23,29,  43,55,  73,105]  # P4/16
  - [146,217,  231,300,  335,433]  # P5/32

# YOLOv5 backbone
backbone:
  # [from, number, module, args]
  [[-1, 1, <PERSON><PERSON><PERSON><PERSON>, [32, 3, 2]],    # 0-P2/4
   [-1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [128, 2]], # 1-P3/8
   [-1, 3, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [128, 1]], # 2
   [-1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [256, 2]], # 3-P4/16
   [-1, 7, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [256, 1]], # 4
   [-1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [512, 2]], # 5-P5/32
   [-1, 3, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [512, 1]], # 6
  ]

# YOLOv5 head
head:
  [[-1, 1, Conv, [128, 1, 1]],
   [-1, 1, nn.<PERSON><PERSON><PERSON>, [None, 2, 'nearest']],
   [[-1, 4], 1, <PERSON><PERSON>, [1]],  # cat backbone P4
   [-1, 1, C3, [128, <PERSON>alse]],  # 10

   [-1, 1, Conv, [128, 1, 1]],
   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [[-1, 2], 1, Concat, [1]],  # cat backbone P3
   [-1, 1, C3, [128, False]],  # 14 (P3/8-small)

   [-1, 1, Conv, [128, 3, 2]],
   [[-1, 11], 1, Concat, [1]],  # cat head P4
   [-1, 1, C3, [128, False]],  # 17 (P4/16-medium)

   [-1, 1, Conv, [128, 3, 2]],
   [[-1, 7], 1, Concat, [1]],  # cat head P5
   [-1, 1, C3, [128, False]],  # 20 (P5/32-large)

   [[14, 17, 20], 1, Detect, [nc, anchors]],  # Detect(P3, P4, P5)
  ]
          
