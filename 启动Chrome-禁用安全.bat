@echo off
chcp 65001
echo ⚠️  临时禁用Chrome安全策略启动
echo.
echo 🔒 安全提醒:
echo 此方法会临时禁用Chrome的安全策略
echo 仅用于本地开发测试，请勿用于日常浏览
echo.
echo 🚀 正在启动Chrome...

set CHROME_PATH=""

:: 查找Chrome安装路径
if exist "C:\Program Files\Google\Chrome\Application\chrome.exe" (
    set CHROME_PATH="C:\Program Files\Google\Chrome\Application\chrome.exe"
) else if exist "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" (
    set CHROME_PATH="C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
) else if exist "%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe" (
    set CHROME_PATH="%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe"
) else (
    echo ❌ 未找到Chrome浏览器
    echo 请手动安装Chrome或使用其他解决方案
    pause
    exit /b 1
)

:: 创建临时用户数据目录
set TEMP_DIR=%TEMP%\chrome_temp_%RANDOM%
mkdir "%TEMP_DIR%"

:: 启动Chrome并禁用安全策略
%CHROME_PATH% --disable-web-security --disable-features=VizDisplayCompositor --user-data-dir="%TEMP_DIR%" --allow-file-access-from-files "file:///%~dp0start.html"

:: 清理临时目录
timeout /t 2 /nobreak >nul
rmdir /s /q "%TEMP_DIR%" 2>nul

echo.
echo ✅ Chrome已关闭，临时文件已清理
pause
