# 贡献指南

感谢您对 GejieSoft PDF Tools 项目的关注和贡献！这份指南将帮助您了解如何参与项目开发。

## 📋 目录

- [开发环境搭建](#开发环境搭建)
- [项目结构说明](#项目结构说明)
- [开发流程](#开发流程)
- [代码规范](#代码规范)
- [提交规范](#提交规范)
- [测试指南](#测试指南)
- [文档贡献](#文档贡献)
- [问题反馈](#问题反馈)

## 🛠️ 开发环境搭建

### 环境要求

- **Go**: 1.24+
- **Python**: 3.8+
- **Git**: 最新版本
- **IDE**: 推荐 VS Code 或 GoLand

### 快速开始

1. **Fork 项目**
   ```bash
   # 通过GitHub界面Fork项目到您的账户
   git clone https://github.com/your-username/gejiesoft-pdf-tools.git
   cd gejiesoft-pdf-tools
   ```

2. **设置上游仓库**
   ```bash
   git remote add upstream https://github.com/original-owner/gejiesoft-pdf-tools.git
   ```

3. **环境配置**
   ```bash
   # Linux/macOS
   ./deploy_linux.sh
   
   # Windows
   .\test_env_local.bat
   ```

4. **验证环境**
   ```bash
   go version
   python3 --version
   go mod tidy
   ```

## 🏗️ 项目结构说明

```
gejiesoft-pdf-tools/
├── main/                    # 核心Go代码
│   ├── *.go                # 各功能模块
│   └── ppt_generator/      # Python PPT生成模块
├── frontend/               # 前端资源
├── docs/                   # 项目文档
├── tests/                  # 测试文件
└── scripts/                # 部署和构建脚本
```

### 关键模块说明

- **main.go**: 程序入口，负责启动Web服务器
- **web_server.go**: HTTP服务器实现
- **image_processor.go**: 图片处理核心逻辑
- **post_aibot_api.go**: AI API调用模块
- **config.go**: 配置管理

## 🔄 开发流程

### 1. 创建功能分支

```bash
git checkout -b feature/your-feature-name
```

### 2. 开发和测试

```bash
# 运行开发服务器
go run main/main.go

# 运行测试
go test ./...

# 代码格式化
gofmt -w .
```

### 3. 提交更改

```bash
git add .
git commit -m "feat: 添加新功能描述"
```

### 4. 推送和PR

```bash
git push origin feature/your-feature-name
# 通过GitHub界面创建Pull Request
```

## 📝 代码规范

### Go 代码规范

1. **命名规范**
   - 包名：小写，简洁明了
   - 函数名：驼峰命名，导出函数首字母大写
   - 变量名：驼峰命名，简洁明了

2. **注释规范**
   ```go
   // ProcessImage 处理图片并返回处理结果
   // 参数：imagePath - 图片路径
   // 返回：处理结果和错误信息
   func ProcessImage(imagePath string) (*ProcessResult, error) {
       // ...
   }
   ```

3. **错误处理**
   ```go
   if err != nil {
       log.Printf("处理图片失败: %v", err)
       return nil, fmt.Errorf("图片处理失败: %w", err)
   }
   ```

### Python 代码规范

1. **PEP 8 标准**
   - 使用 4 个空格缩进
   - 行长度不超过 79 字符
   - 函数和类之间空 2 行

2. **类型注解**
   ```python
   def generate_ppt(data: dict, template_path: str) -> str:
       """生成PPT文件"""
       pass
   ```

## 📤 提交规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>(<scope>): <description>

[optional body]

[optional footer]
```

### 提交类型

- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 重构代码
- `test`: 添加测试
- `chore`: 构建过程或辅助工具的变动

### 示例

```bash
feat(image): 添加图片批量处理功能
fix(api): 修复API调用超时问题
docs(readme): 更新部署文档
```

## 🧪 测试指南

### 单元测试

```bash
# 运行所有测试
go test ./...

# 运行指定包测试
go test ./main

# 生成测试覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

### 集成测试

```bash
# 本地环境测试
./test_env_local.bat  # Windows
./test_local.sh       # Linux

# Docker环境测试
docker-compose -f docker-compose.test.yml up
```

### 测试用例编写

```go
func TestProcessImage(t *testing.T) {
    // 准备测试数据
    testImagePath := "test_data/sample.jpg"
    
    // 执行测试
    result, err := ProcessImage(testImagePath)
    
    // 验证结果
    assert.NoError(t, err)
    assert.NotNil(t, result)
    assert.Equal(t, "success", result.Status)
}
```

## 📖 文档贡献

### 文档类型

1. **API文档**: 使用godoc格式
2. **用户文档**: Markdown格式
3. **开发文档**: 详细的技术说明

### 文档更新流程

1. 修改相关文档文件
2. 确保示例代码可以运行
3. 检查链接和格式
4. 提交PR并请求审核

## 🐛 问题反馈

### 报告Bug

请使用GitHub Issues，包含以下信息：

1. **环境信息**
   - 操作系统
   - Go版本
   - Python版本

2. **问题描述**
   - 详细的错误信息
   - 复现步骤
   - 期望结果

3. **日志信息**
   - 相关的日志输出
   - 错误堆栈信息

### 功能请求

1. 描述需求场景
2. 提供具体的功能说明
3. 说明预期的用户体验

## 🔄 发布流程

### 版本发布

1. **更新版本号**
   ```bash
   # 更新版本信息
   git tag -a v1.0.0 -m "Release version 1.0.0"
   ```

2. **生成变更日志**
   ```bash
   # 生成CHANGELOG.md
   git log --oneline --since="2023-01-01" > CHANGELOG.md
   ```

3. **构建发布包**
   ```bash
   # Windows
   .\build_standalone.bat
   
   # Linux
   ./build_release.sh
   ```

## 🤝 社区行为准则

我们致力于为所有人创造一个开放和受欢迎的环境。请遵循以下准则：

- 使用友好和包容的语言
- 尊重不同的观点和经验
- 优雅地接受建设性的批评
- 关注对社区最有利的事情
- 对其他社区成员表示同理心

## 📞 联系方式

- **项目主页**: https://github.com/your-org/gejiesoft-pdf-tools
- **文档网站**: https://docs.gejiesoft.com
- **问题反馈**: https://github.com/your-org/gejiesoft-pdf-tools/issues
- **邮件联系**: <EMAIL>

---

感谢您的贡献！🎉 