package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// APIRequest 按照用户提供的格式定义请求结构
type APIRequest struct {
	Inputs         map[string]interface{} `json:"inputs"`
	Query          string                 `json:"query"`
	ResponseMode   string                 `json:"response_mode"`
	ConversationID string                 `json:"conversation_id"`
	User           string                 `json:"user"`
	Files          []FileInfo             `json:"files"`
}

// APIResponse API响应结构
type APIResponse struct {
	Event          string `json:"event"`
	TaskID         string `json:"task_id"`
	ID             string `json:"id"`
	MessageID      string `json:"message_id"`
	ConversationID string `json:"conversation_id"`
	Mode           string `json:"mode"`
	Answer         string `json:"answer"`
	Metadata       struct {
		Usage struct {
			TotalTokens int    `json:"total_tokens"`
			TotalPrice  string `json:"total_price"`
			Currency    string `json:"currency"`
		} `json:"usage"`
	} `json:"metadata"`
	CreatedAt int64 `json:"created_at"`
}

// callAibotAPI 调用自定义API接口
func callAibotAPI(query string) (string, error) {
	fmt.Println("\n--- 调用自定义API接口 ---")

	// 构造请求数据
	request := APIRequest{
		Inputs:         map[string]interface{}{},
		Query:          query,
		ResponseMode:   "blocking", // 改为blocking模式，避免流式响应
		ConversationID: "",
		User:           "GejieSoft-PDF-Tools",
		Files:          []FileInfo{}, // 暂时不传文件
	}

	// 序列化请求数据
	jsonData, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("序列化请求数据失败: %v", err)
	}

	fmt.Printf("请求URL: %s\n", "https://aibot.gejiesoft.com/v1/chat-messages")
	fmt.Printf("请求数据: %s\n", string(jsonData))

	// 增加超时时间到60秒，并添加重试机制
	maxRetries := 3
	var lastErr error

	for attempt := 1; attempt <= maxRetries; attempt++ {
		fmt.Printf("尝试第 %d 次请求...\n", attempt)

		// 在每次重试时重新创建请求体，因为前一次尝试可能已消耗
		req, err := http.NewRequest("POST", "https://aibot.gejiesoft.com/v1/chat-messages", bytes.NewBuffer(jsonData))
		if err != nil {
			lastErr = fmt.Errorf("第%d次请求：重新创建HTTP请求失败: %v", attempt, err)
			fmt.Printf("请求失败: %v\n", lastErr)
			return "", lastErr // 如果重新创建请求也失败，直接返回错误
		}

		// 重新设置请求头
		req.Header.Set("Authorization", "Bearer app-evmRqSksgUKuO8tGw2IHt5H0")
		req.Header.Set("Content-Type", "application/json")

		// 发送请求，增加超时时间到60秒
		client := &http.Client{Timeout: 60 * time.Second}
		resp, err := client.Do(req)
		if err != nil {
			lastErr = fmt.Errorf("第%d次请求失败: %v", attempt, err)
			fmt.Printf("请求失败: %v\n", lastErr)

			if attempt < maxRetries {
				fmt.Printf("等待3秒后重试...\n")
				time.Sleep(3 * time.Second)
				continue
			}
			return "", fmt.Errorf("发送HTTP请求失败(已重试%d次): %v", maxRetries, err)
		}
		defer resp.Body.Close()

		// 读取响应
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return "", fmt.Errorf("读取响应失败: %v", err)
		}

		fmt.Printf("响应状态码: %d\n", resp.StatusCode)
		fmt.Printf("响应内容:\n%s\n", string(body))

		// 尝试解析JSON响应
		if resp.StatusCode == 200 {
			var apiResp APIResponse
			if err := json.Unmarshal(body, &apiResp); err != nil {
				return "", fmt.Errorf("解析JSON响应失败: %v", err)
			}

			// 检查是否有answer字段
			if apiResp.Answer != "" {
				// 从answer字段中提取JSON内容，去除```json ... ```包裹
				answer := strings.TrimSpace(apiResp.Answer)
				if strings.HasPrefix(answer, "```json") {
					answer = strings.TrimPrefix(answer, "```json")
				}
				if strings.HasSuffix(answer, "```") {
					answer = strings.TrimSuffix(answer, "```")
				}
				answer = strings.TrimSpace(answer)

				return answer, nil
			} else {
				return "", fmt.Errorf("API响应中没有answer字段")
			}
		} else {
			return "", fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
		}
	}

	return "", lastErr
}

// extractResumeInfo 提取简历信息的专门函数
func extractResumeInfo(textContent string) (*ResumeInfo, error) {
	fmt.Println("\n--- 开始提取简历信息 ---")

	// 构造专门的简历信息提取提示词
	prompt := fmt.Sprintf(`请从以下简历文本中提取结构化的信息，按照指定的JSON格式返回：

简历文本：
%s

请按照以下格式提取信息，如果某项信息不存在，请将对应字段设为空字符串：

{
  "姓名": "提取的姓名",
  "医院": "从输入内容中提取所有医院名称。若医院名称结构较为复杂（例如：“上海交通大学医学院附属第六人民医院”），请在输出时使用换行符对其进行分段展示，例如(上海交通大学医学院\n附属第六人民医院)，以提高可读性。",
  "科室": "提取的科室名称",
  "职称": "提取医生的职称，在中国，医生职称主要分为四个等级，由低到高分别是：初级职称(医士、医师/住院医师), 中级职称(主治医师), 副高级职称(副主任医师), 和正高级职称(主任医师)。",
  "介绍": "提取医院、科室、职务职称、学术任职、专业擅长、学术成果（如没有提取到内容就不显示），返回带换行符的列表",
  "职务职称": "单独提取的详细职务职称信息（可能包含多行）",
  "学术任职": "单独提取的学术任职信息（可能包含多行）",
  "专业擅长": "单独提取的专业擅长领域",
  "学术成果": "单独提取的学术成果信息（可能包含多行）",
  "其他": "单独其他无法归为以上分类的信息"
}

注意：
1. 请确保提取的信息准确完整
2. 对于多行信息，请保持原有的换行格式
3. 如果某项信息不存在，请设为空字符串而不是null
4. 请只返回JSON格式的数据，不要包含其他说明文字`, textContent)

	// 调用AI API
	response, err := callAibotAPI(prompt)
	if err != nil {
		return nil, fmt.Errorf("调用AI API提取简历信息失败: %v", err)
	}

	// 解析返回的JSON
	var resumeInfo ResumeInfo
	if err := json.Unmarshal([]byte(response), &resumeInfo); err != nil {
		return nil, fmt.Errorf("解析简历信息JSON失败: %v", err)
	}

	// 处理多行信息，将其分割为数组
	if resumeInfo.Position != "" {
		resumeInfo.Positions = splitMultiLineText(resumeInfo.Position)
	}
	if resumeInfo.AcademicTitle != "" {
		resumeInfo.AcademicTitles = splitMultiLineText(resumeInfo.AcademicTitle)
	}
	if resumeInfo.AcademicOutput != "" {
		resumeInfo.AcademicOutputs = splitMultiLineText(resumeInfo.AcademicOutput)
	}

	fmt.Printf("✅ 简历信息提取完成\n")
	fmt.Printf("姓名: %s\n", resumeInfo.Name)
	fmt.Printf("医院: %s\n", resumeInfo.Hospital)
	fmt.Printf("科室: %s\n", resumeInfo.Department)
	fmt.Printf("职称: %s\n", resumeInfo.Title)

	return &resumeInfo, nil
}

// splitMultiLineText 将多行文本分割为字符串数组 (如果不再被调用，可以考虑删除此函数)
func splitMultiLineText(text string) []string {
	if text == "" {
		return []string{}
	}

	// 按换行符分割
	lines := strings.Split(text, "\n")

	// 过滤空行并去除首尾空格
	var result []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			result = append(result, line)
		}
	}

	return result
}
