{"$schema": "https://developer.microsoft.com/json-schemas/api-extractor/v7/api-extractor.schema.json", "mainEntryPointFilePath": "types/lib/src/human.d.ts", "compiler": {"skipLibCheck": true}, "newlineKind": "lf", "dtsRollup": {"enabled": true, "untrimmedFilePath": "types/human.d.ts"}, "docModel": {"enabled": false}, "tsdocMetadata": {"enabled": false}, "apiReport": {"enabled": false}, "messages": {"compilerMessageReporting": {"default": {"logLevel": "warning"}}, "extractorMessageReporting": {"default": {"logLevel": "warning"}, "ae-missing-release-tag": {"logLevel": "none"}}, "tsdocMessageReporting": {"default": {"logLevel": "warning"}}}}