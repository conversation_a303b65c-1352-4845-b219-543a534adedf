



**已知问题：**

1、安装好模块以后，需要给 用户 设置以下3个权限，用户才可以正常访问个人资料

员工：  主管：管理所有员工

出勤：  主管：管理所有出勤

车队 / 管理员

2、用户的人脸识别，和员工的人脸识别，需要分别设置

员工人脸识别，对应 kiosk



**临时解决办法：安装好以后还需要进一步配置**

修复思路，人脸识别模块三个权限，要分别获取其他权限

HR PRO：user

HR PRO：Mamanger

HR PRO：Admin





1、再以上三个权限上复制出来，3个新的权限，并改原权限名称为新如下，

员工：  主管：管理所有员工 plus+

出勤：  主管：管理所有出勤 plus+

车队 / 管理员 plus+



2、再缩小旧的权限，取消一些非管理员的菜单访问权限

员工：  主管：管理所有员工

出勤：  主管：管理所有出勤

车队 / 管理员



3、给 HR PRO：Mamanger 添加继承以下权限

员工：  主管：管理所有员工

出勤：  主管：管理所有出勤

车队 / 管理员



4、给 HR PRO：user 添加继承以下权限



主管：管理所有员工 plus+

员工：  主管：管理所有员工 plus+

出勤：  主管：管理所有出勤 plus+



**待修复源代码权限配置**
