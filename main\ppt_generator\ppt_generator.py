import argparse
import json
import os
from pptx import Presentation
from pptx.util import Inches, Pt
from datetime import datetime
from pptx.enum.shapes import PP_PLACEHOLDER
from pptx.enum.text import MSO_AUTO_SIZE
import sys 

def auto_fit_text_in_shape(text_frame, text, max_font_size=18, min_font_size=8):
    """
    自动调整文字大小以适应文本框，确保字体大小一致和适当行间距
    """
    if not text.strip():
        return
        
    # 清除现有内容
    text_frame.clear()
    
    # 根据文本长度估算合适的字体大小
    text_length = len(text)
    
    if text_length <= 100:
        font_size = max_font_size
    elif text_length <= 200:
        font_size = max_font_size - 2
    elif text_length <= 400:
        font_size = max_font_size - 4  
    elif text_length <= 600:
        font_size = max_font_size - 6
    else:
        font_size = min_font_size
    
    # 确保字体大小在合理范围内
    font_size = max(min_font_size, min(max_font_size, font_size))
    
    # 按行分割文本
    lines = text.split('\n')
    
    # 处理每一行
    for i, line in enumerate(lines):
        if i == 0:
            # 第一行使用现有段落
            paragraph = text_frame.paragraphs[0]
        else:
            # 其他行添加新段落
            paragraph = text_frame.add_paragraph()
        
        # 设置段落文本
        paragraph.text = line
        
        # 设置行间距（1.2倍行距）
        paragraph.line_spacing = 1.2
        
        # 设置所有run的字体大小
        for run in paragraph.runs:
            run.font.size = Pt(font_size)
    
    print(f"DEBUG: 统一设置字体大小为 {font_size}pt，共 {len(lines)} 行，行间距 1.2", file=sys.stderr)

def manual_fit_text(paragraph, text, max_font_size, min_font_size):
    """
    手动调整文字大小（保留用于向后兼容）
    """
    # 根据文本长度估算合适的字体大小
    text_length = len(text)
    
    if text_length <= 100:
        font_size = max_font_size
    elif text_length <= 200:
        font_size = max_font_size - 2
    elif text_length <= 400:
        font_size = max_font_size - 4
    elif text_length <= 600:
        font_size = max_font_size - 6
    else:
        font_size = min_font_size
    
    # 确保字体大小在合理范围内
    font_size = max(min_font_size, min(max_font_size, font_size))
    
    # 应用字体大小
    run = paragraph.runs[0] if paragraph.runs else None
    if run:
        run.font.size = Pt(font_size)
        print(f"DEBUG: 设置字体大小为 {font_size}pt (文本长度: {text_length})", file=sys.stderr)

def fill_text_placeholder(text_frame, text, is_long_text=False):
    """
    填充文本占位符，支持长文本的自动调整
    """
    if is_long_text:
        # 对长文本使用自动适应
        auto_fit_text_in_shape(text_frame, text, max_font_size=16, min_font_size=8)
    else:
        # 短文本直接填充
        text_frame.text = text

def generate_ppt(json_data_path, template_ppt_path, output_ppt_path, layout_index=0):
    """
    根据 JSON 数据和 PPT 模板生成新的 PPT。
    layout_index: 指定要使用的幻灯片布局的索引（从0开始）。
    """
    try:
        # 1. 加载 JSON 数据
        with open(json_data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"DEBUG: 完整的JSON数据: {json.dumps(data, indent=2)}", file=sys.stderr) # 新增日志

        # 修正：从嵌套的 resume_info 字典中获取所有简历信息
        resume_info = data.get("resume_info", {})
        print(f"DEBUG: resume_info 数据: {json.dumps(resume_info, indent=2)}", file=sys.stderr) # 新增日志

        name = resume_info.get("姓名", "")
        hospital = resume_info.get("医院", "")
        department = resume_info.get("科室", "")
        title = resume_info.get("职称", "")
        
        # 从 processing_stats 中获取最佳头像路径
        processing_stats = data.get("processing_stats", {})
        avatar_filename = processing_stats.get("best_portrait", "")
        
        print(f"DEBUG: 从JSON中获取的头像文件名: {avatar_filename}", file=sys.stderr)

        # 修正头像的完整路径
        # 自动检测环境并确定项目根目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Docker环境检测：如果在 /app/ppt_generator，则项目根目录是 /app
        if script_dir.endswith('/app/ppt_generator') or script_dir.endswith('\\app\\ppt_generator'):
            project_root = '/app'  # Docker环境
            print(f"DEBUG: 检测到Docker环境，项目根目录: {project_root}", file=sys.stderr)
        else:
            # 本地开发环境：main/ppt_generator -> project_root  
            project_root = os.path.abspath(os.path.join(script_dir, '..', '..'))
            print(f"DEBUG: 检测到本地环境，项目根目录: {project_root}", file=sys.stderr)
        
        # 头像路径修正：avatar_filename 本身就包含了完整的相对路径
        # 例如： avatar_filename = "output/VCOUTPUTDATA20250708074941/image3_cutout.png"
        avatar_path = os.path.join(project_root, avatar_filename)
        # 规范化路径以处理 / 和 \ 的差异
        avatar_path = os.path.normpath(avatar_path)

        # DEBUG: Print the constructed avatar_path to stderr
        print(f"DEBUG: 尝试加载的头像路径: {avatar_path}", file=sys.stderr)

        # 获取详细的简历信息字段
        position_list = resume_info.get("职务职称列表", [])
        academic_title_list = resume_info.get("学术任职列表", [])
        academic_output_list = resume_info.get("学术成果列表", [])
        other_info = resume_info.get("其他", "")
        
        # 组合介绍内容
        introduction_parts = []
        
        # 添加职务职称信息（不显示标题）
        if position_list:
            for pos in position_list:
                introduction_parts.append(f"• {pos}")
        
        # 添加学术任职信息（不显示标题）
        if academic_title_list:
            for title in academic_title_list:
                introduction_parts.append(f"• {title}")
        
        # 添加学术成果信息（不显示标题）
        if academic_output_list:
            for output in academic_output_list:
                introduction_parts.append(f"• {output}")
        
        # 添加其他信息（不显示标题）
        if other_info:
            introduction_parts.append(f"• {other_info}")
        
        # 组合最终的介绍文本（不添加空行）
        introduction = "\n".join(introduction_parts)
        
        print(f"DEBUG: 组合后的介绍文本长度: {len(introduction)} 字符", file=sys.stderr)

        # 2. 加载 PPT 模板
        prs = Presentation(template_ppt_path)
        
        # 根据指定的布局索引获取布局，并添加新幻灯片
        slide_layout = prs.slide_layouts[layout_index] 
        slide = prs.slides.add_slide(slide_layout)

        # 3. 填充文本占位符 - 改进版本
        for shape in slide.shapes:
            if not shape.is_placeholder or not shape.has_text_frame:
                continue

            text_frame = shape.text_frame
            ph_type = shape.placeholder_format.type
            ph_idx = shape.placeholder_format.idx
            
            # 根据占位符类型和索引进行填充
            if ph_type == PP_PLACEHOLDER.BODY and ph_idx == 11: # 姓名
                fill_text_placeholder(text_frame, name, is_long_text=False)
            elif ph_type == PP_PLACEHOLDER.BODY and ph_idx == 12: # 职称
                fill_text_placeholder(text_frame, title, is_long_text=False)
            elif ph_type == PP_PLACEHOLDER.BODY and ph_idx == 14: # 科室
                fill_text_placeholder(text_frame, department, is_long_text=False)
            elif ph_type == PP_PLACEHOLDER.BODY and ph_idx == 13: # 医院
                fill_text_placeholder(text_frame, hospital, is_long_text=False)
            elif ph_type == PP_PLACEHOLDER.BODY and ph_idx == 19: # 介绍 - 使用长文本处理
                print(f"DEBUG: 处理介绍文本，长度: {len(introduction)} 字符", file=sys.stderr)
                fill_text_placeholder(text_frame, introduction, is_long_text=True)

        # 4. 插入头像图片
        avatar_inserted = False
        if os.path.exists(avatar_path):
            for placeholder in slide.placeholders:
                if placeholder.placeholder_format.type == PP_PLACEHOLDER.PICTURE: # 使用精确的图片占位符识别
                    # 找到图片占位符，插入图片
                    picture = placeholder.insert_picture(avatar_path)
                    avatar_inserted = True
                    break
            
            if not avatar_inserted:
                # Fallback: 如果没有找到合适的图片占位符，则在固定位置插入图片
                # 这些坐标和尺寸需要根据实际模板进行调整
                left = Inches(1)
                top = Inches(1.5)
                width = Inches(1.5)
                height = Inches(1.5)
                slide.shapes.add_picture(avatar_path, left, top, width=width, height=height)
        else:
            # 警告信息打印到 stderr，而不是 stdout
            print(f"警告：头像文件未找到：{avatar_path}", file=sys.stderr)

        # 5. 保存生成的 PPT
        prs.save(output_ppt_path)
        print(output_ppt_path) # 仅打印路径到 stdout

    except Exception as e:
        # 错误信息打印到 stderr
        print(f"生成 PPT 时发生错误：{e}", file=sys.stderr)
        sys.exit(1) # 使用 sys.exit(1) 明确表示错误退出

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="根据 JSON 数据和 PPT 模板生成新的 PPT。")
    parser.add_argument("--input_json", required=True, help="输入 JSON 文件的路径")
    parser.add_argument("--template_path", required=True, help="PPT 模板文件的路径")
    parser.add_argument("--output_dir", required=True, help="生成的 PPT 文件的输出目录")
    parser.add_argument("--layout_index", type=int, default=2, help="要使用的幻灯片布局的索引（从0开始，默认为2）")
    
    args = parser.parse_args()

    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)

    # 生成唯一的输出文件名
    output_ppt_file = os.path.join(args.output_dir, f"generated_presentation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pptx")

    generate_ppt(args.input_json, args.template_path, output_ppt_file, args.layout_index)