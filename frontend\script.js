// 全局变量
let extractionResult = null;
let extractionOutputDir = null; // 新增：存储提取结果的输出目录 (例如 output/VCOUTPUTDATAxxxx)
let latestGeneratedPPTPath = null; // 存储最新生成的PPT的最终下载路径 (例如 output/VCOUTPUTDATAxxxx/generated_presentation.pptx)

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    // 强制隐藏旧的加载状态，以防万一它没有被移除
    document.getElementById('loadingState')?.classList.add('hidden');
});

// 初始化事件监听器
function initializeEventListeners() {
    const fileInput = document.getElementById('fileInput');
    const dropZone = document.getElementById('uploadZone');
    const extractBtn = document.getElementById('extractBtn');
    const selectFileBtn = document.getElementById('selectFileBtn');
    const downloadJsonBtn = document.getElementById('downloadJsonBtn');
    const downloadImagesBtn = document.getElementById('downloadImagesBtn');
    const generatePPTBtn = document.getElementById('generatePPTBtn'); // 新增：获取生成PPT按钮
    const downloadPPTBtn = document.getElementById('downloadPPTBtn');
    const downloadCutoutBtn = document.getElementById('downloadCutoutBtn'); // 新增：获取下载抠图结果按钮

    // 文件选择事件
    if (fileInput) {
        fileInput.addEventListener('change', handleFileSelect);
    }

    // 选择文件按钮事件
    if (selectFileBtn) {
        selectFileBtn.addEventListener('click', () => {
            fileInput.click();
        });
    }

    // 拖拽事件
    if (dropZone) {
        dropZone.addEventListener('dragover', handleDragOver);
        dropZone.addEventListener('dragleave', handleDragLeave);
        dropZone.addEventListener('drop', handleDrop);
    }

    // 提取按钮事件
    if (extractBtn) {
        extractBtn.addEventListener('click', handleExtract);
    }

    // 下载按钮事件
    if (downloadJsonBtn) {
        downloadJsonBtn.addEventListener('click', downloadJson);
    }

    if (downloadImagesBtn) {
        downloadImagesBtn.addEventListener('click', downloadImages);
    }

    // 新增：生成PPT按钮事件
    if (generatePPTBtn) {
        generatePPTBtn.addEventListener('click', handleGeneratePPT);
    }

    // PPT下载事件
    if (downloadPPTBtn) {
        downloadPPTBtn.addEventListener('click', downloadPPT);
    }

    // 抠图结果下载事件
    if (downloadCutoutBtn) {
        downloadCutoutBtn.addEventListener('click', downloadCutoutResult);
    }

    // 建立日志流连接
    connectLogStream();
}

// 连接日志流
function connectLogStream() {
    const eventSource = new EventSource('/api/log');
    
    eventSource.onmessage = function(event) {
        try {
            const data = JSON.parse(event.data);
            if (data.type && data.message) {
                addProcessingLog(data.message, data.type);
            }
        } catch (error) {
            console.error('解析日志数据失败:', error);
        }
    };
    
    eventSource.onerror = function(error) {
        console.error('日志流连接错误:', error);
        // 尝试重新连接
        setTimeout(() => {
            connectLogStream();
        }, 5000);
    };
    
    eventSource.addEventListener('log', function(event) {
        try {
            const data = JSON.parse(event.data);
            if (data.type && data.message) {
                addProcessingLog(data.message, data.type);
            }
        } catch (error) {
            console.error('解析日志数据失败:', error);
        }
    });
    
    eventSource.addEventListener('ping', function(event) {
        // 保持连接活跃
        // console.log('日志流心跳:', event.data);
    });
}

// 处理文件选择
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        processSelectedFile(file);
    }
}

// 处理拖拽悬停
function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('border-blue-500', 'bg-blue-50');
}

// 处理拖拽离开
function handleDragLeave(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('border-blue-500', 'bg-blue-50');
}

// 处理文件拖放
function handleDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('border-blue-500', 'bg-blue-50');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        processSelectedFile(file);
    }
}

// 验证文件
function isValidFile(file) {
    const validTypes = [
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/vnd.ms-powerpoint'
    ];
    const validExtensions = ['.ppt', '.pptx'];
    
    const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
    return validTypes.includes(file.type) || validExtensions.includes(extension);
}

// 处理选中的文件
function processSelectedFile(file) {
    if (!isValidFile(file)) {
        showError('请选择有效的PPT文件 (.ppt 或 .pptx)');
        return;
    }

    // 显示文件信息
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    
    if (fileInfo && fileName && fileSize) {
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        fileInfo.classList.remove('hidden');
    }

    // 启用提取按钮
    const extractBtn = document.getElementById('extractBtn');
    if (extractBtn) {
        extractBtn.disabled = false;
        extractBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        extractBtn.classList.add('hover:bg-green-700');
    }

    // 存储文件引用
    window.selectedFile = file;
}

// 设置按钮状态的通用函数
function setButtonState(buttonId, isLoading, defaultText, loadingText, iconClass, loadingIconClass) {
    const button = document.getElementById(buttonId);
    console.log(`setButtonState: Button ID: ${buttonId}, Is Loading: ${isLoading}, Button element found: ${!!button}`);
    if (!button) {
        console.error(`setButtonState: Button with ID '${buttonId}' not found.`);
        return;
    }

    if (isLoading) {
        button.disabled = true;
        button.classList.add('opacity-50', 'cursor-not-allowed');
        button.innerHTML = `<i class="fas ${loadingIconClass} mr-2 loading-spinner"></i>${loadingText}`;
        console.log(`setButtonState: Set ${buttonId} to loading state.`);
    } else {
        button.disabled = false;
        button.classList.remove('opacity-50', 'cursor-not-allowed');
        button.innerHTML = `<i class="fas ${iconClass} mr-2"></i>${defaultText}`;
        console.log(`setButtonState: Set ${buttonId} to default state.`);
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 添加处理步骤日志
function addProcessingLog(message, type = 'info') {
    const logContainer = document.getElementById('processingLog');
    if (!logContainer) return;

    const logEntry = document.createElement('div');
    logEntry.className = `p-2 mb-2 rounded text-sm ${getLogTypeClass(type)}`;
    
    const timestamp = new Date().toLocaleTimeString();
    logEntry.innerHTML = `<span class="font-mono text-xs opacity-75">[${timestamp}]</span> ${message}`;
    
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
}

// 获取日志类型样式
function getLogTypeClass(type) {
    switch (type) {
        case 'success': return 'bg-green-100 text-green-800 border border-green-200';
        case 'error': return 'bg-red-100 text-red-800 border border-red-200';
        case 'warning': return 'bg-yellow-100 text-yellow-800 border border-yellow-200';
        case 'info': return 'bg-blue-100 text-blue-800 border border-blue-200';
        default: return 'bg-gray-100 text-gray-800 border border-gray-200';
    }
}

// 清空处理日志
function clearProcessingLog() {
    const logContainer = document.getElementById('processingLog');
    if (logContainer) {
        logContainer.innerHTML = '';
    }
}

// 处理提取请求
async function handleExtract() {
    const file = window.selectedFile;
    if (!file) {
        showError('请先选择PPT文件');
        return;
    }

    // 禁用提取按钮并显示加载状态
    setButtonState('extractBtn', true, '开始提取', '提取中...', 'fa-play', 'fa-spinner');
    hideError(); // 隐藏之前的错误信息

    const formData = new FormData();
    formData.append('file', file);

    try {
        const response = await fetch('/api/extract', {
            method: 'POST',
            body: formData
        });

        // 添加对响应状态的检查和原始响应文本的打印
        console.log('handleExtract 响应状态:', response.status, response.statusText);
        if (!response.ok) {
            const errorText = await response.text();
            console.error('handleExtract 响应错误文本:', errorText);
            throw new Error(`HTTP 错误！状态: ${response.status}, 文本: ${errorText}`);
        }

        const result = await response.json();
        console.log('handleExtract 后端响应:', result);

        if (result.success) {
            showSuccess(result.message);
            // 将整个 result.data 对象赋值给 extractionResult
            extractionResult = result.data;
            extractionOutputDir = result.data.output_dir.replace(/\\/g, '/');
            console.log('DEBUG: extractionResult 赋值后:', extractionResult);
            console.log('DEBUG: extractionOutputDir 赋值后:', extractionOutputDir);
            console.log('提取结果输出目录:', extractionOutputDir); 
            displayResults(result.data);
            // 启用生成PPT按钮并确保文本正确
            const generatePPTBtn = document.getElementById('generatePPTBtn');
            if (generatePPTBtn) {
                setButtonState('generatePPTBtn', false, '生成PPT', '', 'fa-file-powerpoint', 'fa-spinner');
                generatePPTBtn.disabled = false; // 确保它被显式启用
                generatePPTBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            }
            // 禁用下载PPT按钮，因为它只有在生成PPT后才可用
            const downloadPPTBtn = document.getElementById('downloadPPTBtn');
            if (downloadPPTBtn) {
                downloadPPTBtn.disabled = true;
                downloadPPTBtn.classList.add('opacity-50', 'cursor-not-allowed');
            }
            // 清空之前生成的PPT路径
            latestGeneratedPPTPath = null;

        } else {
            showError(result.message + (result.debug ? ` (Debug: ${result.debug})` : ''));
        }
    } catch (error) {
        console.error('提取失败:', error);
        showError('提取过程中发生网络错误或服务器错误');
    } finally {
        // 重新启用提取按钮
        setButtonState('extractBtn', false, '开始提取', '', 'fa-play', 'fa-spinner');
    }
}

// 显示详细处理结果
function displayDetailedResults(result) {
    // 显示简历信息
    if (result.resume_info) {
        addProcessingLog('📋 简历信息提取完成', 'success');
        displayResults(result);
    }

    // 显示图片处理结果
    if (result.image_processing_results) {
        const stats = result.image_processing_results;
        addProcessingLog(`🖼️ 图片处理完成: 总图片 ${stats.total_images} 张`, 'success');
        addProcessingLog(`👤 人像检测: ${stats.portrait_images} 张`, 'info');
        addProcessingLog(`⚡ 处理时间: ${stats.processing_time}ms`, 'info');
        
        if (stats.best_portrait) {
            addProcessingLog(`🎯 最佳头像: ${stats.best_portrait}`, 'success');
        }

        // 显示每张图片的处理结果
        if (stats.results && stats.results.length > 0) {
            addProcessingLog('📸 详细处理结果:', 'info');
            stats.results.forEach((imgResult, index) => {
                let status = '✅ 处理成功';
                if (imgResult.error) {
                    status = `❌ 处理失败: ${imgResult.error}`;
                } else if (imgResult.is_portrait) {
                    status = '👤 人像图片';
                }
                
                const fileName = imgResult.original_path && typeof imgResult.original_path === 'string' ? 
                    imgResult.original_path.split('/').pop() : `图片${index + 1}`;
                addProcessingLog(`   ${fileName}: ${status}`, imgResult.error ? 'error' : 'success');
                
                if (imgResult.cutout_path && typeof imgResult.cutout_path === 'string') {
                    addProcessingLog(`   ✂️ 抠图完成: ${imgResult.cutout_path.split('/').pop()}`, 'success');
                }
            });
        }
    }

    // 显示图片列表 (原始图片)
    if (result.images && result.images.length > 0) {
        addProcessingLog(`🖼️ 提取到 ${result.images.length} 张原始图片`, 'success');
        displayOriginalExtractedImages(result.images); // 调用新的函数来显示所有原始提取图片
    }
}

// 显示JSON预览
function displayJsonPreview(result) {
    const jsonPreview = document.getElementById('jsonPreview');
    
    if (!jsonPreview) {
        console.error('JSON预览元素未找到');
        return;
    }
    
    try {
        // 格式化JSON数据，只显示简历相关的核心信息
        let previewData = {};
        
        // 解析简历信息
        let resumeInfo = result.resume_info || result.resume || result;
        if (result.data) {
            resumeInfo = result.data.resume_info || result.data.resume || result.data;
        }
        
        // 构建预览数据
        previewData = {
            "姓名": resumeInfo.姓名 || resumeInfo.name || "未提取到",
            "医院": resumeInfo.医院 || resumeInfo.hospital || "未提取到", 
            "科室": resumeInfo.科室 || resumeInfo.department || "未提取到",
            "职称": resumeInfo.职称 || resumeInfo.title || "未提取到",
            "头像": resumeInfo.头像 || resumeInfo.profile_picture || "未提取到",
            "职务职称列表": resumeInfo.职务职称列表 || resumeInfo.Positions || [],
            "学术任职列表": resumeInfo.学术任职列表 || resumeInfo.AcademicTitles || [],
            "学术成果列表": resumeInfo.学术成果列表 || resumeInfo.AcademicOutputs || []
        };
        
        const formattedJson = JSON.stringify(previewData, null, 2);
        jsonPreview.innerHTML = `<pre class="whitespace-pre-wrap">${formattedJson}</pre>`;
        
    } catch (error) {
        console.error('JSON预览格式化失败:', error);
        jsonPreview.innerHTML = '<div class="text-red-500 text-center py-4">JSON数据格式化失败</div>';
    }
}

// 显示提取的照片预览
function displayExtractedPhotosPreview(result) {
    const extractedPhotosPreview = document.getElementById('extractedPhotosPreview');
    
    if (!extractedPhotosPreview) {
        console.error('提取照片预览元素未找到');
        return;
    }
    
    // 清空现有内容
    extractedPhotosPreview.innerHTML = '';
    
    // 收集所有图片
    let allImages = [];
    
    // 从结果中提取图片
    if (result.images && result.images.length > 0) {
        allImages = result.images;
    } else if (result.data && result.data.images) {
        allImages = result.data.images;
    }
    
    if (allImages.length === 0) {
        extractedPhotosPreview.innerHTML = `
            <div class="text-gray-400 text-center py-8 col-span-2">
                <i class="fas fa-camera mr-2"></i>
                未找到提取的照片
            </div>
        `;
        return;
    }
    
    // 显示最多6张照片的预览
    const previewImages = allImages.slice(0, 6);
    
    previewImages.forEach((imagePath, index) => {
        const imageCard = document.createElement('div');
        imageCard.className = 'relative bg-white rounded border overflow-hidden hover:shadow-md transition-shadow cursor-pointer';
        
        let imageUrl = imagePath;
        if (!imagePath.startsWith('http') && !imagePath.startsWith('/')) {
            // 将反斜杠转换为正斜杠，确保URL格式正确
            const normalizedPath = imagePath.replace(/\\/g, '/');
            imageUrl = `/api/image/${normalizedPath}`;
        }
        
        imageCard.innerHTML = `
            <img src="${imageUrl}" alt="提取图片 ${index + 1}" 
                 class="w-full h-20 object-cover"
                 onclick="openImageModal('${imageUrl}', '提取图片 ${index + 1}')"
                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuaaguaXoOWbvueJhzwvdGV4dD48L3N2Zz4='">
            <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 text-center">
                图片 ${index + 1}
            </div>
        `;
        
        extractedPhotosPreview.appendChild(imageCard);
    });
    
    // 如果有更多图片，显示数量提示
    if (allImages.length > 6) {
        const moreCard = document.createElement('div');
        moreCard.className = 'bg-gray-100 rounded border flex items-center justify-center text-gray-500 text-sm';
        moreCard.innerHTML = `
            <div class="text-center">
                <i class="fas fa-plus-circle text-lg mb-1"></i>
                <div>还有${allImages.length - 6}张</div>
            </div>
        `;
        extractedPhotosPreview.appendChild(moreCard);
    }
}

// 显示提取结果
function displayResults(result) {
    const resultSection = document.getElementById('resultSection');
    
    console.log('完整的结果数据:', result);
    
    // 显示预览卡片
    displayJsonPreview(result);
    displayExtractedPhotosPreview(result);
    
    // 解析简历信息
    let resumeInfo = {};
    if (result.resume_info) {
        resumeInfo = result.resume_info;
    } else if (result.resume) {
        resumeInfo = result.resume;
    } else if (result.data && result.data.resume_info) {
        resumeInfo = result.data.resume_info;
    } else if (result.data && result.data.resume) {
        resumeInfo = result.data.resume;
    } else if (result.姓名) {
        // 直接是简历信息对象
        resumeInfo = result;
    } else if (result.data && result.data.姓名) {
        resumeInfo = result.data;
    }
    
    console.log('解析的简历信息:', resumeInfo);
    console.log('头像路径字段:', {
        '头像': resumeInfo.头像,
        'profile_picture': resumeInfo.profile_picture,
        'ProfilePicture': resumeInfo.ProfilePicture
    });
    
    // 填充基本信息 - 支持中文字段名
    document.getElementById('extractedName').textContent = resumeInfo.name || resumeInfo.姓名 || '未提取到';
    document.getElementById('extractedHospital').textContent = resumeInfo.hospital || resumeInfo.医院 || '未提取到';
    document.getElementById('extractedDepartment').textContent = resumeInfo.department || resumeInfo.科室 || '未提取到';
    document.getElementById('extractedTitle').textContent = resumeInfo.title || resumeInfo.职称 || '未提取到';
    
    // 使用和PPT生成相同的介绍内容格式
    const descriptionParts = [];
    
    // 添加职务职称信息（不显示标题）
    const positionList = resumeInfo.职务职称列表 || [];
    if (positionList && positionList.length > 0) {
        positionList.forEach(pos => {
            descriptionParts.push(`• ${pos}`);
        });
    }
    
    // 添加学术任职信息（不显示标题）
    const academicTitleList = resumeInfo.学术任职列表 || [];
    if (academicTitleList && academicTitleList.length > 0) {
        academicTitleList.forEach(title => {
            descriptionParts.push(`• ${title}`);
        });
    }
    
    // 添加学术成果信息（不显示标题）
    const academicOutputList = resumeInfo.学术成果列表 || [];
    if (academicOutputList && academicOutputList.length > 0) {
        academicOutputList.forEach(output => {
            descriptionParts.push(`• ${output}`);
        });
    }
    
    // 添加其他信息（不显示标题）
    const otherInfo = resumeInfo.其他 || '';
    if (otherInfo) {
        descriptionParts.push(`• ${otherInfo}`);
    }
    
    // 设置介绍内容
    const combinedDescription = descriptionParts.length > 0 ? descriptionParts.join('\n') : '未提取到';
    const descriptionElement = document.getElementById('extractedDescription');
    if (descriptionElement) {
        descriptionElement.style.whiteSpace = 'pre-line'; // 保持换行格式
        descriptionElement.textContent = combinedDescription;
    }
    
    // 填充详细简历信息 - 新增字段
    const positionSection = document.getElementById('positionSection');
    const academicTitleSection = document.getElementById('academicTitleSection');
    const specialtySection = document.getElementById('specialtySection');
    const academicOutputSection = document.getElementById('academicOutputSection');
    const otherSection = document.getElementById('otherSection');
    
    // 隐藏所有冗余的详细信息字段
    positionSection?.classList.add('hidden');
    academicTitleSection?.classList.add('hidden');
    specialtySection?.classList.add('hidden');
    academicOutputSection?.classList.add('hidden');
    otherSection?.classList.add('hidden');
    
    // 显示照片预览
    displayPhotoPreview(result, resumeInfo);
    
    // 显示结果区域
    resultSection.classList.remove('hidden');
    
    // 滚动到结果区域
    resultSection.scrollIntoView({ behavior: 'smooth' });
}

// 显示照片预览
function displayPhotoPreview(result, resumeInfo) {
    const photoPreviewSection = document.getElementById('photoPreviewSection');
    const photoPreviewSuccess = document.getElementById('photoPreviewSuccess');
    const photoPreviewError = document.getElementById('photoPreviewError');
    const bestPortraitImage = document.getElementById('bestPortraitImage');
    const photoPreviewCaption = document.getElementById('photoPreviewCaption');
    const generatePPTBtn = document.getElementById('generatePPTBtn');

    if (!photoPreviewSection || !bestPortraitImage || !photoPreviewCaption) {
        console.error('照片预览区域的HTML元素未找到。');
        return;
    }

    let bestPortraitPath = null;
    let bestPortraitImageUrl = '';
    let hasValidPortrait = false;
    let isProcessedPortrait = false;

    // 优先从 image_processing_results.best_portrait 获取路径
    if (result.image_processing_results && result.image_processing_results.best_portrait) {
        bestPortraitPath = result.image_processing_results.best_portrait;
        hasValidPortrait = true;
        isProcessedPortrait = true; // 这是处理后的抠图结果
    } else if (resumeInfo.头像) { // 如果 image_processing_results.best_portrait 不存在，则回退到 resumeInfo.头像
        bestPortraitPath = resumeInfo.头像;
        hasValidPortrait = true;
        // 检查头像路径是否包含 _cutout，来判断是否是抠图结果
        isProcessedPortrait = bestPortraitPath.includes('_cutout'); 
    }

    // 构建完整的图片URL
    if (bestPortraitPath && !bestPortraitPath.startsWith('http') && !bestPortraitPath.startsWith('/')) {
        // 将反斜杠转换为正斜杠，确保URL格式正确
        const normalizedPath = bestPortraitPath.replace(/\\/g, '/');
        bestPortraitImageUrl = `/api/image/${normalizedPath}`;
    } else {
        bestPortraitImageUrl = bestPortraitPath; // 如果已经是完整URL或绝对路径，则直接使用
    }

    console.log('--- 照片预览调试信息 ---');
    console.log('bestPortraitPath (来自后端/简历信息):', bestPortraitPath);
    console.log('bestPortraitImageUrl (前端构建):', bestPortraitImageUrl);
    console.log('hasValidPortrait:', hasValidPortrait);
    console.log('isProcessedPortrait:', isProcessedPortrait);
    console.log('-------------------------');

    // 如果有图片处理结果或简历头像，显示照片预览区域
    if (result.image_processing_results || resumeInfo.头像) {
        photoPreviewSection.classList.remove('hidden');

        if (hasValidPortrait && bestPortraitImageUrl && isProcessedPortrait) {
            // 显示成功预览 - 只有在真正检测到人像并抠图成功时才显示
            photoPreviewSuccess.classList.remove('hidden');
            photoPreviewError.classList.add('hidden');
            bestPortraitImage.src = bestPortraitImageUrl;
            bestPortraitImage.classList.remove('hidden');
            photoPreviewCaption.textContent = '这是最佳人像抠图结果';
            
            // 启用PPT生成按钮
            if (generatePPTBtn) {
                generatePPTBtn.disabled = false;
                generatePPTBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            }
            
            // 启用下载抠图结果按钮
            const downloadCutoutBtn = document.getElementById('downloadCutoutBtn');
            if (downloadCutoutBtn) {
                downloadCutoutBtn.classList.remove('hidden');
                downloadCutoutBtn.disabled = false;
                downloadCutoutBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            }
        } else {
            // 显示错误提示 - 没有检测到人像或只有原始图片
            photoPreviewSuccess.classList.add('hidden');
            photoPreviewError.classList.remove('hidden');
            bestPortraitImage.src = '';
            bestPortraitImage.classList.add('hidden');
            photoPreviewCaption.textContent = '人像抠图失败';
            
            // 禁用PPT生成按钮
            if (generatePPTBtn) {
                generatePPTBtn.disabled = true;
                generatePPTBtn.classList.add('opacity-50', 'cursor-not-allowed');
                
                // 添加提示信息
                const existingTooltip = generatePPTBtn.getAttribute('title');
                if (!existingTooltip) {
                    generatePPTBtn.setAttribute('title', '无法生成PPT：人像抠图失败，请检查图片质量');
                }
            }
            
            // 隐藏并禁用下载抠图结果按钮
            const downloadCutoutBtn = document.getElementById('downloadCutoutBtn');
            if (downloadCutoutBtn) {
                downloadCutoutBtn.classList.add('hidden');
                downloadCutoutBtn.disabled = true;
                downloadCutoutBtn.classList.add('opacity-50', 'cursor-not-allowed');
            }
        }
    } else {
        photoPreviewSection.classList.add('hidden'); // 如果没有图片处理结果，也没有简历头像，则隐藏整个区域
        
        // 隐藏下载抠图结果按钮
        const downloadCutoutBtn = document.getElementById('downloadCutoutBtn');
        if (downloadCutoutBtn) {
            downloadCutoutBtn.classList.add('hidden');
            downloadCutoutBtn.disabled = true;
            downloadCutoutBtn.classList.add('opacity-50', 'cursor-not-allowed');
        }
    }
}

// 创建照片卡片
function createPhotoCard(imagePath, title) {
    if (!imagePath || typeof imagePath !== 'string') {
        console.warn('无效的图片路径:', imagePath);
        return null;
    }

    const card = document.createElement('div');
    card.className = 'bg-gray-50 rounded-lg p-2 border-2 border-gray-200';

    let imageUrl = imagePath;
    if (!imagePath.startsWith('http') && !imagePath.startsWith('/')) {
        // 将反斜杠转换为正斜杠，确保URL格式正确
        const normalizedPath = imagePath.replace(/\\/g, '/');
        imageUrl = `/api/image/${normalizedPath}`;
    }

    card.innerHTML = `
        <div class="text-center">
            <img src="${imageUrl}" alt="${title}"
                 class="w-full h-32 object-contain rounded-lg mb-2 cursor-pointer hover:opacity-80 transition-opacity"
                 onclick="openImageModal('${imageUrl}', '${title}')"
                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuaaguaXoOaJgOacieWbvueJhzwvdGV4dD48L3N2Zz4='">
            <p class="text-sm font-medium text-gray-700">${title}</p>
        </div>
    `;

    return card;
}

// 新增：显示所有提取到的原始图片预览
function displayOriginalExtractedImages(images) {
    const originalImagePreviewSection = document.getElementById('originalImagePreviewSection');
    const originalExtractedImagesContainer = document.getElementById('originalExtractedImagesContainer');

    if (!originalImagePreviewSection || !originalExtractedImagesContainer) {
        console.error('原始图片提取预览区域的HTML元素未找到。');
        return;
    }

    originalExtractedImagesContainer.innerHTML = ''; // 清空之前的内容

    if (images && images.length > 0) {
        images.forEach((imagePath, index) => {
            const photoCard = createPhotoCard(imagePath, `原始图片 ${index + 1}`);
            if (photoCard) {
                originalExtractedImagesContainer.appendChild(photoCard);
            }
        });
        originalImagePreviewSection.classList.remove('hidden'); // 显示区域
    } else {
        originalImagePreviewSection.classList.add('hidden'); // 隐藏区域
    }
}

// 打开图片模态框
function openImageModal(imageUrl, title) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-lg max-w-4xl max-h-full overflow-auto p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">${title}</h3>
                <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <img src="${imageUrl}" alt="${title}" class="w-full h-auto">
        </div>
    `;
    
    // 点击背景关闭
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
    
    document.body.appendChild(modal);
}

// 下载JSON文件
function downloadJson() {
    if (!extractionResult) {
        showError('没有可下载的数据');
        return;
    }
    
    // 确保下载完整的数据结构
    const jsonData = JSON.stringify(extractionResult, null, 2);
    const blob = new Blob([jsonData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `extracted_data_${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// 下载图片文件
function downloadImages() {
    console.log('DEBUG: downloadImages 函数开始执行'); // 新增日志
    console.log('DEBUG: downloadImages - extractionResult:', extractionResult); // 新增日志

    if (!extractionResult) {
        showError('没有可下载的数据');
        return;
    }
    
    // 收集所有需要下载的图片路径
    let allImages = [];
    
    // 从简化后的数据结构中提取图片
    if (extractionResult.images) {
        extractionResult.images.forEach(imageInfo => {
            if (imageInfo.original) {
                // 直接使用完整路径，包含任务目录前缀
                allImages.push(imageInfo.original);
            }
            if (imageInfo.cutout) {
                // 直接使用完整路径，包含任务目录前缀
                allImages.push(imageInfo.cutout);
            }
        });
    }
    
    // 如果没有找到图片，尝试从旧格式数据中提取
    if (allImages.length === 0 && extractionResult.images) {
        extractionResult.images.forEach(imagePath => {
            // 直接使用完整路径
            allImages.push(imagePath);
        });
    }
    
    // 如果有简历信息且包含头像，则添加头像图片
    if (extractionResult.resume_info && extractionResult.resume_info.头像) {
        allImages.push(extractionResult.resume_info.头像);
    }

    console.log('DEBUG: downloadImages - 收集到的 allImages:', allImages); // 新增日志

    if (allImages.length === 0) {
        showError('没有可下载的图片');
        return;
    }
    
    console.log('DEBUG: 准备下载的图片 (allImages):', allImages); // 新增日志
    
    // 创建ZIP文件下载
    fetch('/api/download-images', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            images: allImages
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`下载失败: ${response.status} ${response.statusText}`);
        }
        return response.blob();
    })
    .then(blob => {
        if (blob.size === 0) {
            throw new Error('下载的文件为空');
        }
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `extracted_images_${Date.now()}.zip`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showSuccess(`图片下载成功！共下载 ${allImages.length} 张图片`);
    })
    .catch(error => {
        console.error('下载图片失败:', error);
        showError('下载失败: ' + error.message);
    });
}

// 新增：处理PPT生成请求
async function handleGeneratePPT() {
    const generatePPTBtn = document.getElementById('generatePPTBtn');
    const downloadPPTBtn = document.getElementById('downloadPPTBtn');

    console.log('handleGeneratePPT: generatePPTBtn found:', !!generatePPTBtn);
    console.log('handleGeneratePPT: downloadPPTBtn found:', !!downloadPPTBtn);

    // 检查是否存在人像抠图失败的情况
    const photoPreviewError = document.getElementById('photoPreviewError');
    if (photoPreviewError && !photoPreviewError.classList.contains('hidden')) {
        showError('无法生成PPT：人像抠图失败，请检查PPT中的图片质量后重新上传');
        return;
    }

    // 禁用生成PPT按钮并显示加载状态
    setButtonState('generatePPTBtn', true, '生成PPT', '生成中...', 'fa-file-powerpoint', 'fa-spinner');

    if (!extractionResult) {
        showError('请先提取简历信息');
        // 恢复生成PPT按钮状态
        setButtonState('generatePPTBtn', false, '生成PPT', '', 'fa-file-powerpoint', 'fa-spinner');
        return;
    }
    
    // 如果没有提取到输出目录，则报错
    if (!extractionOutputDir) {
        showError('未找到提取结果的输出目录，请先进行提取操作。');
        // 恢复生成PPT按钮状态
        setButtonState('generatePPTBtn', false, '生成PPT', '', 'fa-file-powerpoint', 'fa-spinner');
        return;
    }

    hideError(); // 隐藏之前的错误信息

    // 调试：打印即将发送的 input_json_path
    console.log('发送给后端的InputJsonPath:', extractionOutputDir);

    try {
        const response = await fetch('/api/generate-ppt', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            // 传递 output_root_path 作为 input_json_path
            body: JSON.stringify({ resume_data: extractionResult, input_json_path: extractionOutputDir })
        });

        // 添加对响应状态的检查和原始响应文本的打印
        console.log('handleGeneratePPT 响应状态:', response.status, response.statusText);
        if (!response.ok) {
            const errorText = await response.text();
            console.error('handleGeneratePPT 响应错误文本:', errorText);
            throw new Error(`HTTP 错误！状态: ${response.status}, 文本: ${errorText}`);
        }

        const result = await response.json();
        console.log('handleGeneratePPT 后端响应:', result);

        if (result.success) {
            showSuccess(result.message);
            // 更新 latestGeneratedPPTPath 为最终的 PPT 文件路径
            latestGeneratedPPTPath = result.data.output_path.replace(/\\/g, '/'); 
            console.log('DEBUG: latestGeneratedPPTPath 赋值后:', latestGeneratedPPTPath); // 新增日志
            console.log('最终生成的PPT文件路径:', latestGeneratedPPTPath);

            // 生成PPT按钮保持禁用状态，但文本恢复为"生成PPT"
            setButtonState('generatePPTBtn', false, '生成PPT', '', 'fa-file-powerpoint', 'fa-spinner');
            generatePPTBtn.disabled = true; // 显式禁用
            generatePPTBtn.classList.add('opacity-50', 'cursor-not-allowed');

            // 启用下载PPT按钮，并设置状态和链接
            if (downloadPPTBtn) {
                downloadPPTBtn.disabled = false;
                downloadPPTBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                downloadPPTBtn.innerHTML = `<i class="fas fa-download mr-2"></i>下载PPT文件`; // 确保文本是正确的
            }
        } else {
            showError(result.message + (result.debug ? ` (Debug: ${result.debug})` : ''));
            // 失败时恢复生成PPT按钮
            setButtonState('generatePPTBtn', false, '生成PPT', '', 'fa-file-powerpoint', 'fa-spinner');
            if (downloadPPTBtn) {
                downloadPPTBtn.disabled = true;
                downloadPPTBtn.classList.add('opacity-50', 'cursor-not-allowed');
            }
        }
    } catch (error) {
        console.error('生成PPT失败:', error);
        showError('生成PPT过程中发生网络错误或服务器错误');
        // 失败时恢复生成PPT按钮
        setButtonState('generatePPTBtn', false, '生成PPT', '', 'fa-file-powerpoint', 'fa-spinner');
        if (downloadPPTBtn) {
            downloadPPTBtn.disabled = true;
            downloadPPTBtn.classList.add('opacity-50', 'cursor-not-allowed');
        }
    } finally {
        // generatePPTBtn 的 disabled 状态已在成功或失败的分支中处理
    }
}

// 下载PPT文件
function downloadPPT() {
    if (!latestGeneratedPPTPath) {
        showError('没有可下载的PPT文件');
        return;
    }

    try {
        // 获取姓名和医院信息来构建文件名
        let resumeInfo = {};
        if (extractionResult) {
            if (extractionResult.resume_info) {
                resumeInfo = extractionResult.resume_info;
            } else if (extractionResult.resume) {
                resumeInfo = extractionResult.resume;
            } else if (extractionResult.data && extractionResult.data.resume_info) {
                resumeInfo = extractionResult.data.resume_info;
            } else if (extractionResult.data && extractionResult.data.resume) {
                resumeInfo = extractionResult.data.resume;
            }
        }

        const name = resumeInfo.name || resumeInfo.姓名 || '未知姓名';
        const hospital = resumeInfo.hospital || resumeInfo.医院 || '未知医院';
        
        // 构建文件名，去除可能的特殊字符
        const fileName = `${name}_${hospital}`.replace(/[<>:"/\\|?*]/g, '_');
        const finalFileName = `${fileName}.pptx`;

        // 使用 encodeURIComponent 编码路径，并构建完整的API下载URL，包含自定义文件名参数
        const downloadUrl = `/api/download-ppt/${encodeURIComponent(latestGeneratedPPTPath)}?filename=${encodeURIComponent(fileName)}`;
        
        // 创建一个临时的a标签来触发下载
        const a = document.createElement('a');
        a.href = downloadUrl;
        a.download = finalFileName; // 作为备用，虽然后端会覆盖
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        addProcessingLog(`📥 正在下载PPT文件: ${finalFileName}`, 'info');
        showSuccess(`PPT下载已开始: ${finalFileName}`);
        
    } catch (error) {
        console.error('下载PPT文件失败:', error);
        showError(`下载失败: ${error.message}`);
        
        // 如果出错，使用原来的文件名作为后备方案
        const downloadUrl = `/api/download-ppt/${encodeURIComponent(latestGeneratedPPTPath)}`;
        const a = document.createElement('a');
        a.href = downloadUrl;
        a.download = latestGeneratedPPTPath.split('/').pop(); // 从路径中获取文件名作为下载文件名
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        addProcessingLog('📥 正在下载PPT文件...', 'info');
    }
}

// 显示错误信息
function showError(message) {
    const errorDiv = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');
    if (errorDiv && errorText) {
        errorText.textContent = message;
        errorDiv.classList.remove('hidden');
    }
}

// 隐藏错误信息
function hideError() {
    const errorDiv = document.getElementById('errorMessage');
    if (errorDiv) {
        errorDiv.classList.add('hidden');
    }
}

// 显示成功信息
function showSuccess(message) {
    // 创建临时成功提示
    const successDiv = document.createElement('div');
    successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
    successDiv.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-check-circle mr-2"></i>
            <span>${message}</span>
        </div>
    `;
    document.body.appendChild(successDiv);
    
    // 3秒后自动移除
    setTimeout(() => {
        successDiv.remove();
    }, 3000);
}

// 下载抠图结果
async function downloadCutoutResult() {
    if (!extractionResult) {
        showError('没有可下载的抠图结果');
        return;
    }

    // 调试：打印完整的extractionResult结构
    console.log('=== 下载抠图结果调试信息 ===');
    console.log('完整的extractionResult:', extractionResult);
    console.log('image_processing_results:', extractionResult.image_processing_results);
    if (extractionResult.image_processing_results) {
        console.log('best_portrait:', extractionResult.image_processing_results.best_portrait);
    }

    // 获取最佳人像抠图路径 - 尝试多个可能的路径
    let bestPortraitPath = null;
    
    // 路径1: extractionResult.image_processing_results.best_portrait
    if (extractionResult.image_processing_results && extractionResult.image_processing_results.best_portrait) {
        bestPortraitPath = extractionResult.image_processing_results.best_portrait;
        console.log('找到抠图路径 (路径1):', bestPortraitPath);
    }
    
    // 路径2: 从resume_info中查找头像（可能包含抠图结果）
    if (!bestPortraitPath && extractionResult.resume_info && extractionResult.resume_info.头像) {
        const avatarPath = extractionResult.resume_info.头像;
        if (avatarPath && avatarPath.includes('_cutout')) {
            bestPortraitPath = avatarPath;
            console.log('找到抠图路径 (路径2-头像):', bestPortraitPath);
        }
    }
    
    // 路径3: 检查image_processing_results.results中的cutout_path
    if (!bestPortraitPath && extractionResult.image_processing_results && extractionResult.image_processing_results.results) {
        for (const result of extractionResult.image_processing_results.results) {
            if (result.cutout_path) {
                bestPortraitPath = result.cutout_path;
                console.log('找到抠图路径 (路径3-results):', bestPortraitPath);
                break;
            }
        }
    }

    console.log('最终确定的抠图路径:', bestPortraitPath);
    console.log('===============================');

    if (!bestPortraitPath) {
        showError('没有找到抠图结果。请检查是否成功完成了人像抠图处理。');
        return;
    }

    try {
        // 获取姓名和医院信息来构建文件名
        let resumeInfo = {};
        if (extractionResult.resume_info) {
            resumeInfo = extractionResult.resume_info;
        } else if (extractionResult.resume) {
            resumeInfo = extractionResult.resume;
        } else if (extractionResult.data && extractionResult.data.resume_info) {
            resumeInfo = extractionResult.data.resume_info;
        } else if (extractionResult.data && extractionResult.data.resume) {
            resumeInfo = extractionResult.data.resume;
        }

        const name = resumeInfo.name || resumeInfo.姓名 || '未知姓名';
        const hospital = resumeInfo.hospital || resumeInfo.医院 || '未知医院';
        
        // 构建文件名，去除可能的特殊字符
        const fileName = `${name}_${hospital}`.replace(/[<>:"/\\|?*]/g, '_');
        
        // 获取文件扩展名
        const fileExtension = bestPortraitPath.toLowerCase().includes('.png') ? '.png' : '.jpg';
        const finalFileName = `${fileName}${fileExtension}`;

        // 构建图片URL
        let imageUrl = bestPortraitPath;
        if (!bestPortraitPath.startsWith('http') && !bestPortraitPath.startsWith('/')) {
            const normalizedPath = bestPortraitPath.replace(/\\/g, '/');
            imageUrl = `/api/image/${normalizedPath}`;
        }

        // 获取图片数据
        const response = await fetch(imageUrl);
        if (!response.ok) {
            throw new Error(`获取图片失败: ${response.status}`);
        }

        const blob = await response.blob();
        
        // 创建下载链接并触发下载
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = finalFileName; // 这会触发文件保存对话框
        a.style.display = 'none';
        
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        // 清理URL对象
        URL.revokeObjectURL(url);
        
        showSuccess(`抠图结果下载已开始: ${finalFileName}`);
        
    } catch (error) {
        console.error('下载抠图结果失败:', error);
        showError(`下载失败: ${error.message}`);
    }
} 