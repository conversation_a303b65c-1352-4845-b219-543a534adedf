@echo off
chcp 65001 >nul
echo 检查项目文件编码...
echo.

echo =============================================================================
echo 检查批处理文件编码
echo =============================================================================

echo 检查 build_standalone.bat
findstr "chcp 65001" build_standalone.bat > nul
if %errorlevel% equ 0 (
    echo ✓ build_standalone.bat 已设置UTF-8编码
) else (
    echo ✗ build_standalone.bat 未设置UTF-8编码
)

echo 检查 test_local.bat
findstr "chcp 65001" test_local.bat > nul
if %errorlevel% equ 0 (
    echo ✓ test_local.bat 已设置UTF-8编码
) else (
    echo ✗ test_local.bat 未设置UTF-8编码
)

echo 检查 test_docker.bat
findstr "chcp 65001" test_docker.bat > nul
if %errorlevel% equ 0 (
    echo ✓ test_docker.bat 已设置UTF-8编码
) else (
    echo ✗ test_docker.bat 未设置UTF-8编码
)

echo 检查 test_env_local.bat
findstr "chcp 65001" test_env_local.bat > nul
if %errorlevel% equ 0 (
    echo ✓ test_env_local.bat 已设置UTF-8编码
) else (
    echo ✗ test_env_local.bat 未设置UTF-8编码
)

echo 检查 test_env_complete.bat
findstr "chcp 65001" test_env_complete.bat > nul
if %errorlevel% equ 0 (
    echo ✓ test_env_complete.bat 已设置UTF-8编码
) else (
    echo ✗ test_env_complete.bat 未设置UTF-8编码
)

echo 检查 verify_security.bat
findstr "chcp 65001" verify_security.bat > nul
if %errorlevel% equ 0 (
    echo ✓ verify_security.bat 已设置UTF-8编码
) else (
    echo ✗ verify_security.bat 未设置UTF-8编码
)

echo.
echo =============================================================================
echo 检查前端文件编码
echo =============================================================================

echo 检查 frontend/index.html
findstr "charset=\"UTF-8\"" frontend\index.html > nul
if %errorlevel% equ 0 (
    echo ✓ frontend/index.html 已设置UTF-8编码
) else (
    echo ✗ frontend/index.html 未设置UTF-8编码
)

echo.
echo =============================================================================
echo 检查Go文件编码
echo =============================================================================

echo 检查 main/main.go
findstr "charset=utf-8" main\main.go > nul
if %errorlevel% equ 0 (
    echo ✓ main/main.go 包含UTF-8编码设置
) else (
    echo 检查其他编码相关设置...
    findstr "encoding" main\main.go > nul
    if %errorlevel% equ 0 (
        echo ✓ main/main.go 包含编码相关代码
    ) else (
        echo - main/main.go 无编码相关代码（正常）
    )
)

echo.
echo =============================================================================
echo 检查独立运行包编码
echo =============================================================================

if exist dist\README.txt (
    echo 检查 dist/README.txt
    findstr "独立运行包" dist\README.txt > nul
    if %errorlevel% equ 0 (
        echo ✓ dist/README.txt 编码正常
    ) else (
        echo ✗ dist/README.txt 编码异常
    )
) else (
    echo - dist/README.txt 不存在
)

if exist dist\start.bat (
    echo 检查 dist/start.bat
    findstr "chcp 65001" dist\start.bat > nul
    if %errorlevel% equ 0 (
        echo ✓ dist/start.bat 已设置UTF-8编码
    ) else (
        echo ✗ dist/start.bat 未设置UTF-8编码
    )
) else (
    echo - dist/start.bat 不存在
)

echo.
echo =============================================================================
echo 编码检查总结
echo =============================================================================

echo ✅ 编码检查完成
echo.
echo 建议:
echo - 所有批处理文件都应包含 chcp 65001 设置
echo - 前端文件应设置 charset="UTF-8"
echo - Go文件中的HTTP请求应设置 charset=utf-8
echo - 生成的独立运行包应使用UTF-8编码

echo.
echo 检查完成！ 