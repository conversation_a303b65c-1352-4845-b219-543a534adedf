# Security & Privacy Policy

<br>

## Issues

All issues are tracked publicly on GitHub: <https://github.com/vladmandic/human/issues>  

<br>

## Vulnerabilities

`Human` library code base and indluded dependencies are automatically scanned against known security vulnerabilities  
Any code commit is validated before merge  

- [Dependencies](https://github.com/vladmandic/human/security/dependabot)
- [Scann<PERSON> Alerts](https://github.com/vladmandic/human/security/code-scanning)

<br>

## Privacy

`Human` library and included demo apps:

- Are fully self-contained and does not send or share data of any kind with external targets
- Do not store any user or system data tracking, user provided inputs (images, video) or detection results
- Do not utilize any analytic services (such as Google Analytics)

`Human` library can establish external connections *only* for following purposes and *only* when explicitly configured by user:

- Load models from externally hosted site (e.g. CDN)
- Load inputs for detection from *http & https* sources
