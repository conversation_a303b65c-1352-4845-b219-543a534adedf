# GejieSoft PDF Tools - 系统架构流程图

## 整体系统架构流程图

```mermaid
graph TB
    %% 用户交互层
    User[👤 用户] --> WebUI[🌐 Web界面<br/>frontend/index.html]
    
    %% Web服务层
    WebUI --> |上传PPT文件| WebServer[🚀 Web服务器<br/>main/web_server.go]
    WebUI --> |生成PPT请求| WebServer
    WebUI --> |下载请求| WebServer
    
    %% 核心业务流程
    WebServer --> |1. 文件解析| PPTParser[📄 PPT解析器<br/>extractAllTextFromPPT]
    WebServer --> |2. 图片提取| ImageExtractor[🖼️ 图片提取器<br/>extractImagesFromFileWeb]
    
    %% AI处理流程
    PPTParser --> |提取的文本| AIProcessor[🤖 AI处理器<br/>main/post_aibot_api.go]
    AIProcessor --> |API调用| AibotAPI[🔗 Aibot API<br/>aibot.gejiesoft.com]
    AibotAPI --> |结构化数据| ResumeInfo[📋 简历信息<br/>ResumeInfo结构]
    
    %% 图片处理流程
    ImageExtractor --> |原始图片| ImageProcessor[🎨 图片处理器<br/>main/image_processor.go]
    ImageProcessor --> |人像检测| TencentAPI[☁️ 腾讯云API<br/>COS + IAI]
    ImageProcessor --> |图片优化| PhotoEnhancer[✨ 照片增强器<br/>main/photo_enhancer.go]
    
    %% 腾讯云服务
    TencentAPI --> |人脸检测| FaceDetection[👤 人脸检测]
    TencentAPI --> |人像抠图| PortraitMatting[🎭 人像抠图]
    TencentAPI --> |图像超分| ImageSuperRes[📈 图像超分]
    
    %% 处理结果整合
    ResumeInfo --> |文本信息| DataIntegrator[🔄 数据整合器]
    PhotoEnhancer --> |处理后图片| DataIntegrator
    DataIntegrator --> |完整数据| JSONOutput[📄 JSON输出<br/>vc_extracted.json]
    
    %% PPT生成流程
    DataIntegrator --> |生成请求| PPTGenerator[🎯 PPT生成器<br/>main/ppt_generator/]
    PPTGenerator --> |Python脚本| PythonScript[🐍 Python脚本<br/>ppt_generator.py]
    PythonScript --> |模板处理| PPTTemplate[📋 PPT模板<br/>templates-ppt/]
    PythonScript --> |生成PPT| GeneratedPPT[📊 生成的PPT]
    
    %% 存储系统
    JSONOutput --> FileSystem[💾 文件系统<br/>output/]
    GeneratedPPT --> FileSystem
    PhotoEnhancer --> FileSystem
    
    %% 配置管理
    ConfigManager[⚙️ 配置管理<br/>main/config.go] --> WebServer
    ConfigManager --> ImageProcessor
    ConfigManager --> PhotoEnhancer
    
    %% 日志系统
    WebServer --> LogManager[📝 日志管理<br/>LogManager]
    LogManager --> |实时日志| WebUI
    
    %% 样式定义
    classDef userLayer fill:#e1f5fe
    classDef webLayer fill:#f3e5f5
    classDef businessLayer fill:#e8f5e8
    classDef apiLayer fill:#fff3e0
    classDef storageLayer fill:#fce4ec
    classDef configLayer fill:#f1f8e9
    
    class User,WebUI userLayer
    class WebServer,LogManager webLayer
    class PPTParser,ImageExtractor,ImageProcessor,PhotoEnhancer,DataIntegrator,PPTGenerator,PythonScript businessLayer
    class AIProcessor,AibotAPI,TencentAPI,FaceDetection,PortraitMatting,ImageSuperRes apiLayer
    class FileSystem,JSONOutput,GeneratedPPT,PPTTemplate storageLayer
    class ConfigManager configLayer
```

## 详细业务流程图

```mermaid
sequenceDiagram
    participant U as 👤 用户
    participant W as 🌐 Web界面
    participant S as 🚀 Web服务器
    participant P as 📄 PPT解析器
    participant I as 🖼️ 图片提取器
    participant AI as 🤖 AI处理器
    participant T as ☁️ 腾讯云API
    participant IP as 🎨 图片处理器
    participant PE as ✨ 照片增强器
    participant PG as 🎯 PPT生成器
    participant FS as 💾 文件系统
    
    %% 第一阶段：文件上传和解析
    U->>W: 上传PPT文件
    W->>S: POST /api/extract
    S->>FS: 保存临时文件
    S->>P: 解析PPT文本内容
    P-->>S: 返回提取的文本
    S->>I: 提取PPT图片
    I-->>S: 返回图片列表
    I->>FS: 保存图片文件
    
    %% 第二阶段：AI信息提取
    S->>AI: 调用AI API提取简历信息
    AI->>AI: 发送HTTP请求到Aibot API
    AI-->>S: 返回结构化简历数据
    
    %% 第三阶段：图片处理
    S->>IP: 处理提取的图片
    IP->>T: 调用人像检测API
    T-->>IP: 返回检测结果
    
    alt 检测到人像
        IP->>PE: 进行照片增强处理
        PE->>T: 调用人像抠图API
        T-->>PE: 返回抠图结果
        PE->>T: 调用图像超分API（如需要）
        T-->>PE: 返回增强结果
        PE-->>IP: 返回处理后图片
    end
    
    IP-->>S: 返回图片处理统计
    
    %% 第四阶段：数据整合和输出
    S->>S: 整合文本和图片信息
    S->>FS: 保存JSON结果文件
    S-->>W: 返回处理结果
    W-->>U: 显示提取结果和下载链接
    
    %% 第五阶段：PPT生成（可选）
    opt 用户请求生成PPT
        U->>W: 点击生成PPT
        W->>S: POST /api/generate-ppt
        S->>PG: 调用Python PPT生成器
        PG->>PG: 读取JSON数据和PPT模板
        PG->>PG: 填充模板内容
        PG->>FS: 保存生成的PPT
        PG-->>S: 返回生成的PPT路径
        S-->>W: 返回PPT下载链接
        W-->>U: 提供PPT下载
    end
```

## 核心模块架构图

```mermaid
graph LR
    %% 核心模块
    subgraph "🎯 核心业务模块"
        WS[Web服务器<br/>web_server.go]
        IP[图片处理器<br/>image_processor.go]
        PE[照片增强器<br/>photo_enhancer.go]
        AI[AI API调用<br/>post_aibot_api.go]
        TA[模板分析器<br/>template_analyzer.go]
    end
    
    %% 数据层
    subgraph "📊 数据结构层"
        T[类型定义<br/>types.go]
        C[配置管理<br/>config.go]
    end
    
    %% 外部服务
    subgraph "🌐 外部服务"
        TC[腾讯云COS/IAI]
        AB[Aibot API]
    end
    
    %% Python模块
    subgraph "🐍 Python模块"
        PG[PPT生成器<br/>ppt_generator.py]
    end
    
    %% 前端
    subgraph "🎨 前端界面"
        FE[Web界面<br/>frontend/]
    end
    
    %% 连接关系
    WS --> IP
    WS --> AI
    WS --> TA
    WS --> PG
    IP --> PE
    IP --> TC
    AI --> AB
    PE --> TC
    WS --> FE
    WS --> T
    WS --> C
    IP --> T
    PE --> T
    AI --> T
```
