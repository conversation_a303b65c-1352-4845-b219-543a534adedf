{"format": "graph-model", "generatedBy": "https://github.com/HSE-<PERSON><PERSON><PERSON>/HSE_FaceRec_tf", "convertedBy": "https://github.com/vladmandic", "signature": {"inputs": {"input_1": {"name": "input_1", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "224"}, {"size": "224"}, {"size": "3"}]}}}, "outputs": {"gender_pred/Sigmoid:0": {"name": "gender_pred/Sigmoid:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}]}}, "global_pooling/Mean": {"name": "global_pooling/Mean", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1024"}]}}, "age_pred/Softmax:0": {"name": "age_pred/Softmax:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "100"}]}}}}, "modelTopology": {"node": [{"name": "age_pred/kernel", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}, {"size": "100"}]}}}}}, {"name": "age_pred/bias", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "100"}]}}}}}, {"name": "feats/kernel", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1024"}, {"size": "256"}]}}}}}, {"name": "feats/bias", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}}}, {"name": "gender_pred/kernel", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "gender_pred/bias", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "conv1/convolution/merged_input", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "3"}, {"size": "32"}]}}}}}, {"name": "conv1_bn/batchnorm_1/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv1_relu/Cast", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv1_relu/Const_1", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_dw_1/depthwise_kernel", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_1_bn/batchnorm_1/mul", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "conv_dw_1_bn/batchnorm_1/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_1_relu/Cast", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_1_relu/Const_1", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_pw_1/convolution/merged_input", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_1_bn/batchnorm_1/sub", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "conv_pw_1_relu/Cast", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_pw_1_relu/Const_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_2/depthwise_kernel", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "64"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_2_bn/batchnorm_1/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_2_bn/batchnorm_1/sub", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}}}, {"name": "conv_dw_2_relu/Cast", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_dw_2_relu/Const_1", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_pw_2/convolution/merged_input", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "64"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_2_bn/batchnorm_1/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_2_relu/Cast", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_2_relu/Const_1", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_dw_3/depthwise_kernel", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_3_bn/batchnorm_1/mul", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "conv_dw_3_bn/batchnorm_1/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_3_relu/Cast", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_3_relu/Const_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_3/convolution/merged_input", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "128"}]}}}}}, {"name": "conv_pw_3_bn/batchnorm_1/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_3_relu/Cast", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_3_relu/Const_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_4/depthwise_kernel", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "128"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_4_bn/batchnorm_1/mul", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "conv_dw_4_bn/batchnorm_1/sub", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "conv_dw_4_relu/Cast", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_4_relu/Const_1", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_pw_4/convolution/merged_input", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "256"}]}}}}}, {"name": "conv_pw_4_bn/batchnorm_1/sub", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}}}, {"name": "conv_pw_4_relu/Cast", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_pw_4_relu/Const_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_5/depthwise_kernel", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "256"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_5_bn/batchnorm_1/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_5_bn/batchnorm_1/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_5_relu/Cast", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_dw_5_relu/Const_1", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_pw_5/convolution/merged_input", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "256"}, {"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_5_bn/batchnorm_1/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_5_relu/Cast", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_5_relu/Const_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_6/depthwise_kernel", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "256"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_6_bn/batchnorm_1/mul", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}}}, {"name": "conv_dw_6_bn/batchnorm_1/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "256"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_6_relu/Cast", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_6_relu/Const_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_6/convolution/merged_input", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "256"}, {"size": "512"}]}}}}}, {"name": "conv_pw_6_bn/batchnorm_1/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_6_relu/Cast", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_6_relu/Const_1", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_dw_7/depthwise_kernel", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "512"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_7_bn/batchnorm_1/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_7_bn/batchnorm_1/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_7_relu/Cast", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_7_relu/Const_1", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_pw_7/convolution/merged_input", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "512"}, {"size": "512"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_7_bn/batchnorm_1/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_7_relu/Cast", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_pw_7_relu/Const_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_8/depthwise_kernel", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "512"}, {"size": "1"}]}}}}}, {"name": "conv_dw_8_bn/batchnorm_1/mul", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}]}}}}}, {"name": "conv_dw_8_bn/batchnorm_1/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_8_relu/Cast", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_8_relu/Const_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_8/convolution/merged_input", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "512"}, {"size": "512"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_8_bn/batchnorm_1/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_8_relu/Cast", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_pw_8_relu/Const_1", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_dw_9/depthwise_kernel", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "512"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_9_bn/batchnorm_1/mul", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}]}}}}}, {"name": "conv_dw_9_bn/batchnorm_1/sub", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}]}}}}}, {"name": "conv_dw_9_relu/Cast", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_9_relu/Const_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_9/convolution/merged_input", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "512"}, {"size": "512"}]}}}}}, {"name": "conv_pw_9_bn/batchnorm_1/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_9_relu/Cast", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_pw_9_relu/Const_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_10/depthwise_kernel", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "512"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_10_bn/batchnorm_1/mul", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}]}}}}}, {"name": "conv_dw_10_bn/batchnorm_1/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_10_relu/Cast", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_10_relu/Const_1", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_pw_10/convolution/merged_input", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "512"}, {"size": "512"}]}}}}}, {"name": "conv_pw_10_bn/batchnorm_1/sub", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}]}}}}}, {"name": "conv_pw_10_relu/Cast", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_10_relu/Const_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_11/depthwise_kernel", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "512"}, {"size": "1"}]}}}}}, {"name": "conv_dw_11_bn/batchnorm_1/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_11_bn/batchnorm_1/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_11_relu/Cast", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_11_relu/Const_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_11/convolution/merged_input", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "512"}, {"size": "512"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_11_bn/batchnorm_1/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_11_relu/Cast", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_pw_11_relu/Const_1", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_dw_12/depthwise_kernel", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "512"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_12_bn/batchnorm_1/mul", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}]}}}}}, {"name": "conv_dw_12_bn/batchnorm_1/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "512"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_12_relu/Cast", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_dw_12_relu/Const_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_12/convolution/merged_input", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "512"}, {"size": "1024"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_12_bn/batchnorm_1/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1024"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_12_relu/Cast", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_12_relu/Const_1", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_dw_13/depthwise_kernel", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "1024"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_13_bn/batchnorm_1/mul", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1024"}]}}}}}, {"name": "conv_dw_13_bn/batchnorm_1/sub", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1024"}]}}}}}, {"name": "conv_dw_13_relu/Cast", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "conv_dw_13_relu/Const_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_13/convolution/merged_input", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "1024"}, {"size": "1024"}]}}}}}, {"name": "conv_pw_13_bn/batchnorm_1/sub", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1024"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_13_relu/Cast", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_13_relu/Const_1", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "global_pooling/Mean/reduction_indices", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "input_1", "op": "Placeholder", "attr": {"shape": {"shape": {"dim": [{"size": "-1"}, {"size": "224"}, {"size": "224"}, {"size": "3"}]}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "conv1_bn/batchnorm_1/mul_1", "op": "Conv2D", "input": ["input_1", "conv1/convolution/merged_input"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "2", "2", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "conv1_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv1_bn/batchnorm_1/mul_1", "conv1_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv1_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv1_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv1_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv1_relu/Relu", "conv1_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv1_relu/clip_by_value", "op": "Maximum", "input": ["conv1_relu/clip_by_value/Minimum", "conv1_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_1/depthwise", "op": "DepthwiseConv2dNative", "input": ["conv1_relu/clip_by_value", "conv_dw_1/depthwise_kernel"], "attr": {"T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "conv_dw_1_bn/batchnorm_1/mul_1", "op": "<PERSON><PERSON>", "input": ["conv_dw_1/depthwise", "conv_dw_1_bn/batchnorm_1/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_1_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_dw_1_bn/batchnorm_1/mul_1", "conv_dw_1_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_1_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_dw_1_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_1_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_dw_1_relu/Relu", "conv_dw_1_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_1_relu/clip_by_value", "op": "Maximum", "input": ["conv_dw_1_relu/clip_by_value/Minimum", "conv_dw_1_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_1_bn/batchnorm_1/mul_1", "op": "Conv2D", "input": ["conv_dw_1_relu/clip_by_value", "conv_pw_1/convolution/merged_input"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}}}, {"name": "conv_pw_1_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_pw_1_bn/batchnorm_1/mul_1", "conv_pw_1_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_1_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_pw_1_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_1_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_pw_1_relu/Relu", "conv_pw_1_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_1_relu/clip_by_value", "op": "Maximum", "input": ["conv_pw_1_relu/clip_by_value/Minimum", "conv_pw_1_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_2/depthwise", "op": "DepthwiseConv2dNative", "input": ["conv_pw_1_relu/clip_by_value", "conv_dw_2/depthwise_kernel"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "conv_dw_2_bn/batchnorm_1/mul_1", "op": "<PERSON><PERSON>", "input": ["conv_dw_2/depthwise", "conv_dw_2_bn/batchnorm_1/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_2_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_dw_2_bn/batchnorm_1/mul_1", "conv_dw_2_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_2_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_dw_2_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_2_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_dw_2_relu/Relu", "conv_dw_2_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_2_relu/clip_by_value", "op": "Maximum", "input": ["conv_dw_2_relu/clip_by_value/Minimum", "conv_dw_2_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_2_bn/batchnorm_1/mul_1", "op": "Conv2D", "input": ["conv_dw_2_relu/clip_by_value", "conv_pw_2/convolution/merged_input"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "conv_pw_2_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_pw_2_bn/batchnorm_1/mul_1", "conv_pw_2_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_2_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_pw_2_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_2_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_pw_2_relu/Relu", "conv_pw_2_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_2_relu/clip_by_value", "op": "Maximum", "input": ["conv_pw_2_relu/clip_by_value/Minimum", "conv_pw_2_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_3/depthwise", "op": "DepthwiseConv2dNative", "input": ["conv_pw_2_relu/clip_by_value", "conv_dw_3/depthwise_kernel"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}}}, {"name": "conv_dw_3_bn/batchnorm_1/mul_1", "op": "<PERSON><PERSON>", "input": ["conv_dw_3/depthwise", "conv_dw_3_bn/batchnorm_1/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_3_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_dw_3_bn/batchnorm_1/mul_1", "conv_dw_3_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_3_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_dw_3_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_3_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_dw_3_relu/Relu", "conv_dw_3_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_3_relu/clip_by_value", "op": "Maximum", "input": ["conv_dw_3_relu/clip_by_value/Minimum", "conv_dw_3_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_3_bn/batchnorm_1/mul_1", "op": "Conv2D", "input": ["conv_dw_3_relu/clip_by_value", "conv_pw_3/convolution/merged_input"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "conv_pw_3_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_pw_3_bn/batchnorm_1/mul_1", "conv_pw_3_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_3_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_pw_3_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_3_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_pw_3_relu/Relu", "conv_pw_3_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_3_relu/clip_by_value", "op": "Maximum", "input": ["conv_pw_3_relu/clip_by_value/Minimum", "conv_pw_3_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_4/depthwise", "op": "DepthwiseConv2dNative", "input": ["conv_pw_3_relu/clip_by_value", "conv_dw_4/depthwise_kernel"], "attr": {"explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}}}, {"name": "conv_dw_4_bn/batchnorm_1/mul_1", "op": "<PERSON><PERSON>", "input": ["conv_dw_4/depthwise", "conv_dw_4_bn/batchnorm_1/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_4_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_dw_4_bn/batchnorm_1/mul_1", "conv_dw_4_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_4_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_dw_4_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_4_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_dw_4_relu/Relu", "conv_dw_4_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_4_relu/clip_by_value", "op": "Maximum", "input": ["conv_dw_4_relu/clip_by_value/Minimum", "conv_dw_4_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_4_bn/batchnorm_1/mul_1", "op": "Conv2D", "input": ["conv_dw_4_relu/clip_by_value", "conv_pw_4/convolution/merged_input"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_4_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_pw_4_bn/batchnorm_1/mul_1", "conv_pw_4_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_4_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_pw_4_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_4_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_pw_4_relu/Relu", "conv_pw_4_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_4_relu/clip_by_value", "op": "Maximum", "input": ["conv_pw_4_relu/clip_by_value/Minimum", "conv_pw_4_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_5/depthwise", "op": "DepthwiseConv2dNative", "input": ["conv_pw_4_relu/clip_by_value", "conv_dw_5/depthwise_kernel"], "attr": {"explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}}}, {"name": "conv_dw_5_bn/batchnorm_1/mul_1", "op": "<PERSON><PERSON>", "input": ["conv_dw_5/depthwise", "conv_dw_5_bn/batchnorm_1/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_5_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_dw_5_bn/batchnorm_1/mul_1", "conv_dw_5_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_5_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_dw_5_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_5_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_dw_5_relu/Relu", "conv_dw_5_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_5_relu/clip_by_value", "op": "Maximum", "input": ["conv_dw_5_relu/clip_by_value/Minimum", "conv_dw_5_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_5_bn/batchnorm_1/mul_1", "op": "Conv2D", "input": ["conv_dw_5_relu/clip_by_value", "conv_pw_5/convolution/merged_input"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "explicit_paddings": {"list": {}}}}, {"name": "conv_pw_5_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_pw_5_bn/batchnorm_1/mul_1", "conv_pw_5_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_5_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_pw_5_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_5_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_pw_5_relu/Relu", "conv_pw_5_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_5_relu/clip_by_value", "op": "Maximum", "input": ["conv_pw_5_relu/clip_by_value/Minimum", "conv_pw_5_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_6/depthwise", "op": "DepthwiseConv2dNative", "input": ["conv_pw_5_relu/clip_by_value", "conv_dw_6/depthwise_kernel"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}}}, {"name": "conv_dw_6_bn/batchnorm_1/mul_1", "op": "<PERSON><PERSON>", "input": ["conv_dw_6/depthwise", "conv_dw_6_bn/batchnorm_1/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_6_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_dw_6_bn/batchnorm_1/mul_1", "conv_dw_6_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_6_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_dw_6_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_6_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_dw_6_relu/Relu", "conv_dw_6_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_6_relu/clip_by_value", "op": "Maximum", "input": ["conv_dw_6_relu/clip_by_value/Minimum", "conv_dw_6_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_6_bn/batchnorm_1/mul_1", "op": "Conv2D", "input": ["conv_dw_6_relu/clip_by_value", "conv_pw_6/convolution/merged_input"], "device": "/device:CPU:0", "attr": {"padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "conv_pw_6_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_pw_6_bn/batchnorm_1/mul_1", "conv_pw_6_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_6_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_pw_6_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_6_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_pw_6_relu/Relu", "conv_pw_6_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_6_relu/clip_by_value", "op": "Maximum", "input": ["conv_pw_6_relu/clip_by_value/Minimum", "conv_pw_6_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_7/depthwise", "op": "DepthwiseConv2dNative", "input": ["conv_pw_6_relu/clip_by_value", "conv_dw_7/depthwise_kernel"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}}}, {"name": "conv_dw_7_bn/batchnorm_1/mul_1", "op": "<PERSON><PERSON>", "input": ["conv_dw_7/depthwise", "conv_dw_7_bn/batchnorm_1/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_7_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_dw_7_bn/batchnorm_1/mul_1", "conv_dw_7_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_7_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_dw_7_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_7_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_dw_7_relu/Relu", "conv_dw_7_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_7_relu/clip_by_value", "op": "Maximum", "input": ["conv_dw_7_relu/clip_by_value/Minimum", "conv_dw_7_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_7_bn/batchnorm_1/mul_1", "op": "Conv2D", "input": ["conv_dw_7_relu/clip_by_value", "conv_pw_7/convolution/merged_input"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "conv_pw_7_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_pw_7_bn/batchnorm_1/mul_1", "conv_pw_7_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_7_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_pw_7_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_7_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_pw_7_relu/Relu", "conv_pw_7_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_7_relu/clip_by_value", "op": "Maximum", "input": ["conv_pw_7_relu/clip_by_value/Minimum", "conv_pw_7_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_8/depthwise", "op": "DepthwiseConv2dNative", "input": ["conv_pw_7_relu/clip_by_value", "conv_dw_8/depthwise_kernel"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "conv_dw_8_bn/batchnorm_1/mul_1", "op": "<PERSON><PERSON>", "input": ["conv_dw_8/depthwise", "conv_dw_8_bn/batchnorm_1/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_8_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_dw_8_bn/batchnorm_1/mul_1", "conv_dw_8_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_8_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_dw_8_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_8_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_dw_8_relu/Relu", "conv_dw_8_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_8_relu/clip_by_value", "op": "Maximum", "input": ["conv_dw_8_relu/clip_by_value/Minimum", "conv_dw_8_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_8_bn/batchnorm_1/mul_1", "op": "Conv2D", "input": ["conv_dw_8_relu/clip_by_value", "conv_pw_8/convolution/merged_input"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "conv_pw_8_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_pw_8_bn/batchnorm_1/mul_1", "conv_pw_8_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_8_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_pw_8_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_8_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_pw_8_relu/Relu", "conv_pw_8_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_8_relu/clip_by_value", "op": "Maximum", "input": ["conv_pw_8_relu/clip_by_value/Minimum", "conv_pw_8_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_9/depthwise", "op": "DepthwiseConv2dNative", "input": ["conv_pw_8_relu/clip_by_value", "conv_dw_9/depthwise_kernel"], "attr": {"explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "conv_dw_9_bn/batchnorm_1/mul_1", "op": "<PERSON><PERSON>", "input": ["conv_dw_9/depthwise", "conv_dw_9_bn/batchnorm_1/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_9_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_dw_9_bn/batchnorm_1/mul_1", "conv_dw_9_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_9_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_dw_9_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_9_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_dw_9_relu/Relu", "conv_dw_9_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_9_relu/clip_by_value", "op": "Maximum", "input": ["conv_dw_9_relu/clip_by_value/Minimum", "conv_dw_9_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_9_bn/batchnorm_1/mul_1", "op": "Conv2D", "input": ["conv_dw_9_relu/clip_by_value", "conv_pw_9/convolution/merged_input"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "conv_pw_9_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_pw_9_bn/batchnorm_1/mul_1", "conv_pw_9_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_9_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_pw_9_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_9_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_pw_9_relu/Relu", "conv_pw_9_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_9_relu/clip_by_value", "op": "Maximum", "input": ["conv_pw_9_relu/clip_by_value/Minimum", "conv_pw_9_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_10/depthwise", "op": "DepthwiseConv2dNative", "input": ["conv_pw_9_relu/clip_by_value", "conv_dw_10/depthwise_kernel"], "attr": {"data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "conv_dw_10_bn/batchnorm_1/mul_1", "op": "<PERSON><PERSON>", "input": ["conv_dw_10/depthwise", "conv_dw_10_bn/batchnorm_1/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_10_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_dw_10_bn/batchnorm_1/mul_1", "conv_dw_10_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_10_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_dw_10_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_10_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_dw_10_relu/Relu", "conv_dw_10_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_10_relu/clip_by_value", "op": "Maximum", "input": ["conv_dw_10_relu/clip_by_value/Minimum", "conv_dw_10_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_10_bn/batchnorm_1/mul_1", "op": "Conv2D", "input": ["conv_dw_10_relu/clip_by_value", "conv_pw_10/convolution/merged_input"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "conv_pw_10_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_pw_10_bn/batchnorm_1/mul_1", "conv_pw_10_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_10_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_pw_10_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_10_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_pw_10_relu/Relu", "conv_pw_10_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_10_relu/clip_by_value", "op": "Maximum", "input": ["conv_pw_10_relu/clip_by_value/Minimum", "conv_pw_10_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_11/depthwise", "op": "DepthwiseConv2dNative", "input": ["conv_pw_10_relu/clip_by_value", "conv_dw_11/depthwise_kernel"], "attr": {"padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "conv_dw_11_bn/batchnorm_1/mul_1", "op": "<PERSON><PERSON>", "input": ["conv_dw_11/depthwise", "conv_dw_11_bn/batchnorm_1/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_11_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_dw_11_bn/batchnorm_1/mul_1", "conv_dw_11_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_11_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_dw_11_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_11_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_dw_11_relu/Relu", "conv_dw_11_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_11_relu/clip_by_value", "op": "Maximum", "input": ["conv_dw_11_relu/clip_by_value/Minimum", "conv_dw_11_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_11_bn/batchnorm_1/mul_1", "op": "Conv2D", "input": ["conv_dw_11_relu/clip_by_value", "conv_pw_11/convolution/merged_input"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "conv_pw_11_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_pw_11_bn/batchnorm_1/mul_1", "conv_pw_11_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_11_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_pw_11_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_11_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_pw_11_relu/Relu", "conv_pw_11_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_11_relu/clip_by_value", "op": "Maximum", "input": ["conv_pw_11_relu/clip_by_value/Minimum", "conv_pw_11_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_12/depthwise", "op": "DepthwiseConv2dNative", "input": ["conv_pw_11_relu/clip_by_value", "conv_dw_12/depthwise_kernel"], "attr": {"explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "conv_dw_12_bn/batchnorm_1/mul_1", "op": "<PERSON><PERSON>", "input": ["conv_dw_12/depthwise", "conv_dw_12_bn/batchnorm_1/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_12_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_dw_12_bn/batchnorm_1/mul_1", "conv_dw_12_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_12_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_dw_12_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_12_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_dw_12_relu/Relu", "conv_dw_12_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_12_relu/clip_by_value", "op": "Maximum", "input": ["conv_dw_12_relu/clip_by_value/Minimum", "conv_dw_12_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_12_bn/batchnorm_1/mul_1", "op": "Conv2D", "input": ["conv_dw_12_relu/clip_by_value", "conv_pw_12/convolution/merged_input"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}}}, {"name": "conv_pw_12_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_pw_12_bn/batchnorm_1/mul_1", "conv_pw_12_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_12_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_pw_12_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_12_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_pw_12_relu/Relu", "conv_pw_12_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_12_relu/clip_by_value", "op": "Maximum", "input": ["conv_pw_12_relu/clip_by_value/Minimum", "conv_pw_12_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_13/depthwise", "op": "DepthwiseConv2dNative", "input": ["conv_pw_12_relu/clip_by_value", "conv_dw_13/depthwise_kernel"], "attr": {"T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "conv_dw_13_bn/batchnorm_1/mul_1", "op": "<PERSON><PERSON>", "input": ["conv_dw_13/depthwise", "conv_dw_13_bn/batchnorm_1/mul"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_13_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_dw_13_bn/batchnorm_1/mul_1", "conv_dw_13_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_13_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_dw_13_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_13_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_dw_13_relu/Relu", "conv_dw_13_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_dw_13_relu/clip_by_value", "op": "Maximum", "input": ["conv_dw_13_relu/clip_by_value/Minimum", "conv_dw_13_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_13_bn/batchnorm_1/mul_1", "op": "Conv2D", "input": ["conv_dw_13_relu/clip_by_value", "conv_pw_13/convolution/merged_input"], "device": "/device:CPU:0", "attr": {"padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "conv_pw_13_bn/batchnorm_1/add_1", "op": "Add", "input": ["conv_pw_13_bn/batchnorm_1/mul_1", "conv_pw_13_bn/batchnorm_1/sub"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_13_relu/Relu", "op": "<PERSON><PERSON>", "input": ["conv_pw_13_bn/batchnorm_1/add_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_13_relu/clip_by_value/Minimum", "op": "Minimum", "input": ["conv_pw_13_relu/Relu", "conv_pw_13_relu/Cast"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "conv_pw_13_relu/clip_by_value", "op": "Maximum", "input": ["conv_pw_13_relu/clip_by_value/Minimum", "conv_pw_13_relu/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "global_pooling/Mean", "op": "Mean", "input": ["conv_pw_13_relu/clip_by_value", "global_pooling/Mean/reduction_indices"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}, "keep_dims": {"b": false}}}, {"name": "feats/Relu", "op": "_FusedMatMul", "input": ["global_pooling/Mean", "feats/kernel", "feats/bias"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "num_args": {"i": "1"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "gender_pred/BiasAdd", "op": "_FusedMatMul", "input": ["feats/Relu", "gender_pred/kernel", "gender_pred/bias"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "transpose_a": {"b": false}}}, {"name": "age_pred/BiasAdd", "op": "_FusedMatMul", "input": ["feats/Relu", "age_pred/kernel", "age_pred/bias"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "transpose_b": {"b": false}, "num_args": {"i": "1"}, "transpose_a": {"b": false}}}, {"name": "gender_pred/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["gender_pred/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "age_pred/Softmax", "op": "Softmax", "input": ["age_pred/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}], "library": {}, "versions": {}}, "weightsManifest": [{"paths": ["faceres.bin"], "weights": [{"name": "age_pred/kernel", "shape": [256, 100], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "age_pred/bias", "shape": [100], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "feats/kernel", "shape": [1024, 256], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "feats/bias", "shape": [256], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "gender_pred/kernel", "shape": [256, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "gender_pred/bias", "shape": [1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv1/convolution/merged_input", "shape": [3, 3, 3, 32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv1_bn/batchnorm_1/sub", "shape": [32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv1_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv1_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_1/depthwise_kernel", "shape": [3, 3, 32, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_1_bn/batchnorm_1/mul", "shape": [32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_1_bn/batchnorm_1/sub", "shape": [32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_1_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_1_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_1/convolution/merged_input", "shape": [1, 1, 32, 64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_1_bn/batchnorm_1/sub", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_1_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_1_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_2/depthwise_kernel", "shape": [3, 3, 64, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_2_bn/batchnorm_1/mul", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_2_bn/batchnorm_1/sub", "shape": [64], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_2_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_2_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_2/convolution/merged_input", "shape": [1, 1, 64, 128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_2_bn/batchnorm_1/sub", "shape": [128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_2_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_2_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_3/depthwise_kernel", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_3_bn/batchnorm_1/mul", "shape": [128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_3_bn/batchnorm_1/sub", "shape": [128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_3_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_3_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_3/convolution/merged_input", "shape": [1, 1, 128, 128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_3_bn/batchnorm_1/sub", "shape": [128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_3_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_3_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_4/depthwise_kernel", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_4_bn/batchnorm_1/mul", "shape": [128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_4_bn/batchnorm_1/sub", "shape": [128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_4_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_4_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_4/convolution/merged_input", "shape": [1, 1, 128, 256], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_4_bn/batchnorm_1/sub", "shape": [256], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_4_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_4_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_5/depthwise_kernel", "shape": [3, 3, 256, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_5_bn/batchnorm_1/mul", "shape": [256], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_5_bn/batchnorm_1/sub", "shape": [256], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_5_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_5_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_5/convolution/merged_input", "shape": [1, 1, 256, 256], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_5_bn/batchnorm_1/sub", "shape": [256], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_5_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_5_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_6/depthwise_kernel", "shape": [3, 3, 256, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_6_bn/batchnorm_1/mul", "shape": [256], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_6_bn/batchnorm_1/sub", "shape": [256], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_6_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_6_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_6/convolution/merged_input", "shape": [1, 1, 256, 512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_6_bn/batchnorm_1/sub", "shape": [512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_6_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_6_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_7/depthwise_kernel", "shape": [3, 3, 512, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_7_bn/batchnorm_1/mul", "shape": [512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_7_bn/batchnorm_1/sub", "shape": [512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_7_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_7_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_7/convolution/merged_input", "shape": [1, 1, 512, 512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_7_bn/batchnorm_1/sub", "shape": [512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_7_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_7_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_8/depthwise_kernel", "shape": [3, 3, 512, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_8_bn/batchnorm_1/mul", "shape": [512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_8_bn/batchnorm_1/sub", "shape": [512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_8_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_8_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_8/convolution/merged_input", "shape": [1, 1, 512, 512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_8_bn/batchnorm_1/sub", "shape": [512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_8_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_8_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_9/depthwise_kernel", "shape": [3, 3, 512, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_9_bn/batchnorm_1/mul", "shape": [512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_9_bn/batchnorm_1/sub", "shape": [512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_9_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_9_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_9/convolution/merged_input", "shape": [1, 1, 512, 512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_9_bn/batchnorm_1/sub", "shape": [512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_9_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_9_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_10/depthwise_kernel", "shape": [3, 3, 512, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_10_bn/batchnorm_1/mul", "shape": [512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_10_bn/batchnorm_1/sub", "shape": [512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_10_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_10_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_10/convolution/merged_input", "shape": [1, 1, 512, 512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_10_bn/batchnorm_1/sub", "shape": [512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_10_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_10_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_11/depthwise_kernel", "shape": [3, 3, 512, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_11_bn/batchnorm_1/mul", "shape": [512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_11_bn/batchnorm_1/sub", "shape": [512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_11_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_11_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_11/convolution/merged_input", "shape": [1, 1, 512, 512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_11_bn/batchnorm_1/sub", "shape": [512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_11_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_11_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_12/depthwise_kernel", "shape": [3, 3, 512, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_12_bn/batchnorm_1/mul", "shape": [512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_12_bn/batchnorm_1/sub", "shape": [512], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_12_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_12_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_12/convolution/merged_input", "shape": [1, 1, 512, 1024], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_12_bn/batchnorm_1/sub", "shape": [1024], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_12_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_12_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_13/depthwise_kernel", "shape": [3, 3, 1024, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_13_bn/batchnorm_1/mul", "shape": [1024], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_13_bn/batchnorm_1/sub", "shape": [1024], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_13_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_dw_13_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_13/convolution/merged_input", "shape": [1, 1, 1024, 1024], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_13_bn/batchnorm_1/sub", "shape": [1024], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_13_relu/Cast", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "conv_pw_13_relu/Const_1", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "global_pooling/Mean/reduction_indices", "shape": [2], "dtype": "int32"}]}]}