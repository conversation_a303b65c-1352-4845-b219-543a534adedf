# 🎯 面部对称轴居中算法说明

## 🔍 问题理解

用户需求：
- **T字形对称**: 两只眼睛、鼻子、嘴巴形成字母"T"的形状
- **竖线居中**: T字的竖线（面部对称轴）需要垂直居中
- **允许空白**: 即使左侧产生空白也要保持居中
- **背景去除**: 最终会去除背景，空白不明显

## 🧠 算法原理

### 传统方法的问题
```javascript
// 旧方法：使用人脸框中心
const faceCenterX = faceBox[0] + faceWidth / 2;  // 简单的几何中心
```
**问题**: 人脸框是矩形，但人脸可能在框内偏左或偏右

### 新方法：面部对称轴定位
```javascript
// 新方法：使用468点面部关键点
const keyPoints = {
    noseTip: face.mesh[1] || face.mesh[2],      // 鼻尖
    browCenter: face.mesh[9] || face.mesh[10],  // 眉心  
    chinCenter: face.mesh[175],                 // 下巴中心
    upperLip: face.mesh[13],                    // 上唇中心
    lowerLip: face.mesh[14]                     // 下唇中心
};
```

## 🎯 关键点选择

### MediaPipe FaceMesh 468点模型
Human.js使用MediaPipe的468点面部关键点模型：

#### 选择的关键点：
1. **鼻尖** (索引1, 2): 面部最突出的中心点
2. **眉心** (索引9, 10): 两眉之间的中心
3. **下巴中心** (索引175): 下巴的最低点
4. **上唇中心** (索引13): 上嘴唇的中心
5. **下唇中心** (索引14): 下嘴唇的中心

#### 为什么选择这些点：
- ✅ **对称性**: 这些点都位于面部中轴线上
- ✅ **稳定性**: 不受表情变化影响太大
- ✅ **准确性**: MediaPipe训练数据保证了这些点的精确性
- ✅ **完整性**: 覆盖了从眉心到下巴的完整面部轮廓

## 🔧 算法实现

### 步骤1: 提取对称轴关键点
```javascript
const symmetryPoints = [];
if (keyPoints.noseTip) symmetryPoints.push(keyPoints.noseTip[0]);
if (keyPoints.browCenter) symmetryPoints.push(keyPoints.browCenter[0]);
if (keyPoints.chinCenter) symmetryPoints.push(keyPoints.chinCenter[0]);
if (keyPoints.upperLip) symmetryPoints.push(keyPoints.upperLip[0]);
if (keyPoints.lowerLip) symmetryPoints.push(keyPoints.lowerLip[0]);
```

### 步骤2: 计算对称轴X坐标
```javascript
faceSymmetryX = symmetryPoints.reduce((sum, x) => sum + x, 0) / symmetryPoints.length;
```
**优势**: 多点平均，减少单点误差

### 步骤3: 以对称轴为基准裁切
```javascript
let cropX = faceSymmetryX - cropSize / 2;  // 以对称轴为中心
let cropY = faceCenterY - cropSize / 2;    // 垂直居中
```

## 📊 效果对比

### 旧算法效果：
- ❌ 使用人脸框几何中心
- ❌ 人脸偏左时，输出仍然偏左
- ❌ 不考虑面部特征的实际分布

### 新算法效果：
- ✅ 使用面部关键点计算真实对称轴
- ✅ 无论人脸在原图中的位置，T字竖线都居中
- ✅ 即使产生左右空白，也保持对称轴居中
- ✅ 适合后续背景去除处理

## 🛡️ 容错机制

### 备用方案
```javascript
if (symmetryPoints.length > 0) {
    faceSymmetryX = symmetryPoints.reduce((sum, x) => sum + x, 0) / symmetryPoints.length;
} else {
    // 备用方案：使用人脸框中心
    faceSymmetryX = face.box[0] + face.box[2] / 2;
}
```

### 边界处理
```javascript
// 保持对称轴居中，即使在边界情况下
cropX = Math.max(0, faceSymmetryX - finalCropSize / 2);
```

## 🎯 预期效果

### 对于 1739238272_569242.jpg：
- **原问题**: 人脸在原图中偏左，处理后仍偏左
- **新效果**: 
  - 检测到鼻子、眉心、嘴巴的精确位置
  - 计算这些点形成的对称轴
  - 以对称轴为基准进行垂直居中
  - 输出图像中T字竖线完美居中

### 通用效果：
- ✅ **T字竖线居中**: 眼睛-鼻子-嘴巴的对称轴垂直居中
- ✅ **一致性**: 所有照片的面部对称轴都在同一位置
- ✅ **自然效果**: 符合人眼对面部对称的感知
- ✅ **背景友好**: 为后续背景去除创造最佳条件

## 🧪 测试建议

### 测试用例：
1. **偏左人脸**: 如 1739238272_569242.jpg
2. **偏右人脸**: 测试右偏照片
3. **侧脸**: 测试轻微侧脸角度
4. **表情变化**: 测试微笑、严肃等表情
5. **不同角度**: 测试轻微仰头、低头

### 验证标准：
- 输出图像中鼻子应该在水平中心线上
- 眉心到下巴的连线应该垂直居中
- 左右眼睛应该关于中心线对称
- 嘴巴中心应该在垂直中心线上

## 🔧 微调参数

如果需要进一步调整：

### 调整人脸大小：
```javascript
const targetFaceWidthRatio = 0.6;  // 改为0.5或0.7
```

### 调整关键点权重：
```javascript
// 可以给不同关键点不同权重
const weightedSymmetryX = (
    keyPoints.noseTip[0] * 2 +      // 鼻尖权重更高
    keyPoints.browCenter[0] * 1 +
    keyPoints.chinCenter[0] * 1 +
    keyPoints.upperLip[0] * 1.5 +   // 嘴部权重较高
    keyPoints.lowerLip[0] * 1.5
) / 7;
```

现在请测试新的面部对称轴居中算法！
