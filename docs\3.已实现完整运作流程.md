# 已实现完整运作流程 v2.0

## 🏗️ 项目整体架构

这是一个**智能PPT信息提取与生成工具**，采用前后端分离架构，支持从PPT文件中提取简历信息，进行人像抠图处理，并自动生成定制化PPT。

### 技术栈
- **后端**: Go语言 + Gin框架
- **前端**: HTML + JavaScript + Tailwind CSS + Font Awesome
- **PPT生成**: Python + python-pptx库
- **部署**: Docker容器化支持
- **AI服务**: 腾讯云人脸识别/COS API + 自定义Aibot API
- **实时通信**: Server-Sent Events (SSE) 日志流

## 🔧 核心功能模块

### 1. **主程序入口模块** (`main.go`)
**功能**：
- 程序启动入口
- 仅负责启动Web服务器，保持简洁

### 2. **Web服务器模块** (`web_server.go`)
**功能**：
- 提供RESTful API服务
- 文件上传处理和验证
- 实时日志流管理（SSE）
- 静态文件服务
- CORS跨域支持
- 调试模式支持

**主要API端点**：
- `POST /api/extract` - PPT文件上传并提取信息（支持调试模式）
- `POST /api/download-images` - 批量下载所有处理后的图片（ZIP格式）
- `GET /api/image/*filepath` - 访问/获取指定路径的图片资源
- `GET /api/log` - 实时日志流（Server-Sent Events）
- `GET /api/download-ppt/*filepath` - 下载生成的PPT文件
- `POST /api/generate-ppt` - 根据提取的数据生成定制化PPT文件

### 3. **图片处理模块** (`image_processor.go`)
**功能**：
- 腾讯云COS集成和配置验证
- 人像检测（使用腾讯云COS人脸检测API）
- 图片质量检查和优化
- 智能图片压缩（支持大小和DPI检查）
- 图片分类和筛选
- 处理统计信息收集和报告生成
- 支持本地和Docker环境自适应

**新增特性**：
- 智能图片大小优化（压缩至API允许的最大尺寸）
- DPI质量检查和警告系统
- 图片质量评估和警告生成
- 相对路径转换，支持跨平台兼容

### 4. **照片增强模块** (`photo_enhancer.go`)
**功能**：
- 人像背景分割（使用腾讯云COS和IAI API）
- 图片压缩和优化（针对API调用限制）
- 标准照片生成
- 头肩部区域智能裁剪
- 人脸检测和定位
- 支持多种图片格式输出

**新增特性**：
- 智能压缩算法，自动调整图片大小
- 估算DPI功能
- 支持PNG透明背景输出
- 头肩部区域智能识别和裁剪

### 5. **AI API调用模块** (`post_aibot_api.go`)
**功能**：
- 调用自定义Aibot API (`https://aibot.gejiesoft.com/v1/chat-messages`)
- 结构化简历信息提取
- 请求重试机制（最多3次重试）
- 多语言文本处理支持
- JSON响应解析和验证

**数据结构优化**：
- 支持嵌套简历信息字段
- 自动解析多行文本为列表
- 智能文本清理和格式化

### 6. **模板分析模块** (`template_analyzer.go`)
**功能**：
- 读取和解析PPTX模板文件
- 智能占位符识别（支持完整模式 `{{placeholder}}` 和分割模式）
- 文本和图片占位符提取
- 幻灯片结构分析
- 模板映射配置生成
- XML内容解析和处理

### 7. **PPT生成模块** (`main/ppt_generator/ppt_generator.py`)
**功能**：
- 基于Python实现的PPT生成引擎
- 支持文本占位符智能填充
- 图片自动插入和调整
- 自适应字体大小（根据文本长度动态调整）
- 统一行间距控制（1.2倍行距）
- 支持本地和Docker环境路径自动检测

**新增特性**：
- 智能文本适配算法
- 多行文本处理优化
- 头像图片自动定位和插入
- 环境自适应路径解析

### 8. **配置管理模块** (`config.go`)
**功能**：
- 环境变量优先配置加载
- JSON配置文件支持
- Docker环境自动检测
- 配置验证和默认值设置
- 照片增强参数动态配置
- 全局配置实例管理

**新增配置项**：
- 图片大小和DPI检查开关
- 智能压缩参数配置
- 质量优先级设置
- API调用超时配置

### 9. **前端界面模块** (`frontend/`)
**功能**：
- 现代化响应式界面设计
- 文件拖拽上传支持
- 实时处理日志显示（WebSocket连接）
- 结果展示和下载管理
- Font Awesome图标支持
- Tailwind CSS样式框架

### 10. **数据结构定义模块** (`types.go`)
**核心数据结构**：
- `ResumeInfo` - 简历信息结构（支持详细分类字段）
- `ProcessingStats` - 图片处理统计
- `ImageResult` - 单张图片处理结果
- `ImageCompressionInfo` - 图片压缩信息
- `PPTGenerationRequest/Result` - PPT生成请求/结果
- `TemplateMapping` - 模板映射配置

## 🔄 完整运作流程

### 第一阶段：文件上传和初始化
1. **前端文件验证** → 检查PPT文件格式和大小
2. **后端接收处理** → 创建临时工作目录
3. **文件保存** → 保存到安全的临时位置
4. **实时日志推送** → 通过SSE向前端推送状态更新

### 第二阶段：PPT内容解析和图片提取
1. **PPT文件解析** → 使用pptx库读取PPT结构
2. **文本内容提取** → 提取所有文本内容并清理格式
3. **图片自动提取** → 提取所有图片资源到输出目录
4. **文件组织** → 按时间戳创建独立的输出目录

### 第三阶段：图片智能处理和优化
1. **图片质量检查** → 检查DPI、文件大小、分辨率
2. **智能压缩优化** → 自动压缩大图片以满足API要求
3. **人像检测** → 使用腾讯云COS API检测每张图片中的人脸
4. **图片分类** → 区分人像图片和非人像图片
5. **质量警告生成** → 对低质量图片生成警告信息

### 第四阶段：人像图片增强处理
1. **人像背景分割** → 对检测到的人像进行精确抠图
2. **透明背景生成** → 生成PNG格式的透明背景人像
3. **标准照片制作** → 生成符合标准尺寸的证件照
4. **最佳头像选择** → 自动选择处理质量最好的头像

### 第五阶段：AI信息提取和结构化
1. **文本内容整理** → 清理和格式化提取的文本
2. **AI API调用** → 发送到Aibot API进行智能解析
3. **结构化数据解析** → 解析返回的JSON格式简历信息
4. **多行文本处理** → 自动分割职务、学术任职等多行信息
5. **数据验证** → 验证提取信息的完整性和格式

### 第六阶段：PPT自动生成
1. **模板分析** → 解析PPT模板中的占位符
2. **数据映射** → 将简历信息映射到模板占位符
3. **Python脚本调用** → 调用Python PPT生成模块
4. **文本智能适配** → 根据内容长度自动调整字体大小
5. **图片插入** → 自动插入最佳头像图片
6. **文件生成** → 生成最终的定制化PPT文件

### 第七阶段：结果输出和下载
1. **JSON文件生成** → 包含完整的处理结果和统计信息
2. **文件打包** → 准备所有输出文件供下载
3. **下载链接生成** → 提供Web API下载服务
4. **前端结果展示** → 显示处理结果和下载选项
5. **临时文件清理** → 自动清理临时处理文件

## 📊 核心数据结构

### ResumeInfo（简历信息）
```go
type ResumeInfo struct {
	Name           string   `json:"姓名"`
	Hospital       string   `json:"医院"`
	Department     string   `json:"科室"`
	Title          string   `json:"职称"`
	Description    string   `json:"介绍"`
	ProfilePicture string   `json:"头像,omitempty"`

	// 详细字段
	Position       string   `json:"职务职称,omitempty"`
	AcademicTitle  string   `json:"学术任职,omitempty"`
	Specialty      string   `json:"专业擅长,omitempty"`
	AcademicOutput string   `json:"学术成果,omitempty"`
	Other          string   `json:"其他,omitempty"`

	// 结构化列表
	Positions       []string `json:"职务职称列表,omitempty"`
	AcademicTitles  []string `json:"学术任职列表,omitempty"`
	AcademicOutputs []string `json:"学术成果列表,omitempty"`
}
```

### ProcessingStats（处理统计）
```go
type ProcessingStats struct {
	TotalImages     int           `json:"total_images"`
	PortraitImages  int           `json:"portrait_images"`
	ProcessedImages int           `json:"processed_images"`
	ProcessingTime  int64         `json:"processing_time"`
	BestPortrait    string        `json:"best_portrait,omitempty"`
	Results         []ImageResult `json:"results,omitempty"`
}
```

### ImageResult（图片处理结果）
```go
type ImageResult struct {
	OriginalPath      string                `json:"original_path"`
	ProcessedPath     string                `json:"processed_path,omitempty"`
	CutoutPath        string                `json:"cutout_path,omitempty"`
	StandardPhotoPath string                `json:"standard_photo_path,omitempty"`
	IsPortrait        bool                  `json:"is_portrait"`
	Confidence        float64               `json:"confidence,omitempty"`
	ProcessingTime    int64                 `json:"processing_time"`
	Error             string                `json:"error,omitempty"`
	CompressionInfo   *ImageCompressionInfo `json:"compression_info,omitempty"`
	QualityWarnings   []string              `json:"quality_warnings,omitempty"`
}
```

## 🚀 新增特性和改进

### 技术改进
1. **智能图片压缩算法** - 自动优化图片大小以满足API要求
2. **实时日志系统** - 基于SSE的实时状态推送
3. **环境自适应** - 支持本地开发和Docker部署
4. **错误重试机制** - API调用失败自动重试
5. **配置灵活性** - 支持环境变量和配置文件双重配置

### 用户体验改进
1. **现代化界面** - 使用Tailwind CSS和Font Awesome
2. **拖拽上传** - 支持文件拖拽上传
3. **实时反馈** - 处理过程实时日志显示
4. **批量下载** - 支持ZIP格式批量下载
5. **调试模式** - 开发调试信息支持

### 处理能力增强
1. **高质量抠图** - 基于腾讯云AI的精确人像分割
2. **智能PPT生成** - 自适应文本布局和字体大小
3. **多格式支持** - 支持多种图片格式输入输出
4. **质量检查** - 自动检查图片质量并给出建议

## 🔧 环境配置

### 必需的环境变量
```bash
# AI API配置
AIBOT_API_KEY=your_api_key

# 腾讯云配置
TENCENT_SECRET_ID=your_secret_id
TENCENT_SECRET_KEY=your_secret_key
TENCENT_REGION=ap-shanghai
TENCENT_BUCKET=your_bucket_name

# 可选配置
DEBUG=true  # 启用调试模式
PORT=8088   # 服务端口
```

### Docker部署支持
项目支持完整的Docker化部署，自动检测运行环境并调整配置路径和依赖。

---

**注意**: 此文档反映了项目v2.0版本的完整功能。所有功能已经过测试并可在生产环境中使用。

