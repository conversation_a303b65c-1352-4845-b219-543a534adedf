package main

import (
	"bufio"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"
)

// globalConfig 存储全局配置实例
var globalConfig *Config

func init() {
	var err error
	globalConfig, err = LoadConfig("") // 在程序启动时加载配置
	if err != nil {
		fmt.Printf("❌ 初始配置加载失败: %v\n", err)
		// 根据需要决定是否panic或继续，这里选择继续但打印警告
	}
}

// GetConfig 获取全局配置实例
func GetConfig() *Config {
	return globalConfig
}

// 添加.env文件加载功能
func loadEnvFile(envPath string) error {
	file, err := os.Open(envPath)
	if err != nil {
		return fmt.Errorf("无法打开.env文件: %v", err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		// 跳过注释和空行
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// 解析键值对
		if strings.Contains(line, "=") {
			parts := strings.SplitN(line, "=", 2)
			if len(parts) == 2 {
				key := strings.TrimSpace(parts[0])
				value := strings.TrimSpace(parts[1])

				// 设置环境变量
				os.Setenv(key, value)
			}
		}
	}

	return scanner.Err()
}

// getEnvPath 获取.env文件路径
func getEnvPath() string {
	// 首先尝试项目根目录
	rootEnv := ".env"
	if _, err := os.Stat(rootEnv); err == nil {
		return rootEnv
	}

	// 如果根目录没有，尝试main目录
	mainEnv := "main/.env"
	if _, err := os.Stat(mainEnv); err == nil {
		return mainEnv
	}

	return ""
}

// getConfigPath 根据运行环境获取配置文件路径
func getConfigPath() string {
	// 检查是否在Docker环境中运行
	// Docker容器中通常没有main目录，配置文件在根目录
	if _, err := os.Stat("main"); os.IsNotExist(err) {
		// Docker环境：配置文件在根目录
		return "config.json"
	}

	// 本地开发环境：配置文件在main目录
	return "main/config.json"
}

// isDockerEnvironment 检测是否在Docker环境中运行
func isDockerEnvironment() bool {
	// 方法1：检查main目录是否存在
	if _, err := os.Stat("main"); os.IsNotExist(err) {
		return true
	}

	// 方法2：检查Docker相关环境变量
	if os.Getenv("DOCKER_CONTAINER") == "true" {
		return true
	}

	// 方法3：检查是否在容器中运行
	if _, err := os.Stat("/.dockerenv"); err == nil {
		return true
	}

	return false
}

// getEnvOrDefault 从环境变量获取值，如果不存在则返回默认值
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvBoolOrDefault 从环境变量获取布尔值
func getEnvBoolOrDefault(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if strings.ToLower(value) == "true" || value == "1" {
			return true
		}
		if strings.ToLower(value) == "false" || value == "0" {
			return false
		}
	}
	return defaultValue
}

// getEnvFloatOrDefault 从环境变量获取浮点数值
func getEnvFloatOrDefault(key string, defaultValue float64) float64 {
	if value := os.Getenv(key); value != "" {
		if floatValue, err := strconv.ParseFloat(value, 64); err == nil {
			return floatValue
		}
	}
	return defaultValue
}

// getEnvIntOrDefault 从环境变量获取整数值
func getEnvIntOrDefault(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	config := &Config{}

	// 1. 首先加载.env文件
	envPath := getEnvPath()
	if envPath != "" {
		fmt.Printf("🔍 加载环境变量文件: %s\n", envPath)
		if err := loadEnvFile(envPath); err != nil {
			fmt.Printf("⚠️  加载.env文件失败: %v\n", err)
		} else {
			fmt.Printf("✅ 成功加载.env文件\n")
		}
	} else {
		fmt.Printf("⚠️  未找到.env文件\n")
	}

	// 2. 添加环境变量调试信息
	fmt.Printf("🔍 检查环境变量...\n")
	fmt.Printf("   TENCENT_SECRET_ID: %s\n", os.Getenv("TENCENT_SECRET_ID"))
	fmt.Printf("   TENCENT_SECRET_KEY: %s\n", os.Getenv("TENCENT_SECRET_KEY"))
	fmt.Printf("   TENCENT_BUCKET: %s\n", os.Getenv("TENCENT_BUCKET"))
	fmt.Printf("   TENCENT_REGION: %s\n", os.Getenv("TENCENT_REGION"))
	fmt.Printf("   AIBOT_API_KEY: %s\n", os.Getenv("AIBOT_API_KEY"))

	// 3. 从环境变量加载配置
	config.AIAPIKey = getEnvOrDefault("AIBOT_API_KEY", "")
	config.AIModel = getEnvOrDefault("AIBOT_MODEL", "")
	config.AIEndpoint = getEnvOrDefault("AIBOT_ENDPOINT", "")
	config.TencentSecretID = getEnvOrDefault("TENCENT_SECRET_ID", "")
	config.TencentSecretKey = getEnvOrDefault("TENCENT_SECRET_KEY", "")
	config.TencentRegion = getEnvOrDefault("TENCENT_REGION", "")
	config.TencentBucket = getEnvOrDefault("TENCENT_BUCKET", "")

	config.PortraitDetectionThreshold = getEnvFloatOrDefault("PORTRAIT_DETECTION_THRESHOLD", 0.7)
	config.EnableImageProcessing = getEnvBoolOrDefault("ENABLE_IMAGE_PROCESSING", true)
	config.MaxImageSizeMB = getEnvIntOrDefault("MAX_IMAGE_SIZE_MB", 10)
	config.ImageProcessingTimeout = getEnvIntOrDefault("IMAGE_PROCESSING_TIMEOUT", 30)
	config.EnablePhotoEnhancement = getEnvBoolOrDefault("ENABLE_PHOTO_ENHANCEMENT", true)

	// 从环境变量加载照片增强配置
	config.PhotoEnhancement.StandardWidth = getEnvIntOrDefault("PHOTO_STANDARD_WIDTH", 800)
	config.PhotoEnhancement.StandardHeight = getEnvIntOrDefault("PHOTO_STANDARD_HEIGHT", 1000)
	config.PhotoEnhancement.AutoRotate = getEnvBoolOrDefault("PHOTO_AUTO_ROTATE", true)
	config.PhotoEnhancement.SkewThreshold = getEnvFloatOrDefault("PHOTO_SKEW_THRESHOLD", 5.0)
	config.PhotoEnhancement.FaceDetection = getEnvBoolOrDefault("PHOTO_FACE_DETECTION", true)
	config.PhotoEnhancement.Quality = getEnvIntOrDefault("PHOTO_QUALITY", 90)
	config.PhotoEnhancement.Compression = getEnvIntOrDefault("PHOTO_COMPRESSION", 6)
	config.PhotoEnhancement.PPTWidth = getEnvIntOrDefault("PHOTO_PPT_WIDTH", 300)
	config.PhotoEnhancement.PPTHeight = getEnvIntOrDefault("PHOTO_PPT_HEIGHT", 400)
	config.PhotoEnhancement.MaintainAspect = getEnvBoolOrDefault("PHOTO_MAINTAIN_ASPECT", true)

	// 新增字段：图片大小优化相关配置
	config.PhotoEnhancement.MaxImageSizeForAPI = getEnvFloatOrDefault("PHOTO_MAX_IMAGE_SIZE_FOR_API", 2.99)
	config.PhotoEnhancement.CompressionQuality = getEnvIntOrDefault("PHOTO_COMPRESSION_QUALITY", 85)
	config.PhotoEnhancement.MinDPI = getEnvIntOrDefault("PHOTO_MIN_DPI", 100)
	config.PhotoEnhancement.EnableSizeCheck = getEnvBoolOrDefault("PHOTO_ENABLE_SIZE_CHECK", true)
	config.PhotoEnhancement.EnableDPICheck = getEnvBoolOrDefault("PHOTO_ENABLE_DPI_CHECK", true)

	// 智能压缩配置
	config.PhotoEnhancement.SmartResizeTargetWidth = getEnvIntOrDefault("PHOTO_SMART_RESIZE_TARGET_WIDTH", 1920)
	config.PhotoEnhancement.SmartResizeThresholdWidth = getEnvIntOrDefault("PHOTO_SMART_RESIZE_THRESHOLD_WIDTH", 2000)
	config.PhotoEnhancement.QualityPriority = getEnvBoolOrDefault("PHOTO_QUALITY_PRIORITY", true)

	// 如果环境变量中没有关键配置，尝试从配置文件加载（作为备用）
	if config.AIAPIKey == "" || config.TencentSecretID == "" {
		fmt.Printf("⚠️  环境变量中缺少关键配置，尝试从配置文件加载...\n")

		// 如果没有指定路径，使用自动检测的路径
		if configPath == "" {
			configPath = getConfigPath()
		}

		fmt.Printf("🔍 尝试加载配置文件: %s\n", configPath)

		// 读取配置文件
		data, err := os.ReadFile(configPath)
		if err != nil {
			return nil, fmt.Errorf("读取配置文件失败: %v", err)
		}

		// 解析配置文件
		var fileConfig Config
		if err := json.Unmarshal(data, &fileConfig); err != nil {
			return nil, fmt.Errorf("解析配置文件失败: %v", err)
		}

		fmt.Printf("✅ 成功加载配置文件\n")

		// 如果环境变量中没有设置，使用配置文件的值
		if config.AIAPIKey == "" {
			config.AIAPIKey = fileConfig.AIAPIKey
		}
		if config.AIModel == "" {
			config.AIModel = fileConfig.AIModel
		}
		if config.AIEndpoint == "" {
			config.AIEndpoint = fileConfig.AIEndpoint
		}
		if config.TencentSecretID == "" {
			config.TencentSecretID = fileConfig.TencentSecretID
		}
		if config.TencentSecretKey == "" {
			config.TencentSecretKey = fileConfig.TencentSecretKey
		}
		if config.TencentRegion == "" {
			config.TencentRegion = fileConfig.TencentRegion
		}
		if config.TencentBucket == "" {
			config.TencentBucket = fileConfig.TencentBucket
		}

		// 合并其他配置
		if config.PortraitDetectionThreshold == 0 {
			config.PortraitDetectionThreshold = fileConfig.PortraitDetectionThreshold
		}
		if !config.EnableImageProcessing {
			config.EnableImageProcessing = fileConfig.EnableImageProcessing
		}
		if config.MaxImageSizeMB == 0 {
			config.MaxImageSizeMB = fileConfig.MaxImageSizeMB
		}
		if config.ImageProcessingTimeout == 0 {
			config.ImageProcessingTimeout = fileConfig.ImageProcessingTimeout
		}
		if !config.EnablePhotoEnhancement {
			config.EnablePhotoEnhancement = fileConfig.EnablePhotoEnhancement
		}

		// 合并照片增强配置
		if config.PhotoEnhancement.StandardWidth == 0 {
			config.PhotoEnhancement.StandardWidth = fileConfig.PhotoEnhancement.StandardWidth
		}
		if config.PhotoEnhancement.StandardHeight == 0 {
			config.PhotoEnhancement.StandardHeight = fileConfig.PhotoEnhancement.StandardHeight
		}
		if !config.PhotoEnhancement.AutoRotate {
			config.PhotoEnhancement.AutoRotate = fileConfig.PhotoEnhancement.AutoRotate
		}
		if config.PhotoEnhancement.SkewThreshold == 0 {
			config.PhotoEnhancement.SkewThreshold = fileConfig.PhotoEnhancement.SkewThreshold
		}
		if !config.PhotoEnhancement.FaceDetection {
			config.PhotoEnhancement.FaceDetection = fileConfig.PhotoEnhancement.FaceDetection
		}
		if config.PhotoEnhancement.Quality == 0 {
			config.PhotoEnhancement.Quality = fileConfig.PhotoEnhancement.Quality
		}
		if config.PhotoEnhancement.Compression == 0 {
			config.PhotoEnhancement.Compression = fileConfig.PhotoEnhancement.Compression
		}
		if config.PhotoEnhancement.PPTWidth == 0 {
			config.PhotoEnhancement.PPTWidth = fileConfig.PhotoEnhancement.PPTWidth
		}
		if config.PhotoEnhancement.PPTHeight == 0 {
			config.PhotoEnhancement.PPTHeight = fileConfig.PhotoEnhancement.PPTHeight
		}
		if !config.PhotoEnhancement.MaintainAspect {
			config.PhotoEnhancement.MaintainAspect = fileConfig.PhotoEnhancement.MaintainAspect
		}

		// 合并新增的图片优化配置
		if config.PhotoEnhancement.MaxImageSizeForAPI == 0 {
			config.PhotoEnhancement.MaxImageSizeForAPI = fileConfig.PhotoEnhancement.MaxImageSizeForAPI
		}
		if config.PhotoEnhancement.CompressionQuality == 0 {
			config.PhotoEnhancement.CompressionQuality = fileConfig.PhotoEnhancement.CompressionQuality
		}
		if config.PhotoEnhancement.MinDPI == 0 {
			config.PhotoEnhancement.MinDPI = fileConfig.PhotoEnhancement.MinDPI
		}
		if !config.PhotoEnhancement.EnableSizeCheck {
			config.PhotoEnhancement.EnableSizeCheck = fileConfig.PhotoEnhancement.EnableSizeCheck
		}
		if !config.PhotoEnhancement.EnableDPICheck {
			config.PhotoEnhancement.EnableDPICheck = fileConfig.PhotoEnhancement.EnableDPICheck
		}

		// 合并智能压缩配置
		if config.PhotoEnhancement.SmartResizeTargetWidth == 0 {
			config.PhotoEnhancement.SmartResizeTargetWidth = fileConfig.PhotoEnhancement.SmartResizeTargetWidth
		}
		if config.PhotoEnhancement.SmartResizeThresholdWidth == 0 {
			config.PhotoEnhancement.SmartResizeThresholdWidth = fileConfig.PhotoEnhancement.SmartResizeThresholdWidth
		}
		if !config.PhotoEnhancement.QualityPriority {
			config.PhotoEnhancement.QualityPriority = fileConfig.PhotoEnhancement.QualityPriority
		}
	}

	// 设置默认值
	if config.PortraitDetectionThreshold == 0 {
		config.PortraitDetectionThreshold = 0.7
	}
	if config.MaxImageSizeMB == 0 {
		config.MaxImageSizeMB = 10
	}
	if config.ImageProcessingTimeout == 0 {
		config.ImageProcessingTimeout = 30
	}

	// 设置照片矫正默认值
	if config.PhotoEnhancement.StandardWidth == 0 {
		config.PhotoEnhancement.StandardWidth = 800
	}
	if config.PhotoEnhancement.StandardHeight == 0 {
		config.PhotoEnhancement.StandardHeight = 1000
	}
	if config.PhotoEnhancement.SkewThreshold == 0 {
		config.PhotoEnhancement.SkewThreshold = 5.0
	}
	if config.PhotoEnhancement.Quality == 0 {
		config.PhotoEnhancement.Quality = 90
	}
	if config.PhotoEnhancement.Compression == 0 {
		config.PhotoEnhancement.Compression = 6
	}
	if config.PhotoEnhancement.PPTWidth == 0 {
		config.PhotoEnhancement.PPTWidth = 300
	}
	if config.PhotoEnhancement.PPTHeight == 0 {
		config.PhotoEnhancement.PPTHeight = 400
	}

	// 设置新增字段的默认值
	if config.PhotoEnhancement.MaxImageSizeForAPI == 0 {
		config.PhotoEnhancement.MaxImageSizeForAPI = 2.99
	}
	if config.PhotoEnhancement.CompressionQuality == 0 {
		config.PhotoEnhancement.CompressionQuality = 85
	}
	if config.PhotoEnhancement.MinDPI == 0 {
		config.PhotoEnhancement.MinDPI = 100
	}

	// 设置智能压缩默认值
	if config.PhotoEnhancement.SmartResizeTargetWidth == 0 {
		config.PhotoEnhancement.SmartResizeTargetWidth = 1920
	}
	if config.PhotoEnhancement.SmartResizeThresholdWidth == 0 {
		config.PhotoEnhancement.SmartResizeThresholdWidth = 2000
	}

	// 最终配置确认
	fmt.Printf("🔍 最终配置确认:\n")
	fmt.Printf("   腾讯云SecretID: %s\n", config.TencentSecretID)
	fmt.Printf("   腾讯云SecretKey: %s\n", config.TencentSecretKey)
	fmt.Printf("   腾讯云Bucket: %s\n", config.TencentBucket)
	fmt.Printf("   腾讯云Region: %s\n", config.TencentRegion)
	fmt.Printf("   Aibot API Key: %s\n", config.AIAPIKey)

	return config, nil
}
