# 二进制文件
*.exe
*.exe~
*.dll
*.so
*.dylib

# 测试二进制文件
*.test

# 输出文件
*.out

# 依赖目录
vendor/

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log

# 临时文件
temp/
tmp/

# 输出目录
output/

# # 环境变量文件（包含敏感信息）
# .env
# .env.local
# .env.production
# .env.staging

# 配置文件（可选，如果包含敏感信息）
main/config.json

# 测试结果
test/test_results/

# 构建产物
build/
dist/

# Go相关
go.work 


# 虚拟环境
venv/


templates-vc/