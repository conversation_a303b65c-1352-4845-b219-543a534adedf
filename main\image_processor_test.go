package main

import (
	"os"
	"path/filepath"
	"testing"
	"time"
)

// TestImageProcessor 测试图片处理器基本功能
func TestImageProcessor(t *testing.T) {
	// 创建临时测试目录
	tempDir, err := os.MkdirTemp("", "test_images")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建测试图片文件（模拟）
	testImages := []string{"image1.jpg", "image2.png", "image3.jpeg"}
	for _, img := range testImages {
		imgPath := filepath.Join(tempDir, img)
		if err := os.WriteFile(imgPath, []byte("fake image data"), 0644); err != nil {
			t.Fatalf("Failed to create test image %s: %v", img, err)
		}
	}

	// 创建配置
	config := &Config{
		AibotAPIKey:      "test_key",
		TencentSecretID:  "test_id",
		TencentSecretKey: "test_key",
		TencentBucket:    "test_bucket",
		TencentRegion:    "ap-shanghai",
	}

	// 创建图片处理器
	processor := NewImageProcessor(config)

	// 测试处理器是否正确初始化
	if processor == nil {
		t.Fatal("ImageProcessor should not be nil")
	}
}

// TestValidateImageFormat 测试图片格式验证
func TestValidateImageFormat(t *testing.T) {
	tests := []struct {
		filename string
		expected bool
	}{
		{"image.jpg", true},
		{"image.jpeg", true},
		{"image.png", true},
		{"image.gif", false},
		{"image.bmp", false},
		{"document.pdf", false},
		{"file.txt", false},
		{"", false},
	}

	for _, tt := range tests {
		t.Run(tt.filename, func(t *testing.T) {
			result := validateImageFormat(tt.filename)
			if result != tt.expected {
				t.Errorf("validateImageFormat(%s) = %v, expected %v", tt.filename, result, tt.expected)
			}
		})
	}
}

// TestProcessingStats 测试处理统计功能
func TestProcessingStats(t *testing.T) {
	stats := &ProcessingStats{
		TotalImages:       5,
		ProcessedImages:   3,
		PortraitsDetected: 2,
		NonPortraits:      1,
		Errors:            []string{},
	}

	// 测试添加错误
	stats.AddError("test error")
	if len(stats.Errors) != 1 {
		t.Errorf("Expected 1 error, got %d", len(stats.Errors))
	}

	// 测试更新处理时间
	stats.UpdateProcessingTime(time.Second * 30)
	if stats.TotalProcessingTime != "30s" {
		t.Errorf("Expected processing time '30s', got %s", stats.TotalProcessingTime)
	}
}

// TestImagePathGeneration 测试图片路径生成
func TestImagePathGeneration(t *testing.T) {
	outputDir := "/test/output"
	filename := "test_image.jpg"

	// 测试原始图片路径
	originalPath := generateImagePath(outputDir, filename, "original")
	expected := filepath.Join(outputDir, "original_test_image.jpg")
	if originalPath != expected {
		t.Errorf("Expected %s, got %s", expected, originalPath)
	}

	// 测试处理后图片路径
	processedPath := generateImagePath(outputDir, filename, "processed")
	expected = filepath.Join(outputDir, "processed_test_image.jpg")
	if processedPath != expected {
		t.Errorf("Expected %s, got %s", expected, processedPath)
	}
}

// TestImageMetadata 测试图片元数据提取
func TestImageMetadata(t *testing.T) {
	metadata := &ImageMetadata{
		Filename:    "test.jpg",
		Size:        1024,
		Format:      "JPEG",
		Width:       800,
		Height:      600,
		IsPortrait:  true,
		ProcessedAt: time.Now(),
	}

	// 测试元数据验证
	if !metadata.IsValid() {
		t.Error("Valid metadata should return true")
	}

	// 测试宽高比计算
	aspectRatio := metadata.GetAspectRatio()
	expected := float64(800) / float64(600)
	if aspectRatio != expected {
		t.Errorf("Expected aspect ratio %f, got %f", expected, aspectRatio)
	}
}

// 辅助函数：验证图片格式
func validateImageFormat(filename string) bool {
	if filename == "" {
		return false
	}

	ext := filepath.Ext(filename)
	switch ext {
	case ".jpg", ".jpeg", ".png":
		return true
	default:
		return false
	}
}

// 辅助函数：生成图片路径
func generateImagePath(outputDir, filename, prefix string) string {
	if prefix != "" {
		base := filepath.Base(filename)
		ext := filepath.Ext(base)
		name := base[:len(base)-len(ext)]
		newName := prefix + "_" + name + ext
		return filepath.Join(outputDir, newName)
	}
	return filepath.Join(outputDir, filename)
}

// 辅助结构：图片元数据
type ImageMetadata struct {
	Filename    string
	Size        int64
	Format      string
	Width       int
	Height      int
	IsPortrait  bool
	ProcessedAt time.Time
}

// IsValid 检查元数据是否有效
func (m *ImageMetadata) IsValid() bool {
	return m.Filename != "" && m.Size > 0 && m.Width > 0 && m.Height > 0
}

// GetAspectRatio 计算宽高比
func (m *ImageMetadata) GetAspectRatio() float64 {
	if m.Height == 0 {
		return 0
	}
	return float64(m.Width) / float64(m.Height)
}

// 辅助结构：图片处理器（模拟）
type ImageProcessor struct {
	config *Config
}

// NewImageProcessor 创建新的图片处理器
func NewImageProcessor(config *Config) *ImageProcessor {
	return &ImageProcessor{config: config}
}

// ProcessingStats 的扩展方法
func (s *ProcessingStats) AddError(err string) {
	s.Errors = append(s.Errors, err)
}

func (s *ProcessingStats) UpdateProcessingTime(duration time.Duration) {
	s.TotalProcessingTime = duration.String()
}
