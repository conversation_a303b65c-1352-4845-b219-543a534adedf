<!DOCTYPE html><html class="default" lang="en" data-base=".."><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>GraphModel | @vladmandic/human - v3.3.5</title><meta name="description" content="Documentation for @vladmandic/human"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script><script async src="../assets/hierarchy.js" id="tsd-hierarchy-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search"><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@vladmandic/human - v3.3.5</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../index.html">@vladmandic/human</a></li><li><a href="GraphModel.html">GraphModel</a></li></ul><h1>Class GraphModel&lt;ModelURL&gt;</h1></div><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><p>A <code>tf.GraphModel</code> is a directed, acyclic graph built from a
SavedModel GraphDef and allows inference execution.</p>
<p>A <code>tf.GraphModel</code> can only be created by loading from a model converted from
a <a href="https://www.tensorflow.org/guide/saved_model">TensorFlow SavedModel</a> using
the command line converter tool and loaded via <code>tf.loadGraphModel</code>.</p>
</div><div class="tsd-comment tsd-typography"><div class="tsd-tag-doc"><h4 class="tsd-anchor-link"><a id="doc" class="tsd-anchor"></a>Doc<a href="#doc" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4></div></div></section> <section class="tsd-panel"><h4>Type Parameters</h4><ul class="tsd-type-parameter-list"><li><span><a id="modelurl" class="tsd-anchor"></a><span class="tsd-kind-type-parameter">ModelURL</span><span class="tsd-signature-keyword"> extends </span><span class="tsd-signature-type">Url</span> = <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">io.IOHandler</span></span></li></ul></section> <section class="tsd-panel"><h4>Implements</h4><ul class="tsd-hierarchy"><li><span class="tsd-signature-type">InferenceModel</span></li></ul></section><aside class="tsd-sources"><ul><li>Defined in node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/executor/graph_model.d.ts:34</li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg> Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Constructors</h3><div class="tsd-index-list"><a href="GraphModel.html#constructor" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Accessors</h3><div class="tsd-index-list"><a href="GraphModel.html#inputnodes" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-262144"></use></svg><span>input<wbr/>Nodes</span></a>
<a href="GraphModel.html#inputs" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-262144"></use></svg><span>inputs</span></a>
<a href="GraphModel.html#metadata" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-262144"></use></svg><span>metadata</span></a>
<a href="GraphModel.html#modelsignature" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-262144"></use></svg><span>model<wbr/>Signature</span></a>
<a href="GraphModel.html#modelstructuredoutputkeys" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-262144"></use></svg><span>model<wbr/>Structured<wbr/>Output<wbr/>Keys</span></a>
<a href="GraphModel.html#modelversion" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-262144"></use></svg><span>model<wbr/>Version</span></a>
<a href="GraphModel.html#outputnodes" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-262144"></use></svg><span>output<wbr/>Nodes</span></a>
<a href="GraphModel.html#outputs" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-262144"></use></svg><span>outputs</span></a>
<a href="GraphModel.html#weights" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-262144"></use></svg><span>weights</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Methods</h3><div class="tsd-index-list"><a href="GraphModel.html#dispose" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>dispose</span></a>
<a href="GraphModel.html#disposeintermediatetensors" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>dispose<wbr/>Intermediate<wbr/>Tensors</span></a>
<a href="GraphModel.html#execute" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>execute</span></a>
<a href="GraphModel.html#executeasync" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>execute<wbr/>Async</span></a>
<a href="GraphModel.html#getintermediatetensors" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Intermediate<wbr/>Tensors</span></a>
<a href="GraphModel.html#load" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>load</span></a>
<a href="GraphModel.html#loadsync" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>load<wbr/>Sync</span></a>
<a href="GraphModel.html#predict" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>predict</span></a>
<a href="GraphModel.html#predictasync" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>predict<wbr/>Async</span></a>
<a href="GraphModel.html#save" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>save</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Constructors"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Constructors</h2></summary><section><section class="tsd-panel tsd-member"><a id="constructor" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>constructor</span><a href="#constructor" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="constructorgraphmodel" class="tsd-anchor"></a><span class="tsd-signature-keyword">new</span> <span class="tsd-kind-constructor-signature">GraphModel</span><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="GraphModel.html#constructorgraphmodelmodelurl">ModelURL</a> <span class="tsd-signature-keyword">extends</span> <span class="tsd-signature-type">Url</span> <span class="tsd-signature-symbol">=</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">IOHandler</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">modelUrl</span><span class="tsd-signature-symbol">:</span> <a class="tsd-signature-type tsd-kind-type-parameter" href="GraphModel.html#constructorgraphmodelmodelurl">ModelURL</a><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-kind-parameter">loadOptions</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">LoadOptions</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-kind-parameter">tfio</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">__module</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="GraphModel.html" class="tsd-signature-type tsd-kind-class">GraphModel</a><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="GraphModel.html#constructorgraphmodelmodelurl">ModelURL</a><span class="tsd-signature-symbol">&gt;</span><a href="#constructorgraphmodel" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><section class="tsd-panel"><h4>Type Parameters</h4><ul class="tsd-type-parameter-list"><li><span><a id="constructorgraphmodelmodelurl" class="tsd-anchor"></a><span class="tsd-kind-type-parameter">ModelURL</span><span class="tsd-signature-keyword"> extends </span><span class="tsd-signature-type">Url</span> = <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">IOHandler</span></span></li></ul></section><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">modelUrl</span>: <a class="tsd-signature-type tsd-kind-type-parameter" href="GraphModel.html#constructorgraphmodelmodelurl">ModelURL</a></span><div class="tsd-comment tsd-typography"><p>url for the model, or an <code>io.IOHandler</code>.</p>
</div><div class="tsd-comment tsd-typography"></div></li><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">loadOptions</span>: <span class="tsd-signature-type">LoadOptions</span></span></li><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">tfio</span>: <span class="tsd-signature-type">__module</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="GraphModel.html" class="tsd-signature-type tsd-kind-class">GraphModel</a><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="GraphModel.html#constructorgraphmodelmodelurl">ModelURL</a><span class="tsd-signature-symbol">&gt;</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/executor/graph_model.d.ts:66</li></ul></aside></li></ul></section></section></details><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Accessors"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Accessors</h2></summary><section><section class="tsd-panel tsd-member"><a id="inputnodes" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>input<wbr/>Nodes</span><a href="#inputnodes" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature" id="inputnodes-1"><span class="tsd-signature-keyword">get</span> <span class="tsd-kind-get-signature">inputNodes</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></li><li class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h4><aside class="tsd-sources"><ul><li>Defined in node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/executor/graph_model.d.ts:49</li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="inputs" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>inputs</span><a href="#inputs" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature" id="inputs-1"><span class="tsd-signature-keyword">get</span> <span class="tsd-kind-get-signature">inputs</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">TensorInfo</span><span class="tsd-signature-symbol">[]</span></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Return the array of input tensor info.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">TensorInfo</span><span class="tsd-signature-symbol">[]</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><p>Implementation of InferenceModel.inputs</p><ul><li>Defined in node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/executor/graph_model.d.ts:51</li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="metadata" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>metadata</span><a href="#metadata" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature" id="metadata-1"><span class="tsd-signature-keyword">get</span> <span class="tsd-kind-get-signature">metadata</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{}</span></li><li class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">{}</span></h4><aside class="tsd-sources"><ul><li>Defined in node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/executor/graph_model.d.ts:54</li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="modelsignature" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>model<wbr/>Signature</span><a href="#modelsignature" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature" id="modelsignature-1"><span class="tsd-signature-keyword">get</span> <span class="tsd-kind-get-signature">modelSignature</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{}</span></li><li class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">{}</span></h4><aside class="tsd-sources"><ul><li>Defined in node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/executor/graph_model.d.ts:55</li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="modelstructuredoutputkeys" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>model<wbr/>Structured<wbr/>Output<wbr/>Keys</span><a href="#modelstructuredoutputkeys" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature" id="modelstructuredoutputkeys-1"><span class="tsd-signature-keyword">get</span> <span class="tsd-kind-get-signature">modelStructuredOutputKeys</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{}</span></li><li class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">{}</span></h4><aside class="tsd-sources"><ul><li>Defined in node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/executor/graph_model.d.ts:56</li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="modelversion" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>model<wbr/>Version</span><a href="#modelversion" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature" id="modelversion-1"><span class="tsd-signature-keyword">get</span> <span class="tsd-kind-get-signature">modelVersion</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></li><li class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4><aside class="tsd-sources"><ul><li>Defined in node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/executor/graph_model.d.ts:48</li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="outputnodes" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>output<wbr/>Nodes</span><a href="#outputnodes" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature" id="outputnodes-1"><span class="tsd-signature-keyword">get</span> <span class="tsd-kind-get-signature">outputNodes</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></li><li class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h4><aside class="tsd-sources"><ul><li>Defined in node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/executor/graph_model.d.ts:50</li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="outputs" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>outputs</span><a href="#outputs" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature" id="outputs-1"><span class="tsd-signature-keyword">get</span> <span class="tsd-kind-get-signature">outputs</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">TensorInfo</span><span class="tsd-signature-symbol">[]</span></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Return the array of output tensor info.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">TensorInfo</span><span class="tsd-signature-symbol">[]</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><p>Implementation of InferenceModel.outputs</p><ul><li>Defined in node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/executor/graph_model.d.ts:52</li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="weights" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>weights</span><a href="#weights" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature" id="weights-1"><span class="tsd-signature-keyword">get</span> <span class="tsd-kind-get-signature">weights</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">NamedTensorsMap</span></li><li class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">NamedTensorsMap</span></h4><aside class="tsd-sources"><ul><li>Defined in node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/executor/graph_model.d.ts:53</li></ul></aside></li></ul></section></section></details><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Methods"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Methods</h2></summary><section><section class="tsd-panel tsd-member"><a id="dispose" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>dispose</span><a href="#dispose" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="dispose-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">dispose</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">void</span><a href="#dispose-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Releases the memory used by the weight tensors and resourceManager.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4><div class="tsd-comment tsd-typography"><div class="tsd-tag-doc"><h4 class="tsd-anchor-link"><a id="doc-1" class="tsd-anchor"></a>Doc<a href="#doc-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4></div></div><aside class="tsd-sources"><ul><li>Defined in node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/executor/graph_model.d.ts:268</li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="disposeintermediatetensors" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>dispose<wbr/>Intermediate<wbr/>Tensors</span><a href="#disposeintermediatetensors" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="disposeintermediatetensors-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">disposeIntermediateTensors</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">void</span><a href="#disposeintermediatetensors-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Dispose intermediate tensors for model debugging mode (flag
KEEP_INTERMEDIATE_TENSORS is true).</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4><div class="tsd-comment tsd-typography"><div class="tsd-tag-doc"><h4 class="tsd-anchor-link"><a id="doc-2" class="tsd-anchor"></a>Doc<a href="#doc-2" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4></div></div><aside class="tsd-sources"><ul><li>Defined in node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/executor/graph_model.d.ts:261</li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="execute" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>execute</span><a href="#execute" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="execute-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">execute</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">inputs</span><span class="tsd-signature-symbol">:</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a> <span class="tsd-signature-symbol">|</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a><span class="tsd-signature-symbol">[]</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">NamedTensorMap</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-kind-parameter">outputs</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a> <span class="tsd-signature-symbol">|</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a><span class="tsd-signature-symbol">[]</span><a href="#execute-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Executes inference for the model for given input tensors.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">inputs</span>: <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a> <span class="tsd-signature-symbol">|</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a><span class="tsd-signature-symbol">[]</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">NamedTensorMap</span></span><div class="tsd-comment tsd-typography"><p>tensor, tensor array or tensor map of the inputs for the
model, keyed by the input node names.</p>
</div><div class="tsd-comment tsd-typography"></div></li><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">outputs</span>: <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></span><div class="tsd-comment tsd-typography"><p>output node name from the TensorFlow model, if no
outputs are specified, the default outputs of the model would be used.
You can inspect intermediate nodes of the model by adding them to the
outputs array.</p>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a> <span class="tsd-signature-symbol">|</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a><span class="tsd-signature-symbol">[]</span></h4><p>A single tensor if provided with a single output or no outputs
are provided and there is only one default output, otherwise return a
tensor array. The order of the tensor array is the same as the outputs
if provided, otherwise the order of outputNodes attribute of the model.</p>
<div class="tsd-comment tsd-typography"><div class="tsd-tag-doc"><h4 class="tsd-anchor-link"><a id="doc-3" class="tsd-anchor"></a>Doc<a href="#doc-3" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4></div></div><aside class="tsd-sources"><p>Implementation of InferenceModel.execute</p><ul><li>Defined in node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/executor/graph_model.d.ts:230</li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="executeasync" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>execute<wbr/>Async</span><a href="#executeasync" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="executeasync-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">executeAsync</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">inputs</span><span class="tsd-signature-symbol">:</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a> <span class="tsd-signature-symbol">|</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a><span class="tsd-signature-symbol">[]</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">NamedTensorMap</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-kind-parameter">outputs</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a> <span class="tsd-signature-symbol">|</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><a href="#executeasync-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Executes inference for the model for given input tensors in async
fashion, use this method when your model contains control flow ops.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">inputs</span>: <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a> <span class="tsd-signature-symbol">|</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a><span class="tsd-signature-symbol">[]</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">NamedTensorMap</span></span><div class="tsd-comment tsd-typography"><p>tensor, tensor array or tensor map of the inputs for the
model, keyed by the input node names.</p>
</div><div class="tsd-comment tsd-typography"></div></li><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">outputs</span>: <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></span><div class="tsd-comment tsd-typography"><p>output node name from the TensorFlow model, if no outputs
are specified, the default outputs of the model would be used. You can
inspect intermediate nodes of the model by adding them to the outputs
array.</p>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a> <span class="tsd-signature-symbol">|</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4><p>A Promise of single tensor if provided with a single output or
no outputs are provided and there is only one default output, otherwise
return a tensor map.</p>
<div class="tsd-comment tsd-typography"><div class="tsd-tag-doc"><h4 class="tsd-anchor-link"><a id="doc-4" class="tsd-anchor"></a>Doc<a href="#doc-4" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4></div></div><aside class="tsd-sources"><ul><li>Defined in node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/executor/graph_model.d.ts:247</li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="getintermediatetensors" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>get<wbr/>Intermediate<wbr/>Tensors</span><a href="#getintermediatetensors" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="getintermediatetensors-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">getIntermediateTensors</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">NamedTensorsMap</span><a href="#getintermediatetensors-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Get intermediate tensors for model debugging mode (flag
KEEP_INTERMEDIATE_TENSORS is true).</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">NamedTensorsMap</span></h4><div class="tsd-comment tsd-typography"><div class="tsd-tag-doc"><h4 class="tsd-anchor-link"><a id="doc-5" class="tsd-anchor"></a>Doc<a href="#doc-5" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4></div></div><aside class="tsd-sources"><ul><li>Defined in node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/executor/graph_model.d.ts:254</li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="load" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>load</span><a href="#load" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="load-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">load</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">UrlIOHandler</span><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="GraphModel.html#constructorgraphmodelmodelurl">ModelURL</a><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-keyword">extends</span> <span class="tsd-signature-type">IOHandlerSync</span><br/>    <span class="tsd-signature-symbol">?</span> <span class="tsd-signature-type">boolean</span><br/>    <span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span><a href="#load-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Loads the model and weight files, construct the in memory weight map and
compile the inference graph.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">UrlIOHandler</span><span class="tsd-signature-symbol">&lt;</span><a class="tsd-signature-type tsd-kind-type-parameter" href="GraphModel.html#constructorgraphmodelmodelurl">ModelURL</a><span class="tsd-signature-symbol">&gt;</span> <span class="tsd-signature-keyword">extends</span> <span class="tsd-signature-type">IOHandlerSync</span> <span class="tsd-signature-symbol">?</span> <span class="tsd-signature-type">boolean</span> <span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/executor/graph_model.d.ts:72</li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="loadsync" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>load<wbr/>Sync</span><a href="#loadsync" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="loadsync-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">loadSync</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">artifacts</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">ModelArtifacts</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><a href="#loadsync-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Synchronously construct the in memory weight map and
compile the inference graph.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">artifacts</span>: <span class="tsd-signature-type">ModelArtifacts</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4><div class="tsd-comment tsd-typography"><div class="tsd-tag-doc"><h4 class="tsd-anchor-link"><a id="doc-6" class="tsd-anchor"></a>Doc<a href="#doc-6" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4></div></div><aside class="tsd-sources"><ul><li>Defined in node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/executor/graph_model.d.ts:79</li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="predict" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>predict</span><a href="#predict" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="predict-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">predict</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">inputs</span><span class="tsd-signature-symbol">:</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a> <span class="tsd-signature-symbol">|</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a><span class="tsd-signature-symbol">[]</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">NamedTensorMap</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-kind-parameter">config</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">ModelPredictConfig</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a> <span class="tsd-signature-symbol">|</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a><span class="tsd-signature-symbol">[]</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">NamedTensorMap</span><a href="#predict-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Execute the inference for the input tensors.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">inputs</span>: <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a> <span class="tsd-signature-symbol">|</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a><span class="tsd-signature-symbol">[]</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">NamedTensorMap</span></span></li><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">config</span>: <span class="tsd-signature-type">ModelPredictConfig</span></span><div class="tsd-comment tsd-typography"><p>Prediction configuration for specifying the batch size.
Currently the batch size option is ignored for graph model.</p>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a> <span class="tsd-signature-symbol">|</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a><span class="tsd-signature-symbol">[]</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">NamedTensorMap</span></h4><p>Inference result tensors. If the model is converted and it
originally had structured_outputs in tensorflow, then a NamedTensorMap
will be returned matching the structured_outputs. If no structured_outputs
are present, the output will be single <code>tf.Tensor</code> if the model has single
output node, otherwise Tensor[].</p>
<div class="tsd-comment tsd-typography"><div class="tsd-tag-see"><h4 class="tsd-anchor-link"><a id="see" class="tsd-anchor"></a>See<a href="#see" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><p><a href="GraphModel.html#inputnodes" class="tsd-kind-accessor">GraphModel.inputNodes</a></p>
<p>You can also feed any intermediate nodes using the NamedTensorMap as the
input type. For example, given the graph
InputNode =&gt; Intermediate =&gt; OutputNode,
you can execute the subgraph Intermediate =&gt; OutputNode by calling
model.execute('IntermediateNode' : tf.tensor(...));</p>
<p>This is useful for models that uses tf.dynamic_rnn, where the intermediate
state needs to be fed manually.</p>
<p>For batch inference execution, the tensors for each input need to be
concatenated together. For example with mobilenet, the required input shape
is [1, 244, 244, 3], which represents the [batch, height, width, channel].
If we are provide a batched data of 100 images, the input tensor should be
in the shape of [100, 244, 244, 3].</p>
</div><div class="tsd-tag-doc"><h4 class="tsd-anchor-link"><a id="doc-7" class="tsd-anchor"></a>Doc<a href="#doc-7" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4></div></div><aside class="tsd-sources"><p>Implementation of InferenceModel.predict</p><ul><li>Defined in node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/executor/graph_model.d.ts:167</li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="predictasync" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>predict<wbr/>Async</span><a href="#predictasync" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="predictasync-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">predictAsync</span><span class="tsd-signature-symbol">(</span><br/>    <span class="tsd-kind-parameter">inputs</span><span class="tsd-signature-symbol">:</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a> <span class="tsd-signature-symbol">|</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a><span class="tsd-signature-symbol">[]</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">NamedTensorMap</span><span class="tsd-signature-symbol">,</span><br/>    <span class="tsd-kind-parameter">config</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">ModelPredictConfig</span><span class="tsd-signature-symbol">,</span><br/><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a> <span class="tsd-signature-symbol">|</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a><span class="tsd-signature-symbol">[]</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">NamedTensorMap</span><span class="tsd-signature-symbol">&gt;</span><a href="#predictasync-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Execute the inference for the input tensors in async fashion, use this
method when your model contains control flow ops.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">inputs</span>: <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a> <span class="tsd-signature-symbol">|</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a><span class="tsd-signature-symbol">[]</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">NamedTensorMap</span></span></li><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">config</span>: <span class="tsd-signature-type">ModelPredictConfig</span></span><div class="tsd-comment tsd-typography"><p>Prediction configuration for specifying the batch size.
Currently the batch size option is ignored for graph model.</p>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a> <span class="tsd-signature-symbol">|</span> <a href="Tensor-1.html" class="tsd-signature-type tsd-kind-class">Tensor</a><span class="tsd-signature-symbol">[]</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">NamedTensorMap</span><span class="tsd-signature-symbol">&gt;</span></h4><p>A Promise of inference result tensors. If the model is converted
and it originally had structured_outputs in tensorflow, then a
NamedTensorMap will be returned matching the structured_outputs. If no
structured_outputs are present, the output will be single <code>tf.Tensor</code> if
the model has single output node, otherwise Tensor[].</p>
<div class="tsd-comment tsd-typography"><div class="tsd-tag-see"><h4 class="tsd-anchor-link"><a id="see-1" class="tsd-anchor"></a>See<a href="#see-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><p><a href="GraphModel.html#inputnodes" class="tsd-kind-accessor">GraphModel.inputNodes</a></p>
<p>You can also feed any intermediate nodes using the NamedTensorMap as the
input type. For example, given the graph
InputNode =&gt; Intermediate =&gt; OutputNode,
you can execute the subgraph Intermediate =&gt; OutputNode by calling
model.execute('IntermediateNode' : tf.tensor(...));</p>
<p>This is useful for models that uses tf.dynamic_rnn, where the intermediate
state needs to be fed manually.</p>
<p>For batch inference execution, the tensors for each input need to be
concatenated together. For example with mobilenet, the required input shape
is [1, 244, 244, 3], which represents the [batch, height, width, channel].
If we are provide a batched data of 100 images, the input tensor should be
in the shape of [100, 244, 244, 3].</p>
</div><div class="tsd-tag-doc"><h4 class="tsd-anchor-link"><a id="doc-8" class="tsd-anchor"></a>Doc<a href="#doc-8" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4></div></div><aside class="tsd-sources"><ul><li>Defined in node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/executor/graph_model.d.ts:208</li></ul></aside></li></ul></section><section class="tsd-panel tsd-member"><a id="save" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>save</span><a href="#save" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class="tsd-signature tsd-anchor-link"><a id="save-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">save</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">handlerOrURL</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">IOHandler</span><span class="tsd-signature-symbol">,</span> <span class="tsd-kind-parameter">config</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">SaveConfig</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">SaveResult</span><span class="tsd-signature-symbol">&gt;</span><a href="#save-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></li><li class="tsd-description"><div class="tsd-comment tsd-typography"><p>Save the configuration and/or weights of the GraphModel.</p>
<p>An <code>IOHandler</code> is an object that has a <code>save</code> method of the proper
signature defined. The <code>save</code> method manages the storing or
transmission of serialized data (&quot;artifacts&quot;) that represent the
model's topology and weights onto or via a specific medium, such as
file downloads, local storage, IndexedDB in the web browser and HTTP
requests to a server. TensorFlow.js provides <code>IOHandler</code>
implementations for a number of frequently used saving mediums, such as
<code>tf.io.browserDownloads</code> and <code>tf.io.browserLocalStorage</code>. See <code>tf.io</code>
for more details.</p>
<p>This method also allows you to refer to certain types of <code>IOHandler</code>s
as URL-like string shortcuts, such as 'localstorage://' and
'indexeddb://'.</p>
<p>Example 1: Save <code>model</code>'s topology and weights to browser <a href="https://developer.mozilla.org/en-US/docs/Web/API/Window/localStorage">local
storage</a>;
then load it back.</p>
<pre><code class="js"><span class="hl-0">const</span><span class="hl-1"> </span><span class="hl-2">modelUrl</span><span class="hl-1"> =</span><br/><span class="hl-1">   </span><span class="hl-3">&#39;https://storage.googleapis.com/tfjs-models/savedmodel/mobilenet_v2_1.0_224/model.json&#39;</span><span class="hl-1">;</span><br/><span class="hl-0">const</span><span class="hl-1"> </span><span class="hl-2">model</span><span class="hl-1"> = </span><span class="hl-4">await</span><span class="hl-1"> </span><span class="hl-5">tf</span><span class="hl-1">.</span><span class="hl-6">loadGraphModel</span><span class="hl-1">(</span><span class="hl-5">modelUrl</span><span class="hl-1">);</span><br/><span class="hl-0">const</span><span class="hl-1"> </span><span class="hl-2">zeros</span><span class="hl-1"> = </span><span class="hl-5">tf</span><span class="hl-1">.</span><span class="hl-6">zeros</span><span class="hl-1">([</span><span class="hl-7">1</span><span class="hl-1">, </span><span class="hl-7">224</span><span class="hl-1">, </span><span class="hl-7">224</span><span class="hl-1">, </span><span class="hl-7">3</span><span class="hl-1">]);</span><br/><span class="hl-5">model</span><span class="hl-1">.</span><span class="hl-6">predict</span><span class="hl-1">(</span><span class="hl-5">zeros</span><span class="hl-1">).</span><span class="hl-6">print</span><span class="hl-1">();</span><br/><br/><span class="hl-0">const</span><span class="hl-1"> </span><span class="hl-2">saveResults</span><span class="hl-1"> = </span><span class="hl-4">await</span><span class="hl-1"> </span><span class="hl-5">model</span><span class="hl-1">.</span><span class="hl-6">save</span><span class="hl-1">(</span><span class="hl-3">&#39;localstorage://my-model-1&#39;</span><span class="hl-1">);</span><br/><br/><span class="hl-0">const</span><span class="hl-1"> </span><span class="hl-2">loadedModel</span><span class="hl-1"> = </span><span class="hl-4">await</span><span class="hl-1"> </span><span class="hl-5">tf</span><span class="hl-1">.</span><span class="hl-6">loadGraphModel</span><span class="hl-1">(</span><span class="hl-3">&#39;localstorage://my-model-1&#39;</span><span class="hl-1">);</span><br/><span class="hl-5">console</span><span class="hl-1">.</span><span class="hl-6">log</span><span class="hl-1">(</span><span class="hl-3">&#39;Prediction from loaded model:&#39;</span><span class="hl-1">);</span><br/><span class="hl-5">model</span><span class="hl-1">.</span><span class="hl-6">predict</span><span class="hl-1">(</span><span class="hl-5">zeros</span><span class="hl-1">).</span><span class="hl-6">print</span><span class="hl-1">();</span>
</code><button type="button">Copy</button></pre>

</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">handlerOrURL</span>: <span class="tsd-signature-type">string</span> <span class="tsd-signature-symbol">|</span> <span class="tsd-signature-type">IOHandler</span></span><div class="tsd-comment tsd-typography"><p>An instance of <code>IOHandler</code> or a URL-like,
scheme-based string shortcut for <code>IOHandler</code>.</p>
</div><div class="tsd-comment tsd-typography"></div></li><li><span><code class="tsd-tag">Optional</code><span class="tsd-kind-parameter">config</span>: <span class="tsd-signature-type">SaveConfig</span></span><div class="tsd-comment tsd-typography"><p>Options for saving the model.</p>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">SaveResult</span><span class="tsd-signature-symbol">&gt;</span></h4><p>A <code>Promise</code> of <code>SaveResult</code>, which summarizes the result of
the saving, such as byte sizes of the saved artifacts for the model's
topology and weight values.</p>
<div class="tsd-comment tsd-typography"><div class="tsd-tag-doc"><h4 class="tsd-anchor-link"><a id="doc-9" class="tsd-anchor"></a>Doc<a href="#doc-9" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4></div></div><aside class="tsd-sources"><ul><li>Defined in node_modules/.pnpm/@tensorflow+tfjs-converter@4.22.0_@tensorflow+tfjs-core@4.22.0/node_modules/@tensorflow/tfjs-converter/dist/executor/graph_model.d.ts:126</li></ul></aside></li></ul></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-filter-visibility"><span class="settings-label">Member Visibility</span><ul id="tsd-filter-options"><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-inherited" name="inherited" checked/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Inherited</span></label></li></ul></div><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Constructors"><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Constructors</summary><div><a href="#constructor" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a></div></details><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Accessors"><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Accessors</summary><div><a href="#inputnodes" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-262144"></use></svg><span>input<wbr/>Nodes</span></a><a href="#inputs" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-262144"></use></svg><span>inputs</span></a><a href="#metadata" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-262144"></use></svg><span>metadata</span></a><a href="#modelsignature" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-262144"></use></svg><span>model<wbr/>Signature</span></a><a href="#modelstructuredoutputkeys" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-262144"></use></svg><span>model<wbr/>Structured<wbr/>Output<wbr/>Keys</span></a><a href="#modelversion" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-262144"></use></svg><span>model<wbr/>Version</span></a><a href="#outputnodes" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-262144"></use></svg><span>output<wbr/>Nodes</span></a><a href="#outputs" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-262144"></use></svg><span>outputs</span></a><a href="#weights" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-262144"></use></svg><span>weights</span></a></div></details><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Methods"><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Methods</summary><div><a href="#dispose" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>dispose</span></a><a href="#disposeintermediatetensors" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>dispose<wbr/>Intermediate<wbr/>Tensors</span></a><a href="#execute" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>execute</span></a><a href="#executeasync" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>execute<wbr/>Async</span></a><a href="#getintermediatetensors" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>get<wbr/>Intermediate<wbr/>Tensors</span></a><a href="#load" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>load</span></a><a href="#loadsync" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>load<wbr/>Sync</span></a><a href="#predict" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>predict</span></a><a href="#predictasync" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>predict<wbr/>Async</span></a><a href="#save" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="../assets/icons.svg#icon-2048"></use></svg><span>save</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../index.html">@vladmandic/human - v3.3.5</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer></footer><div class="overlay"></div></body></html>
