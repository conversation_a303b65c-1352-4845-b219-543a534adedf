@echo off
chcp 65001 >nul
echo 构建独立运行包...
echo.

echo 1. 创建发布目录
if exist dist rmdir /s /q dist
mkdir dist

echo 2. 复制可执行文件
copy gejiesoft-pdf-tools.exe dist\

echo 3. 复制前端文件
xcopy frontend dist\frontend\ /E /I /Y

echo 4. 复制配置文件模板
copy env.example dist\

echo 5. 创建必要目录
mkdir dist\output
mkdir dist\temp

echo 6. 创建启动脚本
echo @echo off > dist\start.bat
echo chcp 65001 ^>nul >> dist\start.bat
echo echo 启动GejieSoft PDF Tools... >> dist\start.bat
echo echo. >> dist\start.bat
echo echo 请确保已配置环境变量或.env文件 >> dist\start.bat
echo echo. >> dist\start.bat
echo gejiesoft-pdf-tools.exe >> dist\start.bat
echo pause >> dist\start.bat

echo 7. 创建说明文件（UTF-8编码）
powershell -Command "& {
    $content = @'
GejieSoft PDF Tools - 独立运行包

使用说明:
1. 配置环境变量或编辑.env文件
2. 运行 start.bat 启动程序
3. 访问 http://localhost:8088

必需的环境变量:
- AIBOT_API_KEY
- TENCENT_SECRET_ID  
- TENCENT_SECRET_KEY
- TENCENT_BUCKET

配置方式:
方式1: 设置系统环境变量
方式2: 编辑.env文件（复制env.example为.env）

注意事项:
- 确保网络连接正常（需要访问腾讯云API）
- 首次运行可能需要较长时间下载依赖
- 输出文件保存在output目录中
'@
    [System.IO.File]::WriteAllText('dist\README.txt', $content, [System.Text.Encoding]::UTF8)
}"

echo.
echo ✅ 独立运行包构建完成！
echo 📁 发布目录: dist\
echo.
echo 使用方法:
echo 1. 将dist目录复制到任意位置
echo 2. 配置环境变量或.env文件
echo 3. 运行 start.bat 