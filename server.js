const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// MIME类型映射
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
    '.bin': 'application/octet-stream',
    '.wasm': 'application/wasm'
};

// 创建HTTP服务器
const server = http.createServer((req, res) => {
    // 解析URL
    const parsedUrl = url.parse(req.url);
    let pathname = parsedUrl.pathname;
    
    // 默认页面
    if (pathname === '/') {
        pathname = '/start.html';
    }
    
    // 构建文件路径
    const filePath = path.join(__dirname, pathname);
    
    // 检查文件是否存在
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            // 文件不存在
            res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
            res.end(`
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>404 - 文件未找到</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                        h1 { color: #e74c3c; }
                        a { color: #3498db; text-decoration: none; }
                        a:hover { text-decoration: underline; }
                    </style>
                </head>
                <body>
                    <h1>404 - 文件未找到</h1>
                    <p>请求的文件 <code>${pathname}</code> 不存在</p>
                    <p><a href="/start.html">返回首页</a></p>
                </body>
                </html>
            `);
            return;
        }
        
        // 获取文件扩展名
        const ext = path.extname(filePath).toLowerCase();
        const contentType = mimeTypes[ext] || 'application/octet-stream';
        
        // 读取文件
        fs.readFile(filePath, (err, data) => {
            if (err) {
                res.writeHead(500, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset="UTF-8">
                        <title>500 - 服务器错误</title>
                        <style>
                            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                            h1 { color: #e74c3c; }
                            a { color: #3498db; text-decoration: none; }
                            a:hover { text-decoration: underline; }
                        </style>
                    </head>
                    <body>
                        <h1>500 - 服务器错误</h1>
                        <p>读取文件时发生错误</p>
                        <p><a href="/start.html">返回首页</a></p>
                    </body>
                    </html>
                `);
                return;
            }
            
            // 设置CORS头部，允许跨域访问
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
            
            // 设置缓存控制
            if (ext === '.json' || ext === '.bin' || ext === '.wasm') {
                res.setHeader('Cache-Control', 'public, max-age=3600'); // 模型文件缓存1小时
            } else {
                res.setHeader('Cache-Control', 'no-cache'); // 其他文件不缓存
            }
            
            // 返回文件内容
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(data);
        });
    });
});

// 启动服务器
const PORT = process.env.PORT || 8000;
const HOST = 'localhost';

server.listen(PORT, HOST, () => {
    console.log('🚀 头像抠图处理系统服务器已启动');
    console.log(`📍 服务器地址: http://${HOST}:${PORT}`);
    console.log(`🌐 访问首页: http://${HOST}:${PORT}/start.html`);
    console.log('');
    console.log('📖 使用说明:');
    console.log('1. 在浏览器中访问上述地址');
    console.log('2. 按 Ctrl+C 停止服务器');
    console.log('');
    console.log('✅ 服务器运行中...');
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭服务器...');
    server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
    });
});

// 错误处理
server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.error(`❌ 端口 ${PORT} 已被占用，请尝试其他端口`);
        console.error(`💡 解决方案: 修改 server.js 中的 PORT 变量`);
    } else {
        console.error('❌ 服务器错误:', err.message);
    }
    process.exit(1);
});
