<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Human Examples Gallery</title>
    <meta http-equiv="content-type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, shrink-to-fit=yes">
    <meta name="keywords" content="Human">
    <meta name="application-name" content="Human">
    <meta name="description" content="Human: 3D Face Detection, Body Pose, Hand & Finger Tracking, Iris Tracking, Age & Gender Prediction, Emotion Prediction & Gesture Recognition; Author: <PERSON> <https://github.com/vladmandic>">
    <meta name="msapplication-tooltip" content="Human: 3D Face Detection, Body Pose, Hand & Finger Tracking, Iris Tracking, Age & Gender Prediction, Emotion Prediction & Gesture Recognition; Author: <PERSON> <https://github.com/vladmandic>">
    <meta name="theme-color" content="#000000">
    <link rel="manifest" href="../demo/manifest.webmanifest">
    <link rel="shortcut icon" href="../favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="../assets/icon.png">
    <style>
      @font-face { font-family: 'Lato'; font-display: swap; font-style: normal; font-weight: 100; src: local('Lato'), url('../assets/lato-light.woff2') }
      html { font-family: 'Lato', 'Segoe UI'; font-size: 24px; font-variant: small-caps; }
      body { margin: 24px; background: black; color: white; overflow-x: auto; overflow-y: auto; text-align: -webkit-center; min-height: 100%; max-height: 100%; }
      ::-webkit-scrollbar { height: 8px; border: 0; border-radius: 0; }
      ::-webkit-scrollbar-thumb { background: grey }
      ::-webkit-scrollbar-track { margin: 3px; }
      .text { margin: 24px }
      .strip { display: flex; width: 100%; overflow-x: auto; overflow-y: hidden; scroll-behavior: smooth; }
      .thumb { height: 180px; margin: 2px; padding: 2px; cursor: grab; }
      .thumb:hover { filter: grayscale(1); transform: scale(1.08); transition : all 0.3s ease; }   
      .image-container { margin: 24px 3px 3px 3px; cursor: nwse-resize; }
      .image-zoomwidth { max-width: 94vw; }
      .image-zoomheight { max-height: 70vh; }
      .image-zoomfull { max-height: -webkit-fill-available; }
      .arrow { font-size: 2rem; font-weight: bolder; background: grey; border-radius: 4px; position: fixed; top: 140px; opacity: 65%; cursor: pointer; }
      .arrow:hover { background: white; color: grey }
    </style>
  </head>
  <body>
    <div class="text">Human Examples Gallery</div>
    <div id="strip" class="strip"></div>
    <div class="image-container">
      <img id="image" src="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=" alt="" class="image-zoomwidth"></img>
      <div class="arrow" id="arrow-left" style="left: 10px">&lt;</div>
      <div class="arrow" id="arrow-right" style="right: 10px">&gt;</div>
    </div>
    <script>
      const samples = [
        'ai-body.jpg',
        'ai-face.jpg',
        'ai-upper.jpg',
        'person-celeste.jpg',
        'person-christina.jpg',
        'person-lauren.jpg',
        'person-lexi.jpg',
        'person-linda.jpg',
        'person-nicole.jpg',
        'person-tasia.jpg',
        'person-tetiana.jpg',
        'person-vlado.jpg',
        'person-vlado1.jpg',
        'person-vlado5.jpg',
        'group-1.jpg',
        'group-2.jpg',
        'group-3.jpg',
        'group-4.jpg',
        'group-5.jpg',
        'group-6.jpg',
        'group-7.jpg',
        'stock-emotions-a-1.jpg',
        'stock-emotions-a-2.jpg',
        'stock-emotions-a-3.jpg',
        'stock-emotions-a-4.jpg',
        'stock-emotions-a-5.jpg',
        'stock-emotions-a-6.jpg',
        'stock-emotions-a-7.jpg',
        'stock-emotions-a-8.jpg',
        'stock-emotions-b-1.jpg',
        'stock-emotions-b-2.jpg',
        'stock-emotions-b-3.jpg',
        'stock-emotions-b-4.jpg',
        'stock-emotions-b-5.jpg',
        'stock-emotions-b-6.jpg',
        'stock-emotions-b-7.jpg',
        'stock-emotions-b-8.jpg',
        'stock-group-1.jpg',
        'stock-group-2.jpg',
        'stock-models-1.jpg',
        'stock-models-10.jpg',
        'stock-models-11.jpg',
        'stock-models-12.jpg',
        'stock-models-13.jpg',
        'stock-models-14.jpg',
        'stock-models-15.jpg',
        'stock-models-16.jpg',
        'stock-models-2.jpg',
        'stock-models-3.jpg',
        'stock-models-4.jpg',
        'stock-models-5.jpg',
        'stock-models-6.jpg',
        'stock-models-7.jpg',
        'stock-models-8.jpg',
        'stock-models-9.jpg',
        'stock-teen-1.jpg',
        'stock-teen-2.jpg',
        'stock-teen-3.jpg',
        'stock-teen-4.jpg',
        'stock-teen-5.jpg',
        'stock-teen-6.jpg',
        'stock-teen-7.jpg',
        'stock-teen-8.jpg',
        'solvay1927.jpg',
        'cgi-model-1.jpg',
        'cgi-model-10.jpg',
        'cgi-model-11.jpg',
        'cgi-model-12.jpg',
        'cgi-model-13.jpg',
        'cgi-model-14.jpg',
        'cgi-model-15.jpg',
        'cgi-model-18.jpg',
        'cgi-model-19.jpg',
        'cgi-model-2.jpg',
        'cgi-model-20.jpg',
        'cgi-model-21.jpg',
        'cgi-model-22.jpg',
        'cgi-model-23.jpg',
        'cgi-model-24.jpg',
        'cgi-model-25.jpg',
        'cgi-model-26.jpg',
        'cgi-model-27.jpg',
        'cgi-model-28.jpg',
        'cgi-model-29.jpg',
        'cgi-model-3.jpg',
        'cgi-model-30.jpg',
        'cgi-model-31.jpg',
        'cgi-model-33.jpg',
        'cgi-model-34.jpg',
        'cgi-model-4.jpg',
        'cgi-model-5.jpg',
        'cgi-model-6.jpg',
        'cgi-model-7.jpg',
        'cgi-model-8.jpg',
        'cgi-model-9.jpg',
        'cgi-multiangle-1.jpg',
        'cgi-multiangle-10.jpg',
        'cgi-multiangle-11.jpg',
        'cgi-multiangle-2.jpg',
        'cgi-multiangle-3.jpg',
        'cgi-multiangle-4.jpg',
        'cgi-multiangle-6.jpg',
        'cgi-multiangle-7.jpg',
        'cgi-multiangle-8.jpg',
        'cgi-multiangle-9.jpg',
      ];
      const image = document.getElementById('image');
      image.addEventListener('click', () => {
        if (image.classList.contains('image-zoomwidth')) image.className = 'image-zoomheight';
        else if (image.classList.contains('image-zoomheight')) image.className = 'image-zoomfull';
        else if (image.classList.contains('image-zoomfull')) image.className = 'image-zoomwidth';
      });
      const strip = document.getElementById('strip');
      document.getElementById('arrow-left').addEventListener('click', () => strip.scrollLeft -= strip.offsetWidth);
      document.getElementById('arrow-right').addEventListener('click', () => strip.scrollLeft += strip.offsetWidth);
      for (const sample of samples) {
        const el = document.createElement('img');
        el.className = 'thumb';
        el.src = `./in/${sample}`;
        el.title = el.src;
        el.alt = el.src;
        el.addEventListener('click', (evt) => {
          image.src = el.src.replace('/in/', '/out/');
          image.alt = image.src;
          image.title = image.src;
          strip.scrollLeft = evt.target.offsetLeft - window.innerWidth / 2 + evt.target.offsetWidth / 2;
        });
        strip.appendChild(el);
      }
    </script> 
  </body>
</html>
