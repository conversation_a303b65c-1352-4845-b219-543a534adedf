<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form_face_recognition" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.hr.attendance.face.recognition</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="99"/>
        <field name="inherit_id" ref="hr_attendance.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@data-key='hr_attendance']" position="inside">
                <div class="row mt16 o_settings_container">
                    <div class="col-12 col-lg-12 o_setting_box" title="仅当网络摄像头创建快照时签入或签出用户">
                        <div style="width: 94px;float: left;">
                            <field name="face_recognition_pro_access"/>
                        </div>
                        <div style="margin-left: 94px;border-left: 1px solid #dee2e6;padding-left: 12px;">
                            <span class="o_form_label">人脸识别认证</span>
                            <div class="text-muted">
                                        使用人脸识别认证签入或签出
                            </div>
                        </div>

                        <div style="width: 94px;float: left;" attrs="{'invisible': [('face_recognition_pro_access', '=', False)]}">
                            <field name="face_recognition_pro_scale_recognition"/>
                        </div>
                        <div style="margin-left: 94px;border-left: 1px solid #dee2e6;padding-left: 12px;" attrs="{'invisible': [('face_recognition_pro_access', '=', False)]}">
                            <span class="o_form_label">相似面比例</span>
                            <div class="text-muted">
                                       0-100, 55 - 标准值，更严格的匹配请设置大于55的值
                            </div>
                        </div>
                        <div style="width: 94px;float: left;" attrs="{'invisible': [('face_recognition_pro_access', '=', False)]}">
                            <field name="face_recognition_pro_backend" style="width: 94px;float: left;"/>
                        </div>
                        <div style="margin-left: 94px;border-left: 1px solid #dee2e6;padding-left: 12px;" attrs="{'invisible': [('face_recognition_pro_access', '=', False)]}">
                            <span class="o_form_label">后端（引擎）面部记录</span>
                            <div class="text-muted">
                                        默认humangl引擎
                            </div>
                        </div>
                        <div style="width: 94px;float: left;" attrs="{'invisible': [('face_recognition_pro_access', '=', False)]}">
                            <field name="face_recognition_pro_store"/>
                        </div>
                        <div style="margin-left: 94px;border-left: 1px solid #dee2e6;padding-left: 12px;" attrs="{'invisible': [('face_recognition_pro_access', '=', False)]}">
                            <span class="o_form_label">面部控制</span>
                            <div class="text-muted">
                                        在数据库中存储员工签入或签出时的快照和描述符，用于可视化控制，占用大量服务器空间
                            </div>
                        </div>

                        <div style="width: 94px;float: left;" attrs="{'invisible': [('face_recognition_pro_access', '=', False)]}">
                            <field name="face_recognition_pro_kiosk_auto"/>
                        </div>
                        <div style="margin-left: 94px;border-left: 1px solid #dee2e6;padding-left: 12px;" attrs="{'invisible': [('face_recognition_pro_access', '=', False)]}">
                            <span class="o_form_label">Kiosk终端模式</span>
                            <div class="text-muted">
                                        当在自助服务终端kiosk mode模式下找到用户面部时，自动签入或签出
                            </div>
                        </div>

                        <div style="width: 94px;float: left;" attrs="{'invisible': [('face_recognition_pro_access', '=', False)]}">
                            <field name="face_recognition_pro_timeout"/>
                        </div>
                        <div style="margin-left: 94px;border-left: 1px solid #dee2e6;padding-left: 12px;" attrs="{'invisible': [('face_recognition_pro_access', '=', False)]}">
                            <span class="o_form_label">人脸识别超时设置</span>
                            <div class="text-muted">
                                        0-禁用，50-推荐，超时后暂停识别并提示用户
                            </div>
                        </div>

                        <div style="width: 94px;float: left;" attrs="{'invisible': [('face_recognition_pro_access', '=', False)]}">
                            <field name="face_recognition_pro_photo_check"/>
                        </div>
                        <div style="margin-left: 94px;border-left: 1px solid #dee2e6;padding-left: 12px;" attrs="{'invisible': [('face_recognition_pro_access', '=', False)]}">
                            <span class="o_form_label">活体检测</span>
                            <div class="text-muted">
                                        仅限真人签到，禁止照片
                            </div>
                        </div>
                        <div style="width: 94px;float: left;" attrs="{'invisible': ['|',('face_recognition_pro_access', '=', False),('face_recognition_pro_photo_check', '=', False)]}">
                            <field name="face_recognition_pro_scale_spoofing"/>
                        </div>
                        <div style="margin-left: 94px;border-left: 1px solid #dee2e6;padding-left: 12px;" attrs="{'invisible': ['|',('face_recognition_pro_access', '=', False),('face_recognition_pro_photo_check', '=', False)]}">
                            <span class="o_form_label">反欺骗规模</span>
                            <div class="text-muted">
                                        0-100, 100 - 100% 真人, 0 - 关闭
                            </div>
                        </div>

                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
